--- #################### 注册中心 + 配置中心相关配置 ####################

spring:
  cloud:
    nacos:
      server-addr: 172.16.116.151:8848 # Nacos 服务器地址
      username: nacos
      password: 123456
      discovery: # 【配置中心】配置项
        namespace: dev # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        metadata:
          version: 1.0.0 # 服务实例的版本号，可用于灰度发布
      config: # 【注册中心】配置项
        namespace: dev # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP

--- #################### 数据库相关配置 ####################
spring:
  # 数据源配置项
  autoconfigure:
    exclude:
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: ************************************************************************************************************************************************************************ # MySQL Connector/J 8.X 连接的示例
          username: tianxing
          password: SsP4@2025
        slave: # 模拟从库，可根据自己需要修改 # 模拟从库，可根据自己需要修改
          lazy: true # 开启懒加载，保证启动速度
          url: ************************************************************************************************************************************************************************ # MySQL Connector/J 8.X 连接的示例
          username: tianxing
          password: SsP4@2025

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  data:
    redis:
      host: ************** # 地址
      port: 6379 # 端口
      database: 1 # 数据库索引
      password: 123456 # 密码，建议生产环境开启

--- #################### MQ 消息队列相关配置 ####################

--- #################### 定时任务相关配置 ####################

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]

--- #################### 芋道相关配置 ####################

# 芋道配置项，设置当前项目所有自定义的配置
yudao:
  pay:
    order-notify-url: http://yunai.natapp1.cc/admin-api/pay/notify/order # 支付渠道的【支付】回调地址
    refund-notify-url: http://yunai.natapp1.cc/admin-api/pay/notify/refund # 支付渠道的【退款】回调地址
    transfer-notify-url: http://yunai.natapp1.cc/admin-api/pay/notify/transfer # 支付渠道的【转账】回调地址
  demo: false # 关闭演示模式
xxl:
  job:
    enabled: false # 是否开启调度中心，默认为 true 开启
    admin:
      addresses: http://**************:8080/xxl-job-admin # 调度中心部署跟地址



# 易宝支付配置
yeepay:
  # 应用标识（从易宝商户后台获取）
  app-key: ${YEEPAY_APP_KEY:app_10090418185}
  # 商户号
  merchant-no: ${YEEPAY_MERCHANT_NO:10090418185}
  # 父商户号
  parent-merchant-no: ${YEEPAY_PARENT_MERCHANT_NO:10090418185}
  # RSA私钥（从易宝商户后台获取）
  private-key: ${YEEPAY_PRIVATE_KEY:MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCttinpI6dhaVetyV97Hw13ULTNQ2TmZ2lxjVsl13ilF1Q2Ih38n09/d13x/b7cdZj1e+Robn+HmxOKzVXGV4OKhx50Y9dQHxGMl5e9Eka1z1pr/8CN0b/Ptd074Oe01hPTSmlMXnqQPEs0aMrnfmB3ecbutJsDyQs20Txld3q3aI5sudLLhjRRbDlMyg5hoL8xNgPxx94RqrLg8K/Tych5DRpVvTSqfEALonF/dcGlXiztc5Gh1+1Zxw2EIHofVkpr/PAoiobFqgJb2x7V8MBxgLsRNOpCCu2x5/SWWjV+p6L9YDWtQEH5xjG/vnV9t6PEr+ideX5tgPrJljo/6vr3AgMBAAECggEAATm3bU6ewAAslIGcK9PZ63flSUwVanlbnr0e4dMgUGyBX+m5CRmH8l/sM2h/2ob12kgnzK4i4NkGN+905cj47kGVGcBuj2vOBY44OCbohXXTWc8HE7kMFQdfv9a5Vmbq4uMJKncW1EJTTCkFGBb7JTygW6S93Iv5iUchREdQf/oOMbQi5ynI8LamyNMkTIFh5VxPY21Clr7vzDYXJ/giTzt7LYPLU0tS0IR3HiXiwRqCGsBlZH7Wn6TlDL2rnyJTepso4jAOjrOZvR8DkrAxT+FfTPljPX1ZQFq8k0LDHs+k0MJ2wT8GTK+fOEryLz8c/b31dth64QBbRsnfGFvP4QKBgQC+7Q0IaWL7d4BA/gQAu8ajN4HZBX0CNbwzEphtt5bM7GOoq/xh8GHdh4x3JucR4cK+l7MlgUj5RHCsI9hCVp+E/fCV0aY2qczMwTc+ND9Oltgk5pheGOrmqf+CV7TxTd0L71/LZ9zihCV16bEMxfIMa98g0/Ch7qZB0F6qU07uRwKBgQDo6xjMP20tgEL/DsSxX4W5AMm3hvkVz6S9L6z1HGhaldJgUM4zZKgTPQ4+TDBsxU64BQBjgy0Qm/jJrQi1lc0iToCLy/Ffvx86ANhHyYF0xFDQqw+Z4wf993zhW5726DNy1weDWUJm0WczHcI1fCzRwGM3MRwd7650yVTJ78x10QKBgQCzNvZN2AamfwSvfQCq45fQK1FCMimmgmQXGLRmALeXwajGcjHkiOyEMjFCwwyRN3ZCy0jIwrOJ75lHrBnaI093WZqUNwerNfXcmN1PEWH/Vg6rjJynbwJH5HjHk6Zv/yOniVlFKEgpg9tZ9DNSBxhuWm9GdEri5Y3AJ2REwxBLDwKBgQCiBQeW7HfxssrnyUohaCUI7/dhZs5V0b7yqvtQ9ZXRjW5t+Ue2kEoqN8p0o2L8BrQfZbtDfbgXWafSAj+QsMelGZ5poFAjHjuGiZfpN86JfxYIUTXlAfxJOaLw0F7fud4wAEwFnk2GYBu9fvHdaqdysLVTayH34QYlzvbgEaLUQQKBgGqzPyzJKcoE/6HE+qBPV7uEQXBjSLhL7WzlTozMLMaOugLWQpsaaH5OKPxN/8hPAXTZvOoxaIUTZwtahb1e4ZCU4cRW1RpsSUK/QHv0lPxhFhOHvNg6TBhWI0dECNEJuwekjVJUWR0gKAPFNFyLrJc2CC0Mrwf1/fmqzUAd6rjS}
  # 易宝公钥（从易宝商户后台获取）
  yeepay-public-key: ${YEEPAY_PUBLIC_KEY:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6p0XWjscY+gsyqKRhw9MeLsEmhFdBRhT2emOck/F1Omw38ZWhJxh9kDfs5HzFJMrVozgU+SJFDONxs8UB0wMILKRmqfLcfClG9MyCNuJkkfm0HFQv1hRGdOvZPXj3Bckuwa7FrEXBRYUhK7vJ40afumspthmse6bs6mZxNn/mALZ2X07uznOrrc2rk41Y2HftduxZw6T4EmtWuN2x4CZ8gwSyPAW5ZzZJLQ6tZDojBK4GZTAGhnn3bg5bBsBlw2+FLkCQBuDsJVsFPiGh/b6K/+zGTvWyUcu+LUj2MejYQELDO3i2vQXVDk7lVi2/TcUYefvIcssnzsfCfjaorxsuwIDAQAB}
  # 支付回调地址
  notify-url: ${PAY_NOTIFY_URL:http://localhost:8080/pay/yeepay/notify}
  # 退款回调地址
  refund-notify-url: ${REFUND_NOTIFY_URL:http://localhost:8080/pay/yeepay/refund-notify}
  # 是否沙箱环境
  sandbox: ${YEEPAY_SANDBOX:true}
  # 连接超时时间(毫秒)
  connect-timeout: ${YEEPAY_CONNECT_TIMEOUT:15000}  # 增加超时时间到15秒
  # 读取超时时间(毫秒)
  read-timeout: ${YEEPAY_READ_TIMEOUT:30000}