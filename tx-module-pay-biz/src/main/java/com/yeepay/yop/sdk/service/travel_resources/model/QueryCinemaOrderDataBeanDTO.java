/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.travel_resources.model.QueryCinemaOrderCouponBeanDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 电影票订单详细信息
 */
public class QueryCinemaOrderDataBeanDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;pre&gt;票总价&lt;/pre&gt;
   */
  @JsonProperty("totalPrice")
  private BigDecimal totalPrice = null;

  /**
   * &lt;pre&gt;seatsDesc&lt;/pre&gt;
   */
  @JsonProperty("seatsDesc")
  private String seatsDesc = null;

  /**
   * &lt;pre&gt;影院的详细地址&lt;/pre&gt;
   */
  @JsonProperty("cinemaAddr")
  private String cinemaAddr = null;

  /**
   * &lt;pre&gt;用户能否取消订单&lt;/pre&gt;
   */
  @JsonProperty("canUserCancel")
  private Boolean canUserCancel = null;

  /**
   * &lt;pre&gt;影片语言&lt;/pre&gt;
   */
  @JsonProperty("language")
  private String language = null;

  /**
   * &lt;pre&gt;影片类型&lt;/pre&gt;
   */
  @JsonProperty("versionTypes")
  private String versionTypes = null;

  /**
   * &lt;pre&gt;城市id&lt;/pre&gt;
   */
  @JsonProperty("cityId")
  private Integer cityId = null;

  /**
   * &lt;pre&gt;电影海报URL地址&lt;/pre&gt;
   */
  @JsonProperty("pic")
  private String pic = null;

  /**
   * &lt;pre&gt;市场价格&lt;/pre&gt;
   */
  @JsonProperty("marketUnitPrice")
  private BigDecimal marketUnitPrice = null;

  /**
   * &lt;pre&gt;播放时长&lt;/pre&gt;
   */
  @JsonProperty("duration")
  private Integer duration = null;

  /**
   * &lt;pre&gt;场次id&lt;/pre&gt;
   */
  @JsonProperty("showId")
  private Integer showId = null;

  /**
   * &lt;pre&gt;券码数组&lt;/pre&gt;
   */
  @JsonProperty("coupons")
  private List<QueryCinemaOrderCouponBeanDTO> coupons = null;

  /**
   * &lt;pre&gt;影院id&lt;/pre&gt;
   */
  @JsonProperty("cinemaId")
  private Integer cinemaId = null;

  /**
   * &lt;pre&gt;影院名称&lt;/pre&gt;
   */
  @JsonProperty("cinemaName")
  private String cinemaName = null;

  /**
   * &lt;pre&gt;场次结束时间&lt;/pre&gt;
   */
  @JsonProperty("showEndTime")
  private String showEndTime = null;

  /**
   * &lt;pre&gt;票单价&lt;/pre&gt;
   */
  @JsonProperty("unitPrice")
  private BigDecimal unitPrice = null;

  /**
   * &lt;pre&gt;子平台id&lt;/pre&gt;
   */
  @JsonProperty("subPlatformId")
  private Integer subPlatformId = null;

  /**
   * &lt;pre&gt;订单号&lt;/pre&gt;
   */
  @JsonProperty("orderNo")
  private String orderNo = null;

  /**
   * &lt;pre&gt;距离开场时间&lt;/pre&gt;
   */
  @JsonProperty("distanceToShow")
  private String distanceToShow = null;

  /**
   * &lt;pre&gt;座位数量&lt;/pre&gt;
   */
  @JsonProperty("seatsCount")
  private Integer seatsCount = null;

  /**
   * &lt;pre&gt;场次开始时间&lt;/pre&gt;
   */
  @JsonProperty("showTime")
  private String showTime = null;

  /**
   * &lt;pre&gt;平台id&lt;/pre&gt;
   */
  @JsonProperty("platformId")
  private Integer platformId = null;

  /**
   * &lt;pre&gt;userName&lt;/pre&gt;
   */
  @JsonProperty("userName")
  private String userName = null;

  /**
   * &lt;pre&gt;是否接受调坐&lt;/pre&gt;
   */
  @JsonProperty("acceptAdjust")
  private Boolean acceptAdjust = null;

  /**
   * &lt;pre&gt;用户id&lt;/pre&gt;
   */
  @JsonProperty("userId")
  private Integer userId = null;

  /**
   * &lt;pre&gt;用户备注&lt;/pre&gt;
   */
  @JsonProperty("userRemark")
  private String userRemark = null;

  /**
   * &lt;pre&gt;0:竞价出票(折扣出票) 5:快速出票(非折扣出票)&lt;/pre&gt;
   */
  @JsonProperty("drawMode")
  private Integer drawMode = null;

  /**
   * &lt;pre&gt;区域id&lt;/pre&gt;
   */
  @JsonProperty("regionId")
  private Integer regionId = null;

  /**
   * &lt;pre&gt;影厅id&lt;/pre&gt;
   */
  @JsonProperty("hallId")
  private Integer hallId = null;

  /**
   * &lt;pre&gt;电影id&lt;/pre&gt;
   */
  @JsonProperty("filmId")
  private Integer filmId = null;

  /**
   * &lt;pre&gt;影片名称&lt;/pre&gt;
   */
  @JsonProperty("filmName")
  private String filmName = null;

  /**
   * &lt;pre&gt;电影的城市&lt;/pre&gt;
   */
  @JsonProperty("cinemaCity")
  private String cinemaCity = null;

  /**
   * &lt;pre&gt;影厅名称&lt;/pre&gt;
   */
  @JsonProperty("hallName")
  private String hallName = null;

  /**
   * &lt;pre&gt;平台唯一用户标识&lt;/pre&gt;
   */
  @JsonProperty("platformUniqueId")
  private String platformUniqueId = null;

  /**
   * &lt;p&gt;佣金&lt;/p&gt;
   */
  @JsonProperty("commissionPrice")
  private BigDecimal commissionPrice = null;

  /**
   * &lt;p&gt;资源方创建订单时间&lt;/p&gt;
   */
  @JsonProperty("createTime")
  private String createTime = null;

  /**
   * &lt;p&gt;资源方更新订单时间&lt;/p&gt;
   */
  @JsonProperty("updateTime")
  private String updateTime = null;

  public QueryCinemaOrderDataBeanDTO totalPrice(BigDecimal totalPrice) {
    this.totalPrice = totalPrice;
    return this;
  }

   /**
   * &lt;pre&gt;票总价&lt;/pre&gt;
   * @return totalPrice
  **/

  public BigDecimal getTotalPrice() {
    return totalPrice;
  }

  public void setTotalPrice(BigDecimal totalPrice) {
    this.totalPrice = totalPrice;
  }

  public QueryCinemaOrderDataBeanDTO seatsDesc(String seatsDesc) {
    this.seatsDesc = seatsDesc;
    return this;
  }

   /**
   * &lt;pre&gt;seatsDesc&lt;/pre&gt;
   * @return seatsDesc
  **/

  public String getSeatsDesc() {
    return seatsDesc;
  }

  public void setSeatsDesc(String seatsDesc) {
    this.seatsDesc = seatsDesc;
  }

  public QueryCinemaOrderDataBeanDTO cinemaAddr(String cinemaAddr) {
    this.cinemaAddr = cinemaAddr;
    return this;
  }

   /**
   * &lt;pre&gt;影院的详细地址&lt;/pre&gt;
   * @return cinemaAddr
  **/

  public String getCinemaAddr() {
    return cinemaAddr;
  }

  public void setCinemaAddr(String cinemaAddr) {
    this.cinemaAddr = cinemaAddr;
  }

  public QueryCinemaOrderDataBeanDTO canUserCancel(Boolean canUserCancel) {
    this.canUserCancel = canUserCancel;
    return this;
  }

   /**
   * &lt;pre&gt;用户能否取消订单&lt;/pre&gt;
   * @return canUserCancel
  **/

  public Boolean isCanUserCancel() {
    return canUserCancel;
  }

  public void setCanUserCancel(Boolean canUserCancel) {
    this.canUserCancel = canUserCancel;
  }

  public QueryCinemaOrderDataBeanDTO language(String language) {
    this.language = language;
    return this;
  }

   /**
   * &lt;pre&gt;影片语言&lt;/pre&gt;
   * @return language
  **/

  public String getLanguage() {
    return language;
  }

  public void setLanguage(String language) {
    this.language = language;
  }

  public QueryCinemaOrderDataBeanDTO versionTypes(String versionTypes) {
    this.versionTypes = versionTypes;
    return this;
  }

   /**
   * &lt;pre&gt;影片类型&lt;/pre&gt;
   * @return versionTypes
  **/

  public String getVersionTypes() {
    return versionTypes;
  }

  public void setVersionTypes(String versionTypes) {
    this.versionTypes = versionTypes;
  }

  public QueryCinemaOrderDataBeanDTO cityId(Integer cityId) {
    this.cityId = cityId;
    return this;
  }

   /**
   * &lt;pre&gt;城市id&lt;/pre&gt;
   * @return cityId
  **/

  public Integer getCityId() {
    return cityId;
  }

  public void setCityId(Integer cityId) {
    this.cityId = cityId;
  }

  public QueryCinemaOrderDataBeanDTO pic(String pic) {
    this.pic = pic;
    return this;
  }

   /**
   * &lt;pre&gt;电影海报URL地址&lt;/pre&gt;
   * @return pic
  **/

  public String getPic() {
    return pic;
  }

  public void setPic(String pic) {
    this.pic = pic;
  }

  public QueryCinemaOrderDataBeanDTO marketUnitPrice(BigDecimal marketUnitPrice) {
    this.marketUnitPrice = marketUnitPrice;
    return this;
  }

   /**
   * &lt;pre&gt;市场价格&lt;/pre&gt;
   * @return marketUnitPrice
  **/

  public BigDecimal getMarketUnitPrice() {
    return marketUnitPrice;
  }

  public void setMarketUnitPrice(BigDecimal marketUnitPrice) {
    this.marketUnitPrice = marketUnitPrice;
  }

  public QueryCinemaOrderDataBeanDTO duration(Integer duration) {
    this.duration = duration;
    return this;
  }

   /**
   * &lt;pre&gt;播放时长&lt;/pre&gt;
   * @return duration
  **/

  public Integer getDuration() {
    return duration;
  }

  public void setDuration(Integer duration) {
    this.duration = duration;
  }

  public QueryCinemaOrderDataBeanDTO showId(Integer showId) {
    this.showId = showId;
    return this;
  }

   /**
   * &lt;pre&gt;场次id&lt;/pre&gt;
   * @return showId
  **/

  public Integer getShowId() {
    return showId;
  }

  public void setShowId(Integer showId) {
    this.showId = showId;
  }

  public QueryCinemaOrderDataBeanDTO coupons(List<QueryCinemaOrderCouponBeanDTO> coupons) {
    this.coupons = coupons;
    return this;
  }

  public QueryCinemaOrderDataBeanDTO addCouponsItem(QueryCinemaOrderCouponBeanDTO couponsItem) {
    if (this.coupons == null) {
      this.coupons = new ArrayList<>();
    }
    this.coupons.add(couponsItem);
    return this;
  }

   /**
   * &lt;pre&gt;券码数组&lt;/pre&gt;
   * @return coupons
  **/

  public List<QueryCinemaOrderCouponBeanDTO> getCoupons() {
    return coupons;
  }

  public void setCoupons(List<QueryCinemaOrderCouponBeanDTO> coupons) {
    this.coupons = coupons;
  }

  public QueryCinemaOrderDataBeanDTO cinemaId(Integer cinemaId) {
    this.cinemaId = cinemaId;
    return this;
  }

   /**
   * &lt;pre&gt;影院id&lt;/pre&gt;
   * @return cinemaId
  **/

  public Integer getCinemaId() {
    return cinemaId;
  }

  public void setCinemaId(Integer cinemaId) {
    this.cinemaId = cinemaId;
  }

  public QueryCinemaOrderDataBeanDTO cinemaName(String cinemaName) {
    this.cinemaName = cinemaName;
    return this;
  }

   /**
   * &lt;pre&gt;影院名称&lt;/pre&gt;
   * @return cinemaName
  **/

  public String getCinemaName() {
    return cinemaName;
  }

  public void setCinemaName(String cinemaName) {
    this.cinemaName = cinemaName;
  }

  public QueryCinemaOrderDataBeanDTO showEndTime(String showEndTime) {
    this.showEndTime = showEndTime;
    return this;
  }

   /**
   * &lt;pre&gt;场次结束时间&lt;/pre&gt;
   * @return showEndTime
  **/

  public String getShowEndTime() {
    return showEndTime;
  }

  public void setShowEndTime(String showEndTime) {
    this.showEndTime = showEndTime;
  }

  public QueryCinemaOrderDataBeanDTO unitPrice(BigDecimal unitPrice) {
    this.unitPrice = unitPrice;
    return this;
  }

   /**
   * &lt;pre&gt;票单价&lt;/pre&gt;
   * @return unitPrice
  **/

  public BigDecimal getUnitPrice() {
    return unitPrice;
  }

  public void setUnitPrice(BigDecimal unitPrice) {
    this.unitPrice = unitPrice;
  }

  public QueryCinemaOrderDataBeanDTO subPlatformId(Integer subPlatformId) {
    this.subPlatformId = subPlatformId;
    return this;
  }

   /**
   * &lt;pre&gt;子平台id&lt;/pre&gt;
   * @return subPlatformId
  **/

  public Integer getSubPlatformId() {
    return subPlatformId;
  }

  public void setSubPlatformId(Integer subPlatformId) {
    this.subPlatformId = subPlatformId;
  }

  public QueryCinemaOrderDataBeanDTO orderNo(String orderNo) {
    this.orderNo = orderNo;
    return this;
  }

   /**
   * &lt;pre&gt;订单号&lt;/pre&gt;
   * @return orderNo
  **/

  public String getOrderNo() {
    return orderNo;
  }

  public void setOrderNo(String orderNo) {
    this.orderNo = orderNo;
  }

  public QueryCinemaOrderDataBeanDTO distanceToShow(String distanceToShow) {
    this.distanceToShow = distanceToShow;
    return this;
  }

   /**
   * &lt;pre&gt;距离开场时间&lt;/pre&gt;
   * @return distanceToShow
  **/

  public String getDistanceToShow() {
    return distanceToShow;
  }

  public void setDistanceToShow(String distanceToShow) {
    this.distanceToShow = distanceToShow;
  }

  public QueryCinemaOrderDataBeanDTO seatsCount(Integer seatsCount) {
    this.seatsCount = seatsCount;
    return this;
  }

   /**
   * &lt;pre&gt;座位数量&lt;/pre&gt;
   * @return seatsCount
  **/

  public Integer getSeatsCount() {
    return seatsCount;
  }

  public void setSeatsCount(Integer seatsCount) {
    this.seatsCount = seatsCount;
  }

  public QueryCinemaOrderDataBeanDTO showTime(String showTime) {
    this.showTime = showTime;
    return this;
  }

   /**
   * &lt;pre&gt;场次开始时间&lt;/pre&gt;
   * @return showTime
  **/

  public String getShowTime() {
    return showTime;
  }

  public void setShowTime(String showTime) {
    this.showTime = showTime;
  }

  public QueryCinemaOrderDataBeanDTO platformId(Integer platformId) {
    this.platformId = platformId;
    return this;
  }

   /**
   * &lt;pre&gt;平台id&lt;/pre&gt;
   * @return platformId
  **/

  public Integer getPlatformId() {
    return platformId;
  }

  public void setPlatformId(Integer platformId) {
    this.platformId = platformId;
  }

  public QueryCinemaOrderDataBeanDTO userName(String userName) {
    this.userName = userName;
    return this;
  }

   /**
   * &lt;pre&gt;userName&lt;/pre&gt;
   * @return userName
  **/

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public QueryCinemaOrderDataBeanDTO acceptAdjust(Boolean acceptAdjust) {
    this.acceptAdjust = acceptAdjust;
    return this;
  }

   /**
   * &lt;pre&gt;是否接受调坐&lt;/pre&gt;
   * @return acceptAdjust
  **/

  public Boolean isAcceptAdjust() {
    return acceptAdjust;
  }

  public void setAcceptAdjust(Boolean acceptAdjust) {
    this.acceptAdjust = acceptAdjust;
  }

  public QueryCinemaOrderDataBeanDTO userId(Integer userId) {
    this.userId = userId;
    return this;
  }

   /**
   * &lt;pre&gt;用户id&lt;/pre&gt;
   * @return userId
  **/

  public Integer getUserId() {
    return userId;
  }

  public void setUserId(Integer userId) {
    this.userId = userId;
  }

  public QueryCinemaOrderDataBeanDTO userRemark(String userRemark) {
    this.userRemark = userRemark;
    return this;
  }

   /**
   * &lt;pre&gt;用户备注&lt;/pre&gt;
   * @return userRemark
  **/

  public String getUserRemark() {
    return userRemark;
  }

  public void setUserRemark(String userRemark) {
    this.userRemark = userRemark;
  }

  public QueryCinemaOrderDataBeanDTO drawMode(Integer drawMode) {
    this.drawMode = drawMode;
    return this;
  }

   /**
   * &lt;pre&gt;0:竞价出票(折扣出票) 5:快速出票(非折扣出票)&lt;/pre&gt;
   * @return drawMode
  **/

  public Integer getDrawMode() {
    return drawMode;
  }

  public void setDrawMode(Integer drawMode) {
    this.drawMode = drawMode;
  }

  public QueryCinemaOrderDataBeanDTO regionId(Integer regionId) {
    this.regionId = regionId;
    return this;
  }

   /**
   * &lt;pre&gt;区域id&lt;/pre&gt;
   * @return regionId
  **/

  public Integer getRegionId() {
    return regionId;
  }

  public void setRegionId(Integer regionId) {
    this.regionId = regionId;
  }

  public QueryCinemaOrderDataBeanDTO hallId(Integer hallId) {
    this.hallId = hallId;
    return this;
  }

   /**
   * &lt;pre&gt;影厅id&lt;/pre&gt;
   * @return hallId
  **/

  public Integer getHallId() {
    return hallId;
  }

  public void setHallId(Integer hallId) {
    this.hallId = hallId;
  }

  public QueryCinemaOrderDataBeanDTO filmId(Integer filmId) {
    this.filmId = filmId;
    return this;
  }

   /**
   * &lt;pre&gt;电影id&lt;/pre&gt;
   * @return filmId
  **/

  public Integer getFilmId() {
    return filmId;
  }

  public void setFilmId(Integer filmId) {
    this.filmId = filmId;
  }

  public QueryCinemaOrderDataBeanDTO filmName(String filmName) {
    this.filmName = filmName;
    return this;
  }

   /**
   * &lt;pre&gt;影片名称&lt;/pre&gt;
   * @return filmName
  **/

  public String getFilmName() {
    return filmName;
  }

  public void setFilmName(String filmName) {
    this.filmName = filmName;
  }

  public QueryCinemaOrderDataBeanDTO cinemaCity(String cinemaCity) {
    this.cinemaCity = cinemaCity;
    return this;
  }

   /**
   * &lt;pre&gt;电影的城市&lt;/pre&gt;
   * @return cinemaCity
  **/

  public String getCinemaCity() {
    return cinemaCity;
  }

  public void setCinemaCity(String cinemaCity) {
    this.cinemaCity = cinemaCity;
  }

  public QueryCinemaOrderDataBeanDTO hallName(String hallName) {
    this.hallName = hallName;
    return this;
  }

   /**
   * &lt;pre&gt;影厅名称&lt;/pre&gt;
   * @return hallName
  **/

  public String getHallName() {
    return hallName;
  }

  public void setHallName(String hallName) {
    this.hallName = hallName;
  }

  public QueryCinemaOrderDataBeanDTO platformUniqueId(String platformUniqueId) {
    this.platformUniqueId = platformUniqueId;
    return this;
  }

   /**
   * &lt;pre&gt;平台唯一用户标识&lt;/pre&gt;
   * @return platformUniqueId
  **/

  public String getPlatformUniqueId() {
    return platformUniqueId;
  }

  public void setPlatformUniqueId(String platformUniqueId) {
    this.platformUniqueId = platformUniqueId;
  }

  public QueryCinemaOrderDataBeanDTO commissionPrice(BigDecimal commissionPrice) {
    this.commissionPrice = commissionPrice;
    return this;
  }

   /**
   * &lt;p&gt;佣金&lt;/p&gt;
   * @return commissionPrice
  **/

  public BigDecimal getCommissionPrice() {
    return commissionPrice;
  }

  public void setCommissionPrice(BigDecimal commissionPrice) {
    this.commissionPrice = commissionPrice;
  }

  public QueryCinemaOrderDataBeanDTO createTime(String createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * &lt;p&gt;资源方创建订单时间&lt;/p&gt;
   * @return createTime
  **/

  public String getCreateTime() {
    return createTime;
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public QueryCinemaOrderDataBeanDTO updateTime(String updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * &lt;p&gt;资源方更新订单时间&lt;/p&gt;
   * @return updateTime
  **/

  public String getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(String updateTime) {
    this.updateTime = updateTime;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QueryCinemaOrderDataBeanDTO queryCinemaOrderDataBeanDTO = (QueryCinemaOrderDataBeanDTO) o;
    return ObjectUtils.equals(this.totalPrice, queryCinemaOrderDataBeanDTO.totalPrice) &&
    ObjectUtils.equals(this.seatsDesc, queryCinemaOrderDataBeanDTO.seatsDesc) &&
    ObjectUtils.equals(this.cinemaAddr, queryCinemaOrderDataBeanDTO.cinemaAddr) &&
    ObjectUtils.equals(this.canUserCancel, queryCinemaOrderDataBeanDTO.canUserCancel) &&
    ObjectUtils.equals(this.language, queryCinemaOrderDataBeanDTO.language) &&
    ObjectUtils.equals(this.versionTypes, queryCinemaOrderDataBeanDTO.versionTypes) &&
    ObjectUtils.equals(this.cityId, queryCinemaOrderDataBeanDTO.cityId) &&
    ObjectUtils.equals(this.pic, queryCinemaOrderDataBeanDTO.pic) &&
    ObjectUtils.equals(this.marketUnitPrice, queryCinemaOrderDataBeanDTO.marketUnitPrice) &&
    ObjectUtils.equals(this.duration, queryCinemaOrderDataBeanDTO.duration) &&
    ObjectUtils.equals(this.showId, queryCinemaOrderDataBeanDTO.showId) &&
    ObjectUtils.equals(this.coupons, queryCinemaOrderDataBeanDTO.coupons) &&
    ObjectUtils.equals(this.cinemaId, queryCinemaOrderDataBeanDTO.cinemaId) &&
    ObjectUtils.equals(this.cinemaName, queryCinemaOrderDataBeanDTO.cinemaName) &&
    ObjectUtils.equals(this.showEndTime, queryCinemaOrderDataBeanDTO.showEndTime) &&
    ObjectUtils.equals(this.unitPrice, queryCinemaOrderDataBeanDTO.unitPrice) &&
    ObjectUtils.equals(this.subPlatformId, queryCinemaOrderDataBeanDTO.subPlatformId) &&
    ObjectUtils.equals(this.orderNo, queryCinemaOrderDataBeanDTO.orderNo) &&
    ObjectUtils.equals(this.distanceToShow, queryCinemaOrderDataBeanDTO.distanceToShow) &&
    ObjectUtils.equals(this.seatsCount, queryCinemaOrderDataBeanDTO.seatsCount) &&
    ObjectUtils.equals(this.showTime, queryCinemaOrderDataBeanDTO.showTime) &&
    ObjectUtils.equals(this.platformId, queryCinemaOrderDataBeanDTO.platformId) &&
    ObjectUtils.equals(this.userName, queryCinemaOrderDataBeanDTO.userName) &&
    ObjectUtils.equals(this.acceptAdjust, queryCinemaOrderDataBeanDTO.acceptAdjust) &&
    ObjectUtils.equals(this.userId, queryCinemaOrderDataBeanDTO.userId) &&
    ObjectUtils.equals(this.userRemark, queryCinemaOrderDataBeanDTO.userRemark) &&
    ObjectUtils.equals(this.drawMode, queryCinemaOrderDataBeanDTO.drawMode) &&
    ObjectUtils.equals(this.regionId, queryCinemaOrderDataBeanDTO.regionId) &&
    ObjectUtils.equals(this.hallId, queryCinemaOrderDataBeanDTO.hallId) &&
    ObjectUtils.equals(this.filmId, queryCinemaOrderDataBeanDTO.filmId) &&
    ObjectUtils.equals(this.filmName, queryCinemaOrderDataBeanDTO.filmName) &&
    ObjectUtils.equals(this.cinemaCity, queryCinemaOrderDataBeanDTO.cinemaCity) &&
    ObjectUtils.equals(this.hallName, queryCinemaOrderDataBeanDTO.hallName) &&
    ObjectUtils.equals(this.platformUniqueId, queryCinemaOrderDataBeanDTO.platformUniqueId) &&
    ObjectUtils.equals(this.commissionPrice, queryCinemaOrderDataBeanDTO.commissionPrice) &&
    ObjectUtils.equals(this.createTime, queryCinemaOrderDataBeanDTO.createTime) &&
    ObjectUtils.equals(this.updateTime, queryCinemaOrderDataBeanDTO.updateTime);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(totalPrice, seatsDesc, cinemaAddr, canUserCancel, language, versionTypes, cityId, pic, marketUnitPrice, duration, showId, coupons, cinemaId, cinemaName, showEndTime, unitPrice, subPlatformId, orderNo, distanceToShow, seatsCount, showTime, platformId, userName, acceptAdjust, userId, userRemark, drawMode, regionId, hallId, filmId, filmName, cinemaCity, hallName, platformUniqueId, commissionPrice, createTime, updateTime);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryCinemaOrderDataBeanDTO {\n");
    
    sb.append("    totalPrice: ").append(toIndentedString(totalPrice)).append("\n");
    sb.append("    seatsDesc: ").append(toIndentedString(seatsDesc)).append("\n");
    sb.append("    cinemaAddr: ").append(toIndentedString(cinemaAddr)).append("\n");
    sb.append("    canUserCancel: ").append(toIndentedString(canUserCancel)).append("\n");
    sb.append("    language: ").append(toIndentedString(language)).append("\n");
    sb.append("    versionTypes: ").append(toIndentedString(versionTypes)).append("\n");
    sb.append("    cityId: ").append(toIndentedString(cityId)).append("\n");
    sb.append("    pic: ").append(toIndentedString(pic)).append("\n");
    sb.append("    marketUnitPrice: ").append(toIndentedString(marketUnitPrice)).append("\n");
    sb.append("    duration: ").append(toIndentedString(duration)).append("\n");
    sb.append("    showId: ").append(toIndentedString(showId)).append("\n");
    sb.append("    coupons: ").append(toIndentedString(coupons)).append("\n");
    sb.append("    cinemaId: ").append(toIndentedString(cinemaId)).append("\n");
    sb.append("    cinemaName: ").append(toIndentedString(cinemaName)).append("\n");
    sb.append("    showEndTime: ").append(toIndentedString(showEndTime)).append("\n");
    sb.append("    unitPrice: ").append(toIndentedString(unitPrice)).append("\n");
    sb.append("    subPlatformId: ").append(toIndentedString(subPlatformId)).append("\n");
    sb.append("    orderNo: ").append(toIndentedString(orderNo)).append("\n");
    sb.append("    distanceToShow: ").append(toIndentedString(distanceToShow)).append("\n");
    sb.append("    seatsCount: ").append(toIndentedString(seatsCount)).append("\n");
    sb.append("    showTime: ").append(toIndentedString(showTime)).append("\n");
    sb.append("    platformId: ").append(toIndentedString(platformId)).append("\n");
    sb.append("    userName: ").append(toIndentedString(userName)).append("\n");
    sb.append("    acceptAdjust: ").append(toIndentedString(acceptAdjust)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    userRemark: ").append(toIndentedString(userRemark)).append("\n");
    sb.append("    drawMode: ").append(toIndentedString(drawMode)).append("\n");
    sb.append("    regionId: ").append(toIndentedString(regionId)).append("\n");
    sb.append("    hallId: ").append(toIndentedString(hallId)).append("\n");
    sb.append("    filmId: ").append(toIndentedString(filmId)).append("\n");
    sb.append("    filmName: ").append(toIndentedString(filmName)).append("\n");
    sb.append("    cinemaCity: ").append(toIndentedString(cinemaCity)).append("\n");
    sb.append("    hallName: ").append(toIndentedString(hallName)).append("\n");
    sb.append("    platformUniqueId: ").append(toIndentedString(platformUniqueId)).append("\n");
    sb.append("    commissionPrice: ").append(toIndentedString(commissionPrice)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

