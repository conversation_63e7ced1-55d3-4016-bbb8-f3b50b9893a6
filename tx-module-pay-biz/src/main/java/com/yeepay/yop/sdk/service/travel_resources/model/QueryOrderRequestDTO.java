/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 查询订单请求参数
 */
public class QueryOrderRequestDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;请求商编&lt;/p&gt;
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;p&gt;商户订单号&lt;/p&gt;
   */
  @JsonProperty("merchantRequestNo")
  private String merchantRequestNo = null;

  /**
   * &lt;pre&gt;请求方订单号&lt;/pre&gt;
   */
  @JsonProperty("parentMerchantRequestNo")
  private String parentMerchantRequestNo = null;

  public QueryOrderRequestDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;请求商编&lt;/p&gt;
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public QueryOrderRequestDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public QueryOrderRequestDTO merchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;商户订单号&lt;/p&gt;
   * @return merchantRequestNo
  **/

  public String getMerchantRequestNo() {
    return merchantRequestNo;
  }

  public void setMerchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
  }

  public QueryOrderRequestDTO parentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
    return this;
  }

   /**
   * &lt;pre&gt;请求方订单号&lt;/pre&gt;
   * @return parentMerchantRequestNo
  **/

  public String getParentMerchantRequestNo() {
    return parentMerchantRequestNo;
  }

  public void setParentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QueryOrderRequestDTO queryOrderRequestDTO = (QueryOrderRequestDTO) o;
    return ObjectUtils.equals(this.parentMerchantNo, queryOrderRequestDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, queryOrderRequestDTO.merchantNo) &&
    ObjectUtils.equals(this.merchantRequestNo, queryOrderRequestDTO.merchantRequestNo) &&
    ObjectUtils.equals(this.parentMerchantRequestNo, queryOrderRequestDTO.parentMerchantRequestNo);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(parentMerchantNo, merchantNo, merchantRequestNo, parentMerchantRequestNo);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryOrderRequestDTO {\n");
    
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantRequestNo: ").append(toIndentedString(merchantRequestNo)).append("\n");
    sb.append("    parentMerchantRequestNo: ").append(toIndentedString(parentMerchantRequestNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

