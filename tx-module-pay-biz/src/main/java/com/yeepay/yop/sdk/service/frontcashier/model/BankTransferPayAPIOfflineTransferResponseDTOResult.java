/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * BankTransferPayAPIOfflineTransferResponseDTOResult
 */
public class BankTransferPayAPIOfflineTransferResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("msg")
  private String msg = null;

  /**
   * 支付状态
   */
  @JsonProperty("payStatus")
  private String payStatus = null;

  /**
   * 附言
   */
  @JsonProperty("remitRemarkCode")
  private String remitRemarkCode = null;

  /**
   * 付款方名称
   */
  @JsonProperty("payerAccountName")
  private String payerAccountName = null;

  /**
   * 收款方名称
   */
  @JsonProperty("receiveName")
  private String receiveName = null;

  /**
   * 收款方账号
   */
  @JsonProperty("receiveAccountNo")
  private String receiveAccountNo = null;

  /**
   * 收款方开户行
   */
  @JsonProperty("accountName")
  private String accountName = null;

  /**
   * 省市／地区
   */
  @JsonProperty("areaInfo")
  private String areaInfo = null;

  /**
   * 转账金额
   */
  @JsonProperty("amount")
  private BigDecimal amount = null;

  /**
   * 易宝收款订单号
   */
  @JsonProperty("uniqueOrderNo")
  private String uniqueOrderNo = null;

  /**
   * 付款方账户号
   */
  @JsonProperty("payerAccountNo")
  private String payerAccountNo = null;

  public BankTransferPayAPIOfflineTransferResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BankTransferPayAPIOfflineTransferResponseDTOResult msg(String msg) {
    this.msg = msg;
    return this;
  }

   /**
   * 返回信息
   * @return msg
  **/

  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }

  public BankTransferPayAPIOfflineTransferResponseDTOResult payStatus(String payStatus) {
    this.payStatus = payStatus;
    return this;
  }

   /**
   * 支付状态
   * @return payStatus
  **/

  public String getPayStatus() {
    return payStatus;
  }

  public void setPayStatus(String payStatus) {
    this.payStatus = payStatus;
  }

  public BankTransferPayAPIOfflineTransferResponseDTOResult remitRemarkCode(String remitRemarkCode) {
    this.remitRemarkCode = remitRemarkCode;
    return this;
  }

   /**
   * 附言
   * @return remitRemarkCode
  **/

  public String getRemitRemarkCode() {
    return remitRemarkCode;
  }

  public void setRemitRemarkCode(String remitRemarkCode) {
    this.remitRemarkCode = remitRemarkCode;
  }

  public BankTransferPayAPIOfflineTransferResponseDTOResult payerAccountName(String payerAccountName) {
    this.payerAccountName = payerAccountName;
    return this;
  }

   /**
   * 付款方名称
   * @return payerAccountName
  **/

  public String getPayerAccountName() {
    return payerAccountName;
  }

  public void setPayerAccountName(String payerAccountName) {
    this.payerAccountName = payerAccountName;
  }

  public BankTransferPayAPIOfflineTransferResponseDTOResult receiveName(String receiveName) {
    this.receiveName = receiveName;
    return this;
  }

   /**
   * 收款方名称
   * @return receiveName
  **/

  public String getReceiveName() {
    return receiveName;
  }

  public void setReceiveName(String receiveName) {
    this.receiveName = receiveName;
  }

  public BankTransferPayAPIOfflineTransferResponseDTOResult receiveAccountNo(String receiveAccountNo) {
    this.receiveAccountNo = receiveAccountNo;
    return this;
  }

   /**
   * 收款方账号
   * @return receiveAccountNo
  **/

  public String getReceiveAccountNo() {
    return receiveAccountNo;
  }

  public void setReceiveAccountNo(String receiveAccountNo) {
    this.receiveAccountNo = receiveAccountNo;
  }

  public BankTransferPayAPIOfflineTransferResponseDTOResult accountName(String accountName) {
    this.accountName = accountName;
    return this;
  }

   /**
   * 收款方开户行
   * @return accountName
  **/

  public String getAccountName() {
    return accountName;
  }

  public void setAccountName(String accountName) {
    this.accountName = accountName;
  }

  public BankTransferPayAPIOfflineTransferResponseDTOResult areaInfo(String areaInfo) {
    this.areaInfo = areaInfo;
    return this;
  }

   /**
   * 省市／地区
   * @return areaInfo
  **/

  public String getAreaInfo() {
    return areaInfo;
  }

  public void setAreaInfo(String areaInfo) {
    this.areaInfo = areaInfo;
  }

  public BankTransferPayAPIOfflineTransferResponseDTOResult amount(BigDecimal amount) {
    this.amount = amount;
    return this;
  }

   /**
   * 转账金额
   * @return amount
  **/

  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public BankTransferPayAPIOfflineTransferResponseDTOResult uniqueOrderNo(String uniqueOrderNo) {
    this.uniqueOrderNo = uniqueOrderNo;
    return this;
  }

   /**
   * 易宝收款订单号
   * @return uniqueOrderNo
  **/

  public String getUniqueOrderNo() {
    return uniqueOrderNo;
  }

  public void setUniqueOrderNo(String uniqueOrderNo) {
    this.uniqueOrderNo = uniqueOrderNo;
  }

  public BankTransferPayAPIOfflineTransferResponseDTOResult payerAccountNo(String payerAccountNo) {
    this.payerAccountNo = payerAccountNo;
    return this;
  }

   /**
   * 付款方账户号
   * @return payerAccountNo
  **/

  public String getPayerAccountNo() {
    return payerAccountNo;
  }

  public void setPayerAccountNo(String payerAccountNo) {
    this.payerAccountNo = payerAccountNo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BankTransferPayAPIOfflineTransferResponseDTOResult bankTransferPayAPIOfflineTransferResponseDTOResult = (BankTransferPayAPIOfflineTransferResponseDTOResult) o;
    return ObjectUtils.equals(this.code, bankTransferPayAPIOfflineTransferResponseDTOResult.code) &&
    ObjectUtils.equals(this.msg, bankTransferPayAPIOfflineTransferResponseDTOResult.msg) &&
    ObjectUtils.equals(this.payStatus, bankTransferPayAPIOfflineTransferResponseDTOResult.payStatus) &&
    ObjectUtils.equals(this.remitRemarkCode, bankTransferPayAPIOfflineTransferResponseDTOResult.remitRemarkCode) &&
    ObjectUtils.equals(this.payerAccountName, bankTransferPayAPIOfflineTransferResponseDTOResult.payerAccountName) &&
    ObjectUtils.equals(this.receiveName, bankTransferPayAPIOfflineTransferResponseDTOResult.receiveName) &&
    ObjectUtils.equals(this.receiveAccountNo, bankTransferPayAPIOfflineTransferResponseDTOResult.receiveAccountNo) &&
    ObjectUtils.equals(this.accountName, bankTransferPayAPIOfflineTransferResponseDTOResult.accountName) &&
    ObjectUtils.equals(this.areaInfo, bankTransferPayAPIOfflineTransferResponseDTOResult.areaInfo) &&
    ObjectUtils.equals(this.amount, bankTransferPayAPIOfflineTransferResponseDTOResult.amount) &&
    ObjectUtils.equals(this.uniqueOrderNo, bankTransferPayAPIOfflineTransferResponseDTOResult.uniqueOrderNo) &&
    ObjectUtils.equals(this.payerAccountNo, bankTransferPayAPIOfflineTransferResponseDTOResult.payerAccountNo);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, msg, payStatus, remitRemarkCode, payerAccountName, receiveName, receiveAccountNo, accountName, areaInfo, amount, uniqueOrderNo, payerAccountNo);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BankTransferPayAPIOfflineTransferResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    msg: ").append(toIndentedString(msg)).append("\n");
    sb.append("    payStatus: ").append(toIndentedString(payStatus)).append("\n");
    sb.append("    remitRemarkCode: ").append(toIndentedString(remitRemarkCode)).append("\n");
    sb.append("    payerAccountName: ").append(toIndentedString(payerAccountName)).append("\n");
    sb.append("    receiveName: ").append(toIndentedString(receiveName)).append("\n");
    sb.append("    receiveAccountNo: ").append(toIndentedString(receiveAccountNo)).append("\n");
    sb.append("    accountName: ").append(toIndentedString(accountName)).append("\n");
    sb.append("    areaInfo: ").append(toIndentedString(areaInfo)).append("\n");
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("    uniqueOrderNo: ").append(toIndentedString(uniqueOrderNo)).append("\n");
    sb.append("    payerAccountNo: ").append(toIndentedString(payerAccountNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

