/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.agency_operation.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 门店绑定查询结果
 */
public class YopQueryMerchantShopBindResDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;被抽佣商编&lt;/p&gt;
   */
  @JsonProperty("contributeMerchantNo")
  private String contributeMerchantNo = null;

  /**
   * &lt;p&gt;抽佣接收方商编&lt;/p&gt;
   */
  @JsonProperty("receiveMerchantNo")
  private String receiveMerchantNo = null;

  /**
   * &lt;p&gt;网点编号&lt;/p&gt;
   */
  @JsonProperty("shopNo")
  private String shopNo = null;

  /**
   * &lt;p&gt;网点名称&lt;/p&gt;
   */
  @JsonProperty("shopName")
  private String shopName = null;

  /**
   * &lt;p&gt;渠道类型&lt;/p&gt; &lt;p&gt;MEITUAN : 美团&lt;/p&gt; &lt;p&gt;DOUYIN : 抖音&lt;/p&gt;
   */
  @JsonProperty("channelType")
  private String channelType = null;

  /**
   * &lt;p&gt;业务id&lt;/p&gt; &lt;p&gt;1：到店餐饮&lt;/p&gt; &lt;p&gt;4：到综行业&lt;/p&gt;
   */
  @JsonProperty("businessId")
  private String businessId = null;

  /**
   * &lt;p&gt;绑定状态&lt;/p&gt; &lt;p&gt;PROCESSING ： 处理中&lt;/p&gt; &lt;p&gt;FAIL ：失败&lt;/p&gt; &lt;p&gt;SUCCESS : 成功&lt;/p&gt;
   */
  @JsonProperty("bindStatus")
  private String bindStatus = null;

  public YopQueryMerchantShopBindResDTO contributeMerchantNo(String contributeMerchantNo) {
    this.contributeMerchantNo = contributeMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;被抽佣商编&lt;/p&gt;
   * @return contributeMerchantNo
  **/

  public String getContributeMerchantNo() {
    return contributeMerchantNo;
  }

  public void setContributeMerchantNo(String contributeMerchantNo) {
    this.contributeMerchantNo = contributeMerchantNo;
  }

  public YopQueryMerchantShopBindResDTO receiveMerchantNo(String receiveMerchantNo) {
    this.receiveMerchantNo = receiveMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;抽佣接收方商编&lt;/p&gt;
   * @return receiveMerchantNo
  **/

  public String getReceiveMerchantNo() {
    return receiveMerchantNo;
  }

  public void setReceiveMerchantNo(String receiveMerchantNo) {
    this.receiveMerchantNo = receiveMerchantNo;
  }

  public YopQueryMerchantShopBindResDTO shopNo(String shopNo) {
    this.shopNo = shopNo;
    return this;
  }

   /**
   * &lt;p&gt;网点编号&lt;/p&gt;
   * @return shopNo
  **/

  public String getShopNo() {
    return shopNo;
  }

  public void setShopNo(String shopNo) {
    this.shopNo = shopNo;
  }

  public YopQueryMerchantShopBindResDTO shopName(String shopName) {
    this.shopName = shopName;
    return this;
  }

   /**
   * &lt;p&gt;网点名称&lt;/p&gt;
   * @return shopName
  **/

  public String getShopName() {
    return shopName;
  }

  public void setShopName(String shopName) {
    this.shopName = shopName;
  }

  public YopQueryMerchantShopBindResDTO channelType(String channelType) {
    this.channelType = channelType;
    return this;
  }

   /**
   * &lt;p&gt;渠道类型&lt;/p&gt; &lt;p&gt;MEITUAN : 美团&lt;/p&gt; &lt;p&gt;DOUYIN : 抖音&lt;/p&gt;
   * @return channelType
  **/

  public String getChannelType() {
    return channelType;
  }

  public void setChannelType(String channelType) {
    this.channelType = channelType;
  }

  public YopQueryMerchantShopBindResDTO businessId(String businessId) {
    this.businessId = businessId;
    return this;
  }

   /**
   * &lt;p&gt;业务id&lt;/p&gt; &lt;p&gt;1：到店餐饮&lt;/p&gt; &lt;p&gt;4：到综行业&lt;/p&gt;
   * @return businessId
  **/

  public String getBusinessId() {
    return businessId;
  }

  public void setBusinessId(String businessId) {
    this.businessId = businessId;
  }

  public YopQueryMerchantShopBindResDTO bindStatus(String bindStatus) {
    this.bindStatus = bindStatus;
    return this;
  }

   /**
   * &lt;p&gt;绑定状态&lt;/p&gt; &lt;p&gt;PROCESSING ： 处理中&lt;/p&gt; &lt;p&gt;FAIL ：失败&lt;/p&gt; &lt;p&gt;SUCCESS : 成功&lt;/p&gt;
   * @return bindStatus
  **/

  public String getBindStatus() {
    return bindStatus;
  }

  public void setBindStatus(String bindStatus) {
    this.bindStatus = bindStatus;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    YopQueryMerchantShopBindResDTO yopQueryMerchantShopBindResDTO = (YopQueryMerchantShopBindResDTO) o;
    return ObjectUtils.equals(this.contributeMerchantNo, yopQueryMerchantShopBindResDTO.contributeMerchantNo) &&
    ObjectUtils.equals(this.receiveMerchantNo, yopQueryMerchantShopBindResDTO.receiveMerchantNo) &&
    ObjectUtils.equals(this.shopNo, yopQueryMerchantShopBindResDTO.shopNo) &&
    ObjectUtils.equals(this.shopName, yopQueryMerchantShopBindResDTO.shopName) &&
    ObjectUtils.equals(this.channelType, yopQueryMerchantShopBindResDTO.channelType) &&
    ObjectUtils.equals(this.businessId, yopQueryMerchantShopBindResDTO.businessId) &&
    ObjectUtils.equals(this.bindStatus, yopQueryMerchantShopBindResDTO.bindStatus);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(contributeMerchantNo, receiveMerchantNo, shopNo, shopName, channelType, businessId, bindStatus);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class YopQueryMerchantShopBindResDTO {\n");
    
    sb.append("    contributeMerchantNo: ").append(toIndentedString(contributeMerchantNo)).append("\n");
    sb.append("    receiveMerchantNo: ").append(toIndentedString(receiveMerchantNo)).append("\n");
    sb.append("    shopNo: ").append(toIndentedString(shopNo)).append("\n");
    sb.append("    shopName: ").append(toIndentedString(shopName)).append("\n");
    sb.append("    channelType: ").append(toIndentedString(channelType)).append("\n");
    sb.append("    businessId: ").append(toIndentedString(businessId)).append("\n");
    sb.append("    bindStatus: ").append(toIndentedString(bindStatus)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

