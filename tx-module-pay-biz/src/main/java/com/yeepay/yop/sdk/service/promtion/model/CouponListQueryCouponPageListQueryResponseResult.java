/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.promtion.model.CouponListQueryCouponQueryInfoResult;
import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * CouponListQueryCouponPageListQueryResponseResult
 */
public class CouponListQueryCouponPageListQueryResponseResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 响应码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 响应描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 用户标识
   */
  @JsonProperty("merchantUsrNo")
  private String merchantUsrNo = null;

  /**
   * 优惠券列表
   */
  @JsonProperty("couponInfoList")
  private List<CouponListQueryCouponQueryInfoResult> couponInfoList = null;

  public CouponListQueryCouponPageListQueryResponseResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 响应码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public CouponListQueryCouponPageListQueryResponseResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 响应描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public CouponListQueryCouponPageListQueryResponseResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public CouponListQueryCouponPageListQueryResponseResult merchantUsrNo(String merchantUsrNo) {
    this.merchantUsrNo = merchantUsrNo;
    return this;
  }

   /**
   * 用户标识
   * @return merchantUsrNo
  **/

  public String getMerchantUsrNo() {
    return merchantUsrNo;
  }

  public void setMerchantUsrNo(String merchantUsrNo) {
    this.merchantUsrNo = merchantUsrNo;
  }

  public CouponListQueryCouponPageListQueryResponseResult couponInfoList(List<CouponListQueryCouponQueryInfoResult> couponInfoList) {
    this.couponInfoList = couponInfoList;
    return this;
  }

  public CouponListQueryCouponPageListQueryResponseResult addCouponInfoListItem(CouponListQueryCouponQueryInfoResult couponInfoListItem) {
    if (this.couponInfoList == null) {
      this.couponInfoList = new ArrayList<>();
    }
    this.couponInfoList.add(couponInfoListItem);
    return this;
  }

   /**
   * 优惠券列表
   * @return couponInfoList
  **/

  public List<CouponListQueryCouponQueryInfoResult> getCouponInfoList() {
    return couponInfoList;
  }

  public void setCouponInfoList(List<CouponListQueryCouponQueryInfoResult> couponInfoList) {
    this.couponInfoList = couponInfoList;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    CouponListQueryCouponPageListQueryResponseResult couponListQueryCouponPageListQueryResponseResult = (CouponListQueryCouponPageListQueryResponseResult) o;
    return ObjectUtils.equals(this.code, couponListQueryCouponPageListQueryResponseResult.code) &&
    ObjectUtils.equals(this.message, couponListQueryCouponPageListQueryResponseResult.message) &&
    ObjectUtils.equals(this.merchantNo, couponListQueryCouponPageListQueryResponseResult.merchantNo) &&
    ObjectUtils.equals(this.merchantUsrNo, couponListQueryCouponPageListQueryResponseResult.merchantUsrNo) &&
    ObjectUtils.equals(this.couponInfoList, couponListQueryCouponPageListQueryResponseResult.couponInfoList);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, merchantNo, merchantUsrNo, couponInfoList);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CouponListQueryCouponPageListQueryResponseResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantUsrNo: ").append(toIndentedString(merchantUsrNo)).append("\n");
    sb.append("    couponInfoList: ").append(toIndentedString(couponInfoList)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

