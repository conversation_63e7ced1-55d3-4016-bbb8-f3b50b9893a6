/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.travel_resources;


import com.yeepay.yop.sdk.auth.req.AuthorizationReqRegistry;
import com.yeepay.yop.sdk.auth.req.DefaultAuthorizationReqRegistry;
import com.yeepay.yop.sdk.client.ClientParams;
import com.yeepay.yop.sdk.client.AbstractServiceClientBuilder;

public class TravelResourcesClientBuilder extends AbstractServiceClientBuilder<TravelResourcesClientBuilder, TravelResourcesClientImpl>{

    private static final AuthorizationReqRegistry REGISTRY;

    static {
        REGISTRY = new DefaultAuthorizationReqRegistry();
        REGISTRY.register("createOrder", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("createRefundOrder", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("kfcOrder", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("kfcOrder_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("queryCinemaOrder", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("queryKfcOrder", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("queryOrder", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("queryPayOrder", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("queryRefundOrder", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
    }

    @Override
    protected AuthorizationReqRegistry authorizationReqRegistry() {
        return REGISTRY;
    }

    @Override
    protected TravelResourcesClientImpl build(ClientParams params) {
        return new TravelResourcesClientImpl(params);
    }

    /**
     * @return Create new instance of builder with all defaults set.
     */
    public static TravelResourcesClientBuilder builder(){
        return new TravelResourcesClientBuilder();
    }

}
