/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * SubsidyQueryYopQuerySubsidyResDTOResult
 */
public class SubsidyQueryYopQuerySubsidyResDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 平台商商户号
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * 子商户商户号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 商户交易订单号
   */
  @JsonProperty("orderId")
  private String orderId = null;

  /**
   * 易宝交易流水号
   */
  @JsonProperty("uniqueOrderNo")
  private String uniqueOrderNo = null;

  /**
   * 易宝补贴订单号
   */
  @JsonProperty("subsidyOrderNo")
  private String subsidyOrderNo = null;

  /**
   * 处理状态
   */
  @JsonProperty("status")
  private String status = null;

  /**
   * 补贴金额
   */
  @JsonProperty("subsidyAmount")
  private String subsidyAmount = null;

  /**
   * 出资方商编
   */
  @JsonProperty("assumeMerchantNo")
  private String assumeMerchantNo = null;

  /**
   * 失败原因
   */
  @JsonProperty("failReason")
  private String failReason = null;

  /**
   * 补贴退回金额
   */
  @JsonProperty("subsidyBackAmount")
  private String subsidyBackAmount = null;

  public SubsidyQueryYopQuerySubsidyResDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public SubsidyQueryYopQuerySubsidyResDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回信息
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public SubsidyQueryYopQuerySubsidyResDTOResult parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * 平台商商户号
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public SubsidyQueryYopQuerySubsidyResDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 子商户商户号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public SubsidyQueryYopQuerySubsidyResDTOResult orderId(String orderId) {
    this.orderId = orderId;
    return this;
  }

   /**
   * 商户交易订单号
   * @return orderId
  **/

  public String getOrderId() {
    return orderId;
  }

  public void setOrderId(String orderId) {
    this.orderId = orderId;
  }

  public SubsidyQueryYopQuerySubsidyResDTOResult uniqueOrderNo(String uniqueOrderNo) {
    this.uniqueOrderNo = uniqueOrderNo;
    return this;
  }

   /**
   * 易宝交易流水号
   * @return uniqueOrderNo
  **/

  public String getUniqueOrderNo() {
    return uniqueOrderNo;
  }

  public void setUniqueOrderNo(String uniqueOrderNo) {
    this.uniqueOrderNo = uniqueOrderNo;
  }

  public SubsidyQueryYopQuerySubsidyResDTOResult subsidyOrderNo(String subsidyOrderNo) {
    this.subsidyOrderNo = subsidyOrderNo;
    return this;
  }

   /**
   * 易宝补贴订单号
   * @return subsidyOrderNo
  **/

  public String getSubsidyOrderNo() {
    return subsidyOrderNo;
  }

  public void setSubsidyOrderNo(String subsidyOrderNo) {
    this.subsidyOrderNo = subsidyOrderNo;
  }

  public SubsidyQueryYopQuerySubsidyResDTOResult status(String status) {
    this.status = status;
    return this;
  }

   /**
   * 处理状态
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public SubsidyQueryYopQuerySubsidyResDTOResult subsidyAmount(String subsidyAmount) {
    this.subsidyAmount = subsidyAmount;
    return this;
  }

   /**
   * 补贴金额
   * @return subsidyAmount
  **/

  public String getSubsidyAmount() {
    return subsidyAmount;
  }

  public void setSubsidyAmount(String subsidyAmount) {
    this.subsidyAmount = subsidyAmount;
  }

  public SubsidyQueryYopQuerySubsidyResDTOResult assumeMerchantNo(String assumeMerchantNo) {
    this.assumeMerchantNo = assumeMerchantNo;
    return this;
  }

   /**
   * 出资方商编
   * @return assumeMerchantNo
  **/

  public String getAssumeMerchantNo() {
    return assumeMerchantNo;
  }

  public void setAssumeMerchantNo(String assumeMerchantNo) {
    this.assumeMerchantNo = assumeMerchantNo;
  }

  public SubsidyQueryYopQuerySubsidyResDTOResult failReason(String failReason) {
    this.failReason = failReason;
    return this;
  }

   /**
   * 失败原因
   * @return failReason
  **/

  public String getFailReason() {
    return failReason;
  }

  public void setFailReason(String failReason) {
    this.failReason = failReason;
  }

  public SubsidyQueryYopQuerySubsidyResDTOResult subsidyBackAmount(String subsidyBackAmount) {
    this.subsidyBackAmount = subsidyBackAmount;
    return this;
  }

   /**
   * 补贴退回金额
   * @return subsidyBackAmount
  **/

  public String getSubsidyBackAmount() {
    return subsidyBackAmount;
  }

  public void setSubsidyBackAmount(String subsidyBackAmount) {
    this.subsidyBackAmount = subsidyBackAmount;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    SubsidyQueryYopQuerySubsidyResDTOResult subsidyQueryYopQuerySubsidyResDTOResult = (SubsidyQueryYopQuerySubsidyResDTOResult) o;
    return ObjectUtils.equals(this.code, subsidyQueryYopQuerySubsidyResDTOResult.code) &&
    ObjectUtils.equals(this.message, subsidyQueryYopQuerySubsidyResDTOResult.message) &&
    ObjectUtils.equals(this.parentMerchantNo, subsidyQueryYopQuerySubsidyResDTOResult.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, subsidyQueryYopQuerySubsidyResDTOResult.merchantNo) &&
    ObjectUtils.equals(this.orderId, subsidyQueryYopQuerySubsidyResDTOResult.orderId) &&
    ObjectUtils.equals(this.uniqueOrderNo, subsidyQueryYopQuerySubsidyResDTOResult.uniqueOrderNo) &&
    ObjectUtils.equals(this.subsidyOrderNo, subsidyQueryYopQuerySubsidyResDTOResult.subsidyOrderNo) &&
    ObjectUtils.equals(this.status, subsidyQueryYopQuerySubsidyResDTOResult.status) &&
    ObjectUtils.equals(this.subsidyAmount, subsidyQueryYopQuerySubsidyResDTOResult.subsidyAmount) &&
    ObjectUtils.equals(this.assumeMerchantNo, subsidyQueryYopQuerySubsidyResDTOResult.assumeMerchantNo) &&
    ObjectUtils.equals(this.failReason, subsidyQueryYopQuerySubsidyResDTOResult.failReason) &&
    ObjectUtils.equals(this.subsidyBackAmount, subsidyQueryYopQuerySubsidyResDTOResult.subsidyBackAmount);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, parentMerchantNo, merchantNo, orderId, uniqueOrderNo, subsidyOrderNo, status, subsidyAmount, assumeMerchantNo, failReason, subsidyBackAmount);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SubsidyQueryYopQuerySubsidyResDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    orderId: ").append(toIndentedString(orderId)).append("\n");
    sb.append("    uniqueOrderNo: ").append(toIndentedString(uniqueOrderNo)).append("\n");
    sb.append("    subsidyOrderNo: ").append(toIndentedString(subsidyOrderNo)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    subsidyAmount: ").append(toIndentedString(subsidyAmount)).append("\n");
    sb.append("    assumeMerchantNo: ").append(toIndentedString(assumeMerchantNo)).append("\n");
    sb.append("    failReason: ").append(toIndentedString(failReason)).append("\n");
    sb.append("    subsidyBackAmount: ").append(toIndentedString(subsidyBackAmount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

