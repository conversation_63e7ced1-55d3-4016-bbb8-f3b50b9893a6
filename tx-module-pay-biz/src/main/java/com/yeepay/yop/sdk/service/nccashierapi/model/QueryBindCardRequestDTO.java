/*
 * API收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.nccashierapi.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yeepay.yop.sdk.service.base.request.serializer.YopCustomSerializer;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 查询绑卡结果请求参数
 */
public class QueryBindCardRequestDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;收单商户编号，与绑卡时传参一致&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;p&gt;商户绑卡唯一流水号，需保证唯一，与绑卡时传参一致&lt;/p&gt;
   */
  @JsonProperty("merchantFlowId")
  private String merchantFlowId = null;

  /**
   * 
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  public QueryBindCardRequestDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;收单商户编号，与绑卡时传参一致&lt;/p&gt;
   * @return merchantNo
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public QueryBindCardRequestDTO merchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
    return this;
  }

   /**
   * &lt;p&gt;商户绑卡唯一流水号，需保证唯一，与绑卡时传参一致&lt;/p&gt;
   * @return merchantFlowId
  **/

  public String getMerchantFlowId() {
    return merchantFlowId;
  }

  public void setMerchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
  }

  public QueryBindCardRequestDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * Get parentMerchantNo
   * @return parentMerchantNo
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QueryBindCardRequestDTO queryBindCardRequestDTO = (QueryBindCardRequestDTO) o;
    return ObjectUtils.equals(this.merchantNo, queryBindCardRequestDTO.merchantNo) &&
    ObjectUtils.equals(this.merchantFlowId, queryBindCardRequestDTO.merchantFlowId) &&
    ObjectUtils.equals(this.parentMerchantNo, queryBindCardRequestDTO.parentMerchantNo);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(merchantNo, merchantFlowId, parentMerchantNo);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryBindCardRequestDTO {\n");
    
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantFlowId: ").append(toIndentedString(merchantFlowId)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

