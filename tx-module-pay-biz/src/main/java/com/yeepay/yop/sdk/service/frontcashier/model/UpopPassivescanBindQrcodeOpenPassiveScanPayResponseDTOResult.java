/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * UpopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult
 */
public class UpopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 响应码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 响应码描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 付款方订单号
   */
  @JsonProperty("payOrderNo")
  private String payOrderNo = null;

  /**
   * 二维码信息
   */
  @JsonProperty("qrCode")
  private String qrCode = null;

  /**
   * 有效期时间
   */
  @JsonProperty("orderValidTime")
  private Integer orderValidTime = null;

  /**
   * 订单创建时间
   */
  @JsonProperty("orderDate")
  private String orderDate = null;

  public UpopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 响应码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public UpopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 响应码描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public UpopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult payOrderNo(String payOrderNo) {
    this.payOrderNo = payOrderNo;
    return this;
  }

   /**
   * 付款方订单号
   * @return payOrderNo
  **/

  public String getPayOrderNo() {
    return payOrderNo;
  }

  public void setPayOrderNo(String payOrderNo) {
    this.payOrderNo = payOrderNo;
  }

  public UpopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult qrCode(String qrCode) {
    this.qrCode = qrCode;
    return this;
  }

   /**
   * 二维码信息
   * @return qrCode
  **/

  public String getQrCode() {
    return qrCode;
  }

  public void setQrCode(String qrCode) {
    this.qrCode = qrCode;
  }

  public UpopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult orderValidTime(Integer orderValidTime) {
    this.orderValidTime = orderValidTime;
    return this;
  }

   /**
   * 有效期时间
   * @return orderValidTime
  **/

  public Integer getOrderValidTime() {
    return orderValidTime;
  }

  public void setOrderValidTime(Integer orderValidTime) {
    this.orderValidTime = orderValidTime;
  }

  public UpopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult orderDate(String orderDate) {
    this.orderDate = orderDate;
    return this;
  }

   /**
   * 订单创建时间
   * @return orderDate
  **/

  public String getOrderDate() {
    return orderDate;
  }

  public void setOrderDate(String orderDate) {
    this.orderDate = orderDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    UpopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult upopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult = (UpopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult) o;
    return ObjectUtils.equals(this.code, upopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, upopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult.message) &&
    ObjectUtils.equals(this.payOrderNo, upopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult.payOrderNo) &&
    ObjectUtils.equals(this.qrCode, upopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult.qrCode) &&
    ObjectUtils.equals(this.orderValidTime, upopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult.orderValidTime) &&
    ObjectUtils.equals(this.orderDate, upopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult.orderDate);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, payOrderNo, qrCode, orderValidTime, orderDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UpopPassivescanBindQrcodeOpenPassiveScanPayResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    payOrderNo: ").append(toIndentedString(payOrderNo)).append("\n");
    sb.append("    qrCode: ").append(toIndentedString(qrCode)).append("\n");
    sb.append("    orderValidTime: ").append(toIndentedString(orderValidTime)).append("\n");
    sb.append("    orderDate: ").append(toIndentedString(orderDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

