/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * BindpaySendsmsApiBindPaySendSmsResponseDTOResult
 */
public class BindpaySendsmsApiBindPaySendSmsResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 订单token
   */
  @JsonProperty("token")
  private String token = null;

  /**
   * 支付记录ID
   */
  @JsonProperty("recordId")
  private String recordId = null;

  public BindpaySendsmsApiBindPaySendSmsResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BindpaySendsmsApiBindPaySendSmsResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回信息
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public BindpaySendsmsApiBindPaySendSmsResponseDTOResult token(String token) {
    this.token = token;
    return this;
  }

   /**
   * 订单token
   * @return token
  **/

  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  public BindpaySendsmsApiBindPaySendSmsResponseDTOResult recordId(String recordId) {
    this.recordId = recordId;
    return this;
  }

   /**
   * 支付记录ID
   * @return recordId
  **/

  public String getRecordId() {
    return recordId;
  }

  public void setRecordId(String recordId) {
    this.recordId = recordId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BindpaySendsmsApiBindPaySendSmsResponseDTOResult bindpaySendsmsApiBindPaySendSmsResponseDTOResult = (BindpaySendsmsApiBindPaySendSmsResponseDTOResult) o;
    return ObjectUtils.equals(this.code, bindpaySendsmsApiBindPaySendSmsResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, bindpaySendsmsApiBindPaySendSmsResponseDTOResult.message) &&
    ObjectUtils.equals(this.token, bindpaySendsmsApiBindPaySendSmsResponseDTOResult.token) &&
    ObjectUtils.equals(this.recordId, bindpaySendsmsApiBindPaySendSmsResponseDTOResult.recordId);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, token, recordId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindpaySendsmsApiBindPaySendSmsResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    token: ").append(toIndentedString(token)).append("\n");
    sb.append("    recordId: ").append(toIndentedString(recordId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

