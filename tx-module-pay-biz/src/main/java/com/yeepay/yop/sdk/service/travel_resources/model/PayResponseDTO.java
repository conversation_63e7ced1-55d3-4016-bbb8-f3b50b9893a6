/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付返回结果
 */
public class PayResponseDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;订单状态需要关注status&lt;/p&gt;
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * &lt;pre&gt;支付金额&lt;/pre&gt;
   */
  @JsonProperty("payAmount")
  private BigDecimal payAmount = null;

  /**
   * 
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * &lt;pre&gt;请求渠道下单时传过去的订单号&lt;/pre&gt;
   */
  @JsonProperty("systemOrderNo")
  private String systemOrderNo = null;

  /**
   * &lt;pre&gt;采购方在供应方的平台id&lt;/pre&gt;
   */
  @JsonProperty("purchasePlatformId")
  private String purchasePlatformId = null;

  /**
   * &lt;p&gt;支付成功时间&lt;/p&gt;
   */
  @JsonProperty("paySuccessTime")
  private String paySuccessTime = null;

  /**
   * &lt;p&gt;请求商编&lt;/p&gt;
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * &lt;pre&gt;商编订单&lt;/pre&gt;
   */
  @JsonProperty("merchantRequestNo")
  private String merchantRequestNo = null;

  /**
   * &lt;pre&gt;0：待支付&lt;/pre&gt; &lt;pre&gt;1：支付成功&lt;/pre&gt; &lt;pre&gt;2：支付失败&lt;/pre&gt; &lt;pre&gt;3：已过期&lt;/pre&gt;
   */
  @JsonProperty("status")
  private String status = null;

  /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;pre&gt;请求方订单号&lt;/pre&gt;
   */
  @JsonProperty("parentMerchantRequestNo")
  private String parentMerchantRequestNo = null;

  /**
   * &lt;p&gt;采购类型&lt;/p&gt;
   */
  @JsonProperty("purchaseType")
  private String purchaseType = null;

  /**
   * &lt;p&gt;在字段用来表示通知类型是交易还是退款。&lt;br /&gt;只有在异步通知结果里该参数才有值&lt;/p&gt;
   */
  @JsonProperty("trxType")
  private String trxType = null;

  public PayResponseDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * &lt;p&gt;订单状态需要关注status&lt;/p&gt;
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public PayResponseDTO payAmount(BigDecimal payAmount) {
    this.payAmount = payAmount;
    return this;
  }

   /**
   * &lt;pre&gt;支付金额&lt;/pre&gt;
   * @return payAmount
  **/

  public BigDecimal getPayAmount() {
    return payAmount;
  }

  public void setPayAmount(BigDecimal payAmount) {
    this.payAmount = payAmount;
  }

  public PayResponseDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * Get message
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public PayResponseDTO systemOrderNo(String systemOrderNo) {
    this.systemOrderNo = systemOrderNo;
    return this;
  }

   /**
   * &lt;pre&gt;请求渠道下单时传过去的订单号&lt;/pre&gt;
   * @return systemOrderNo
  **/

  public String getSystemOrderNo() {
    return systemOrderNo;
  }

  public void setSystemOrderNo(String systemOrderNo) {
    this.systemOrderNo = systemOrderNo;
  }

  public PayResponseDTO purchasePlatformId(String purchasePlatformId) {
    this.purchasePlatformId = purchasePlatformId;
    return this;
  }

   /**
   * &lt;pre&gt;采购方在供应方的平台id&lt;/pre&gt;
   * @return purchasePlatformId
  **/

  public String getPurchasePlatformId() {
    return purchasePlatformId;
  }

  public void setPurchasePlatformId(String purchasePlatformId) {
    this.purchasePlatformId = purchasePlatformId;
  }

  public PayResponseDTO paySuccessTime(String paySuccessTime) {
    this.paySuccessTime = paySuccessTime;
    return this;
  }

   /**
   * &lt;p&gt;支付成功时间&lt;/p&gt;
   * @return paySuccessTime
  **/

  public String getPaySuccessTime() {
    return paySuccessTime;
  }

  public void setPaySuccessTime(String paySuccessTime) {
    this.paySuccessTime = paySuccessTime;
  }

  public PayResponseDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;请求商编&lt;/p&gt;
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public PayResponseDTO merchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
    return this;
  }

   /**
   * &lt;pre&gt;商编订单&lt;/pre&gt;
   * @return merchantRequestNo
  **/

  public String getMerchantRequestNo() {
    return merchantRequestNo;
  }

  public void setMerchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
  }

  public PayResponseDTO status(String status) {
    this.status = status;
    return this;
  }

   /**
   * &lt;pre&gt;0：待支付&lt;/pre&gt; &lt;pre&gt;1：支付成功&lt;/pre&gt; &lt;pre&gt;2：支付失败&lt;/pre&gt; &lt;pre&gt;3：已过期&lt;/pre&gt;
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public PayResponseDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public PayResponseDTO parentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
    return this;
  }

   /**
   * &lt;pre&gt;请求方订单号&lt;/pre&gt;
   * @return parentMerchantRequestNo
  **/

  public String getParentMerchantRequestNo() {
    return parentMerchantRequestNo;
  }

  public void setParentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
  }

  public PayResponseDTO purchaseType(String purchaseType) {
    this.purchaseType = purchaseType;
    return this;
  }

   /**
   * &lt;p&gt;采购类型&lt;/p&gt;
   * @return purchaseType
  **/

  public String getPurchaseType() {
    return purchaseType;
  }

  public void setPurchaseType(String purchaseType) {
    this.purchaseType = purchaseType;
  }

  public PayResponseDTO trxType(String trxType) {
    this.trxType = trxType;
    return this;
  }

   /**
   * &lt;p&gt;在字段用来表示通知类型是交易还是退款。&lt;br /&gt;只有在异步通知结果里该参数才有值&lt;/p&gt;
   * @return trxType
  **/

  public String getTrxType() {
    return trxType;
  }

  public void setTrxType(String trxType) {
    this.trxType = trxType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    PayResponseDTO payResponseDTO = (PayResponseDTO) o;
    return ObjectUtils.equals(this.code, payResponseDTO.code) &&
    ObjectUtils.equals(this.payAmount, payResponseDTO.payAmount) &&
    ObjectUtils.equals(this.message, payResponseDTO.message) &&
    ObjectUtils.equals(this.systemOrderNo, payResponseDTO.systemOrderNo) &&
    ObjectUtils.equals(this.purchasePlatformId, payResponseDTO.purchasePlatformId) &&
    ObjectUtils.equals(this.paySuccessTime, payResponseDTO.paySuccessTime) &&
    ObjectUtils.equals(this.parentMerchantNo, payResponseDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantRequestNo, payResponseDTO.merchantRequestNo) &&
    ObjectUtils.equals(this.status, payResponseDTO.status) &&
    ObjectUtils.equals(this.merchantNo, payResponseDTO.merchantNo) &&
    ObjectUtils.equals(this.parentMerchantRequestNo, payResponseDTO.parentMerchantRequestNo) &&
    ObjectUtils.equals(this.purchaseType, payResponseDTO.purchaseType) &&
    ObjectUtils.equals(this.trxType, payResponseDTO.trxType);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, payAmount, message, systemOrderNo, purchasePlatformId, paySuccessTime, parentMerchantNo, merchantRequestNo, status, merchantNo, parentMerchantRequestNo, purchaseType, trxType);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PayResponseDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    payAmount: ").append(toIndentedString(payAmount)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    systemOrderNo: ").append(toIndentedString(systemOrderNo)).append("\n");
    sb.append("    purchasePlatformId: ").append(toIndentedString(purchasePlatformId)).append("\n");
    sb.append("    paySuccessTime: ").append(toIndentedString(paySuccessTime)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantRequestNo: ").append(toIndentedString(merchantRequestNo)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    parentMerchantRequestNo: ").append(toIndentedString(parentMerchantRequestNo)).append("\n");
    sb.append("    purchaseType: ").append(toIndentedString(purchaseType)).append("\n");
    sb.append("    trxType: ").append(toIndentedString(trxType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

