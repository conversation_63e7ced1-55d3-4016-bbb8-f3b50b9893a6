/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class AccountQueryBalanceV10Request extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String merchantMemberNo;

    private String parentMerchantNo;

    private String merchantNo;

    private String accountType;

    private String elecAccount;


    /**
     * 商户侧存的会员编号，不同人的会员编号不能相同
     * @return merchantMemberNo
     **/
    
    public String getMerchantMemberNo() {
        return merchantMemberNo;
    }

    public void setMerchantMemberNo(String merchantMemberNo) {
        this.merchantMemberNo = merchantMemberNo;
    }

    /**
     * 发起方商户编号&lt;br&gt;（标准商户收付款方案中此参数与商编一致，平台商户收付款方案中此参数为平台商商户编号）
     * @return parentMerchantNo
     **/
    
    public String getParentMerchantNo() {
        return parentMerchantNo;
    }

    public void setParentMerchantNo(String parentMerchantNo) {
        this.parentMerchantNo = parentMerchantNo;
    }

    /**
     * 易宝支付分配的的商户唯一标识
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    /**
     * 可选项如下:&lt;br&gt;CNCB:中信银行
     * @return accountType
     **/
    
    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    /**
     * 账户号
     * @return elecAccount
     **/
    
    public String getElecAccount() {
        return elecAccount;
    }

    public void setElecAccount(String elecAccount) {
        this.elecAccount = elecAccount;
    }

    @Override
    public String getOperationId() {
        return "account_query_balance_v1_0";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
