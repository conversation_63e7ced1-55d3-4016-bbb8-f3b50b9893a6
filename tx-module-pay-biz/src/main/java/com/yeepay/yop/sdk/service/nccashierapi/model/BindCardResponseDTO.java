/*
 * API收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.nccashierapi.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yeepay.yop.sdk.service.base.request.serializer.YopCustomSerializer;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 绑卡收银台响应参数
 */
public class BindCardResponseDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;业务响应码&lt;br /&gt;0000表示成功&lt;/p&gt;
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * &lt;p&gt;业务响应信息&lt;/p&gt;
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 
   */
  @JsonProperty("yeepayPageUrl")
  private String yeepayPageUrl = null;

  /**
   * 
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * 
   */
  @JsonProperty("merchantFlowId")
  private String merchantFlowId = null;

  /**
   * 
   */
  @JsonProperty("bindRequestId")
  private String bindRequestId = null;

  public BindCardResponseDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * &lt;p&gt;业务响应码&lt;br /&gt;0000表示成功&lt;/p&gt;
   * @return code
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BindCardResponseDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * &lt;p&gt;业务响应信息&lt;/p&gt;
   * @return message
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public BindCardResponseDTO yeepayPageUrl(String yeepayPageUrl) {
    this.yeepayPageUrl = yeepayPageUrl;
    return this;
  }

   /**
   * Get yeepayPageUrl
   * @return yeepayPageUrl
  **/

  public String getYeepayPageUrl() {
    return yeepayPageUrl;
  }

  public void setYeepayPageUrl(String yeepayPageUrl) {
    this.yeepayPageUrl = yeepayPageUrl;
  }

  public BindCardResponseDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * Get merchantNo
   * @return merchantNo
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public BindCardResponseDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * Get parentMerchantNo
   * @return parentMerchantNo
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public BindCardResponseDTO merchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
    return this;
  }

   /**
   * Get merchantFlowId
   * @return merchantFlowId
  **/

  public String getMerchantFlowId() {
    return merchantFlowId;
  }

  public void setMerchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
  }

  public BindCardResponseDTO bindRequestId(String bindRequestId) {
    this.bindRequestId = bindRequestId;
    return this;
  }

   /**
   * Get bindRequestId
   * @return bindRequestId
  **/

  public String getBindRequestId() {
    return bindRequestId;
  }

  public void setBindRequestId(String bindRequestId) {
    this.bindRequestId = bindRequestId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BindCardResponseDTO bindCardResponseDTO = (BindCardResponseDTO) o;
    return ObjectUtils.equals(this.code, bindCardResponseDTO.code) &&
    ObjectUtils.equals(this.message, bindCardResponseDTO.message) &&
    ObjectUtils.equals(this.yeepayPageUrl, bindCardResponseDTO.yeepayPageUrl) &&
    ObjectUtils.equals(this.merchantNo, bindCardResponseDTO.merchantNo) &&
    ObjectUtils.equals(this.parentMerchantNo, bindCardResponseDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantFlowId, bindCardResponseDTO.merchantFlowId) &&
    ObjectUtils.equals(this.bindRequestId, bindCardResponseDTO.bindRequestId);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, yeepayPageUrl, merchantNo, parentMerchantNo, merchantFlowId, bindRequestId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindCardResponseDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    yeepayPageUrl: ").append(toIndentedString(yeepayPageUrl)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantFlowId: ").append(toIndentedString(merchantFlowId)).append("\n");
    sb.append("    bindRequestId: ").append(toIndentedString(bindRequestId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

