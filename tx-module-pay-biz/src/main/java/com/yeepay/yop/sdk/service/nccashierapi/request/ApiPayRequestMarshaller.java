/*
 * API收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.nccashierapi.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class ApiPayRequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<ApiPayRequest> {
    private final String serviceName = "Nccashierapi";

    private final String resourcePath = "/rest/v1.0/nccashierapi/api/pay";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.POST;


    @Override
    public Request<ApiPayRequest> marshall(ApiPayRequest request) {
        Request<ApiPayRequest> internalRequest = new DefaultRequest<ApiPayRequest>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getPayTool() != null) {
            internalRequest.addParameter("payTool", PrimitiveMarshallerUtils.marshalling(request.getPayTool(), "String"));
        }
        if (request.getPayType() != null) {
            internalRequest.addParameter("payType", PrimitiveMarshallerUtils.marshalling(request.getPayType(), "String"));
        }
        if (request.getToken() != null) {
            internalRequest.addParameter("token", PrimitiveMarshallerUtils.marshalling(request.getToken(), "String"));
        }
        if (request.getAppId() != null) {
            internalRequest.addParameter("appId", PrimitiveMarshallerUtils.marshalling(request.getAppId(), "String"));
        }
        if (request.getOpenId() != null) {
            internalRequest.addParameter("openId", PrimitiveMarshallerUtils.marshalling(request.getOpenId(), "String"));
        }
        if (request.getVersion() != null) {
            internalRequest.addParameter("version", PrimitiveMarshallerUtils.marshalling(request.getVersion(), "String"));
        }
        if (request.getPayEmpowerNo() != null) {
            internalRequest.addParameter("payEmpowerNo", PrimitiveMarshallerUtils.marshalling(request.getPayEmpowerNo(), "String"));
        }
        if (request.getMerchantTerminalId() != null) {
            internalRequest.addParameter("merchantTerminalId", PrimitiveMarshallerUtils.marshalling(request.getMerchantTerminalId(), "String"));
        }
        if (request.getMerchantStoreNo() != null) {
            internalRequest.addParameter("merchantStoreNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantStoreNo(), "String"));
        }
        if (request.getUserIp() != null) {
            internalRequest.addParameter("userIp", PrimitiveMarshallerUtils.marshalling(request.getUserIp(), "String"));
        }
        if (request.getExtParamMap() != null) {
            internalRequest.addParameter("extParamMap", PrimitiveMarshallerUtils.marshalling(request.getExtParamMap(), "String"));
        }
        if (request.getUserNo() != null) {
            internalRequest.addParameter("userNo", PrimitiveMarshallerUtils.marshalling(request.getUserNo(), "String"));
        }
        if (request.getUserType() != null) {
            internalRequest.addParameter("userType", PrimitiveMarshallerUtils.marshalling(request.getUserType(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static ApiPayRequestMarshaller INSTANCE = new ApiPayRequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static ApiPayRequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
