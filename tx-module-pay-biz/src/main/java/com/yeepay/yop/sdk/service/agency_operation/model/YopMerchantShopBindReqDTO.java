/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.agency_operation.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yeepay.yop.sdk.service.base.request.serializer.YopCustomSerializer;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 请修改我
 */
public class YopMerchantShopBindReqDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;业务id&lt;/p&gt; &lt;p&gt;1：到店餐饮&lt;/p&gt; &lt;p&gt;4：到综行业&lt;/p&gt;
   */
  @JsonProperty("businessId")
  private String businessId = null;

  /**
   * &lt;p&gt;网点名称&lt;/p&gt;
   */
  @JsonProperty("shopName")
  private String shopName = null;

  /**
   * &lt;p&gt;通知地址&lt;/p&gt;
   */
  @JsonProperty("notifyUrl")
  private String notifyUrl = null;

  /**
   * &lt;p&gt;渠道类型&lt;/p&gt; &lt;p&gt;MEITUAN：美团&lt;/p&gt; &lt;p&gt;DOUYIN：抖音&lt;/p&gt;
   */
  @JsonProperty("channelType")
  private String channelType = null;

  /**
   * &lt;p&gt;网点编号&lt;/p&gt;
   */
  @JsonProperty("shopNo")
  private String shopNo = null;

  /**
   * &lt;p&gt;被抽佣方商编&lt;/p&gt;
   */
  @JsonProperty("contributeMerchantNo")
  private String contributeMerchantNo = null;

  /**
   * &lt;p&gt;抽佣接收方商编&lt;/p&gt;
   */
  @JsonProperty("receiveMerchantNo")
  private String receiveMerchantNo = null;

  /**
   * &lt;p&gt;商户资质存储url，需通过【子商户入网资质文件上传】接口，上传【数据授权协议】以及【门店和代运营的扣款协议】&lt;/p&gt;
   */
  @JsonProperty("merQualUrl")
  private String merQualUrl = null;

  public YopMerchantShopBindReqDTO businessId(String businessId) {
    this.businessId = businessId;
    return this;
  }

   /**
   * &lt;p&gt;业务id&lt;/p&gt; &lt;p&gt;1：到店餐饮&lt;/p&gt; &lt;p&gt;4：到综行业&lt;/p&gt;
   * @return businessId
  **/

  public String getBusinessId() {
    return businessId;
  }

  public void setBusinessId(String businessId) {
    this.businessId = businessId;
  }

  public YopMerchantShopBindReqDTO shopName(String shopName) {
    this.shopName = shopName;
    return this;
  }

   /**
   * &lt;p&gt;网点名称&lt;/p&gt;
   * @return shopName
  **/

  public String getShopName() {
    return shopName;
  }

  public void setShopName(String shopName) {
    this.shopName = shopName;
  }

  public YopMerchantShopBindReqDTO notifyUrl(String notifyUrl) {
    this.notifyUrl = notifyUrl;
    return this;
  }

   /**
   * &lt;p&gt;通知地址&lt;/p&gt;
   * @return notifyUrl
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getNotifyUrl() {
    return notifyUrl;
  }

  public void setNotifyUrl(String notifyUrl) {
    this.notifyUrl = notifyUrl;
  }

  public YopMerchantShopBindReqDTO channelType(String channelType) {
    this.channelType = channelType;
    return this;
  }

   /**
   * &lt;p&gt;渠道类型&lt;/p&gt; &lt;p&gt;MEITUAN：美团&lt;/p&gt; &lt;p&gt;DOUYIN：抖音&lt;/p&gt;
   * @return channelType
  **/

  public String getChannelType() {
    return channelType;
  }

  public void setChannelType(String channelType) {
    this.channelType = channelType;
  }

  public YopMerchantShopBindReqDTO shopNo(String shopNo) {
    this.shopNo = shopNo;
    return this;
  }

   /**
   * &lt;p&gt;网点编号&lt;/p&gt;
   * @return shopNo
  **/

  public String getShopNo() {
    return shopNo;
  }

  public void setShopNo(String shopNo) {
    this.shopNo = shopNo;
  }

  public YopMerchantShopBindReqDTO contributeMerchantNo(String contributeMerchantNo) {
    this.contributeMerchantNo = contributeMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;被抽佣方商编&lt;/p&gt;
   * @return contributeMerchantNo
  **/

  public String getContributeMerchantNo() {
    return contributeMerchantNo;
  }

  public void setContributeMerchantNo(String contributeMerchantNo) {
    this.contributeMerchantNo = contributeMerchantNo;
  }

  public YopMerchantShopBindReqDTO receiveMerchantNo(String receiveMerchantNo) {
    this.receiveMerchantNo = receiveMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;抽佣接收方商编&lt;/p&gt;
   * @return receiveMerchantNo
  **/

  public String getReceiveMerchantNo() {
    return receiveMerchantNo;
  }

  public void setReceiveMerchantNo(String receiveMerchantNo) {
    this.receiveMerchantNo = receiveMerchantNo;
  }

  public YopMerchantShopBindReqDTO merQualUrl(String merQualUrl) {
    this.merQualUrl = merQualUrl;
    return this;
  }

   /**
   * &lt;p&gt;商户资质存储url，需通过【子商户入网资质文件上传】接口，上传【数据授权协议】以及【门店和代运营的扣款协议】&lt;/p&gt;
   * @return merQualUrl
  **/

  public String getMerQualUrl() {
    return merQualUrl;
  }

  public void setMerQualUrl(String merQualUrl) {
    this.merQualUrl = merQualUrl;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    YopMerchantShopBindReqDTO yopMerchantShopBindReqDTO = (YopMerchantShopBindReqDTO) o;
    return ObjectUtils.equals(this.businessId, yopMerchantShopBindReqDTO.businessId) &&
    ObjectUtils.equals(this.shopName, yopMerchantShopBindReqDTO.shopName) &&
    ObjectUtils.equals(this.notifyUrl, yopMerchantShopBindReqDTO.notifyUrl) &&
    ObjectUtils.equals(this.channelType, yopMerchantShopBindReqDTO.channelType) &&
    ObjectUtils.equals(this.shopNo, yopMerchantShopBindReqDTO.shopNo) &&
    ObjectUtils.equals(this.contributeMerchantNo, yopMerchantShopBindReqDTO.contributeMerchantNo) &&
    ObjectUtils.equals(this.receiveMerchantNo, yopMerchantShopBindReqDTO.receiveMerchantNo) &&
    ObjectUtils.equals(this.merQualUrl, yopMerchantShopBindReqDTO.merQualUrl);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(businessId, shopName, notifyUrl, channelType, shopNo, contributeMerchantNo, receiveMerchantNo, merQualUrl);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class YopMerchantShopBindReqDTO {\n");
    
    sb.append("    businessId: ").append(toIndentedString(businessId)).append("\n");
    sb.append("    shopName: ").append(toIndentedString(shopName)).append("\n");
    sb.append("    notifyUrl: ").append(toIndentedString(notifyUrl)).append("\n");
    sb.append("    channelType: ").append(toIndentedString(channelType)).append("\n");
    sb.append("    shopNo: ").append(toIndentedString(shopNo)).append("\n");
    sb.append("    contributeMerchantNo: ").append(toIndentedString(contributeMerchantNo)).append("\n");
    sb.append("    receiveMerchantNo: ").append(toIndentedString(receiveMerchantNo)).append("\n");
    sb.append("    merQualUrl: ").append(toIndentedString(merQualUrl)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

