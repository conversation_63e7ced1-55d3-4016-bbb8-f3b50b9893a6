/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * PointOperatePointAccountOperateResponseDTOResult
 */
public class PointOperatePointAccountOperateResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 易宝订单号
   */
  @JsonProperty("unionPointNo")
  private String unionPointNo = null;

  /**
   * 变更结果
   */
  @JsonProperty("status")
  private String status = null;

  public PointOperatePointAccountOperateResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public PointOperatePointAccountOperateResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public PointOperatePointAccountOperateResponseDTOResult unionPointNo(String unionPointNo) {
    this.unionPointNo = unionPointNo;
    return this;
  }

   /**
   * 易宝订单号
   * @return unionPointNo
  **/

  public String getUnionPointNo() {
    return unionPointNo;
  }

  public void setUnionPointNo(String unionPointNo) {
    this.unionPointNo = unionPointNo;
  }

  public PointOperatePointAccountOperateResponseDTOResult status(String status) {
    this.status = status;
    return this;
  }

   /**
   * 变更结果
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    PointOperatePointAccountOperateResponseDTOResult pointOperatePointAccountOperateResponseDTOResult = (PointOperatePointAccountOperateResponseDTOResult) o;
    return ObjectUtils.equals(this.code, pointOperatePointAccountOperateResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, pointOperatePointAccountOperateResponseDTOResult.message) &&
    ObjectUtils.equals(this.unionPointNo, pointOperatePointAccountOperateResponseDTOResult.unionPointNo) &&
    ObjectUtils.equals(this.status, pointOperatePointAccountOperateResponseDTOResult.status);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, unionPointNo, status);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PointOperatePointAccountOperateResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    unionPointNo: ").append(toIndentedString(unionPointNo)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

