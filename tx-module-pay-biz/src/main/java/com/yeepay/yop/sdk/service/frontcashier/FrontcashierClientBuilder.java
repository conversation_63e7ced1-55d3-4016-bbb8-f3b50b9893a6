/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier;


import com.yeepay.yop.sdk.auth.req.AuthorizationReqRegistry;
import com.yeepay.yop.sdk.auth.req.DefaultAuthorizationReqRegistry;
import com.yeepay.yop.sdk.client.ClientParams;
import com.yeepay.yop.sdk.client.AbstractServiceClientBuilder;

public class FrontcashierClientBuilder extends AbstractServiceClientBuilder<FrontcashierClientBuilder, FrontcashierClientImpl>{

    private static final AuthorizationReqRegistry REGISTRY;

    static {
        REGISTRY = new DefaultAuthorizationReqRegistry();
        REGISTRY.register("accountConfirm", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("accountInfoConfirm", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bankTransferPay", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bankTransferQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardBindcardlist", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardConfirm", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardConfirmV2", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardGetcardbin", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardPayerrequest", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardQueryorder", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardQueryorderinfo", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardRequest", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardRequestV2", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardRequestV2_1", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardResendsms", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardResendsmsV2", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardUnbindcard", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindpayConfirm", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindpayRequest", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindpaySendsms", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("fastbindcardRequest", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("getcardbin", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("upopActivescanPay", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("upopActivescanQuerycoupon", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("upopActivescanQuerypayeeorder", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("upopActivescanQuerypayresult", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("upopPassivescanBindQrcode", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("upopPassivescanValidate", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("yjzfBindpayrequest", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("yjzfFirstpayrequest", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("yjzfPaymentconfirm", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("yjzfSendsms", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
    }

    @Override
    protected AuthorizationReqRegistry authorizationReqRegistry() {
        return REGISTRY;
    }

    @Override
    protected FrontcashierClientImpl build(ClientParams params) {
        return new FrontcashierClientImpl(params);
    }

    /**
     * @return Create new instance of builder with all defaults set.
     */
    public static FrontcashierClientBuilder builder(){
        return new FrontcashierClientBuilder();
    }

}
