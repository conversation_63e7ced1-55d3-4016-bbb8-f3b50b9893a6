/*
 * 再处理
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.reprocess.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class MigrateBankQueryV10Request extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String migrateRequestId;

    private String parentMerchantNo;

    private String merchantNo;


    /**
     * 需查询迁移请求的请求号
     * @return migrateRequestId
     **/
    
    public String getMigrateRequestId() {
        return migrateRequestId;
    }

    public void setMigrateRequestId(String migrateRequestId) {
        this.migrateRequestId = migrateRequestId;
    }

    /**
     * 与申请时一致
     * @return parentMerchantNo
     **/
    
    public String getParentMerchantNo() {
        return parentMerchantNo;
    }

    public void setParentMerchantNo(String parentMerchantNo) {
        this.parentMerchantNo = parentMerchantNo;
    }

    /**
     * 收款订单的商户编号，与申请时一致
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    @Override
    public String getOperationId() {
        return "migrate_bank_query_v1_0";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
