/*
 * 公益-月捐
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.month_donate.request;

import com.yeepay.yop.sdk.service.month_donate.model.OpenMonthDonateRequestDTO;
public class OpenRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private OpenMonthDonateRequestDTO body;


    /**
     * 外放开通月捐接口，支持微信公众号，微信小程序，绑定银行卡三种开通方式
     * @return body
     **/
    
    public OpenMonthDonateRequestDTO getBody() {
        return body;
    }

    public void setBody(OpenMonthDonateRequestDTO body) {
        this.body = body;
    }

    @Override
    public String getOperationId() {
        return "open";
    }
}
