/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * BindcardQueryorderOpenQueryOrderResponseDTOResult
 */
public class BindcardQueryorderOpenQueryOrderResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 收单商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 商户签约/绑卡请求号
   */
  @JsonProperty("merchantFlowId")
  private String merchantFlowId = null;

  /**
   * 易宝订单号
   */
  @JsonProperty("nopOrderId")
  private String nopOrderId = null;

  /**
   * 订单状态
   */
  @JsonProperty("orderStatus")
  private String orderStatus = null;

  /**
   * 银行签约状态
   */
  @JsonProperty("signStatus")
  private String signStatus = null;

  /**
   * 用户标识
   */
  @JsonProperty("userNo")
  private String userNo = null;

  /**
   * 用户标识类型
   */
  @JsonProperty("userType")
  private String userType = null;

  /**
   * 商业委托协议ID，仅商业委托返回
   */
  @JsonProperty("entrustProtocolId")
  private String entrustProtocolId = null;

  /**
   * 代收金额，仅商业委托返回
   */
  @JsonProperty("collectAmount")
  private BigDecimal collectAmount = null;

  /**
   * 代收开始时间，仅商业委托返回
   */
  @JsonProperty("collectBeginDate")
  private String collectBeginDate = null;

  /**
   * 代收结束时间，仅商业委托返回
   */
  @JsonProperty("collectEndDate")
  private String collectEndDate = null;

  /**
   * 代收频次，仅商业委托返回
   */
  @JsonProperty("cycleFrequency")
  private Integer cycleFrequency = null;

  /**
   * 代收步长，仅商业委托返回
   */
  @JsonProperty("cycleStep")
  private Integer cycleStep = null;

  /**
   * 代收周期单位，仅商业委托返回
   */
  @JsonProperty("cycleStepUnit")
  private String cycleStepUnit = null;

  /**
   * 跳页签约提交方式，仅跳页签约未成功时返回
   */
  @JsonProperty("submitMethod")
  private String submitMethod = null;

  /**
   * 跳页签约url，仅跳页签约未成功时返回
   */
  @JsonProperty("submitUrl")
  private String submitUrl = null;

  /**
   * 跳页页面编码，仅跳页签约未成功时返回
   */
  @JsonProperty("encoding")
  private String encoding = null;

  /**
   * 银行卡号
   */
  @JsonProperty("bankCardNo")
  private String bankCardNo = null;

  /**
   * 姓名
   */
  @JsonProperty("userName")
  private String userName = null;

  /**
   * 证件号
   */
  @JsonProperty("idCardNo")
  private String idCardNo = null;

  /**
   * 证件类型
   */
  @JsonProperty("idCardType")
  private String idCardType = null;

  /**
   * 银行预留手机号
   */
  @JsonProperty("phone")
  private String phone = null;

  /**
   * 银行编码
   */
  @JsonProperty("bankCode")
  private String bankCode = null;

  /**
   * 跳页签约回调地址，仅跳页签约返回
   */
  @JsonProperty("signSuccessNotifyUrl")
  private String signSuccessNotifyUrl = null;

  /**
   * 签约/绑卡ID
   */
  @JsonProperty("bindId")
  private String bindId = null;

  /**
   * 签约类型
   */
  @JsonProperty("signType")
  private String signType = null;

  public BindcardQueryorderOpenQueryOrderResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 收单商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult merchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
    return this;
  }

   /**
   * 商户签约/绑卡请求号
   * @return merchantFlowId
  **/

  public String getMerchantFlowId() {
    return merchantFlowId;
  }

  public void setMerchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult nopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
    return this;
  }

   /**
   * 易宝订单号
   * @return nopOrderId
  **/

  public String getNopOrderId() {
    return nopOrderId;
  }

  public void setNopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult orderStatus(String orderStatus) {
    this.orderStatus = orderStatus;
    return this;
  }

   /**
   * 订单状态
   * @return orderStatus
  **/

  public String getOrderStatus() {
    return orderStatus;
  }

  public void setOrderStatus(String orderStatus) {
    this.orderStatus = orderStatus;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult signStatus(String signStatus) {
    this.signStatus = signStatus;
    return this;
  }

   /**
   * 银行签约状态
   * @return signStatus
  **/

  public String getSignStatus() {
    return signStatus;
  }

  public void setSignStatus(String signStatus) {
    this.signStatus = signStatus;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult userNo(String userNo) {
    this.userNo = userNo;
    return this;
  }

   /**
   * 用户标识
   * @return userNo
  **/

  public String getUserNo() {
    return userNo;
  }

  public void setUserNo(String userNo) {
    this.userNo = userNo;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult userType(String userType) {
    this.userType = userType;
    return this;
  }

   /**
   * 用户标识类型
   * @return userType
  **/

  public String getUserType() {
    return userType;
  }

  public void setUserType(String userType) {
    this.userType = userType;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult entrustProtocolId(String entrustProtocolId) {
    this.entrustProtocolId = entrustProtocolId;
    return this;
  }

   /**
   * 商业委托协议ID，仅商业委托返回
   * @return entrustProtocolId
  **/

  public String getEntrustProtocolId() {
    return entrustProtocolId;
  }

  public void setEntrustProtocolId(String entrustProtocolId) {
    this.entrustProtocolId = entrustProtocolId;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult collectAmount(BigDecimal collectAmount) {
    this.collectAmount = collectAmount;
    return this;
  }

   /**
   * 代收金额，仅商业委托返回
   * @return collectAmount
  **/

  public BigDecimal getCollectAmount() {
    return collectAmount;
  }

  public void setCollectAmount(BigDecimal collectAmount) {
    this.collectAmount = collectAmount;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult collectBeginDate(String collectBeginDate) {
    this.collectBeginDate = collectBeginDate;
    return this;
  }

   /**
   * 代收开始时间，仅商业委托返回
   * @return collectBeginDate
  **/

  public String getCollectBeginDate() {
    return collectBeginDate;
  }

  public void setCollectBeginDate(String collectBeginDate) {
    this.collectBeginDate = collectBeginDate;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult collectEndDate(String collectEndDate) {
    this.collectEndDate = collectEndDate;
    return this;
  }

   /**
   * 代收结束时间，仅商业委托返回
   * @return collectEndDate
  **/

  public String getCollectEndDate() {
    return collectEndDate;
  }

  public void setCollectEndDate(String collectEndDate) {
    this.collectEndDate = collectEndDate;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult cycleFrequency(Integer cycleFrequency) {
    this.cycleFrequency = cycleFrequency;
    return this;
  }

   /**
   * 代收频次，仅商业委托返回
   * @return cycleFrequency
  **/

  public Integer getCycleFrequency() {
    return cycleFrequency;
  }

  public void setCycleFrequency(Integer cycleFrequency) {
    this.cycleFrequency = cycleFrequency;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult cycleStep(Integer cycleStep) {
    this.cycleStep = cycleStep;
    return this;
  }

   /**
   * 代收步长，仅商业委托返回
   * @return cycleStep
  **/

  public Integer getCycleStep() {
    return cycleStep;
  }

  public void setCycleStep(Integer cycleStep) {
    this.cycleStep = cycleStep;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult cycleStepUnit(String cycleStepUnit) {
    this.cycleStepUnit = cycleStepUnit;
    return this;
  }

   /**
   * 代收周期单位，仅商业委托返回
   * @return cycleStepUnit
  **/

  public String getCycleStepUnit() {
    return cycleStepUnit;
  }

  public void setCycleStepUnit(String cycleStepUnit) {
    this.cycleStepUnit = cycleStepUnit;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult submitMethod(String submitMethod) {
    this.submitMethod = submitMethod;
    return this;
  }

   /**
   * 跳页签约提交方式，仅跳页签约未成功时返回
   * @return submitMethod
  **/

  public String getSubmitMethod() {
    return submitMethod;
  }

  public void setSubmitMethod(String submitMethod) {
    this.submitMethod = submitMethod;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult submitUrl(String submitUrl) {
    this.submitUrl = submitUrl;
    return this;
  }

   /**
   * 跳页签约url，仅跳页签约未成功时返回
   * @return submitUrl
  **/

  public String getSubmitUrl() {
    return submitUrl;
  }

  public void setSubmitUrl(String submitUrl) {
    this.submitUrl = submitUrl;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult encoding(String encoding) {
    this.encoding = encoding;
    return this;
  }

   /**
   * 跳页页面编码，仅跳页签约未成功时返回
   * @return encoding
  **/

  public String getEncoding() {
    return encoding;
  }

  public void setEncoding(String encoding) {
    this.encoding = encoding;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult bankCardNo(String bankCardNo) {
    this.bankCardNo = bankCardNo;
    return this;
  }

   /**
   * 银行卡号
   * @return bankCardNo
  **/

  public String getBankCardNo() {
    return bankCardNo;
  }

  public void setBankCardNo(String bankCardNo) {
    this.bankCardNo = bankCardNo;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult userName(String userName) {
    this.userName = userName;
    return this;
  }

   /**
   * 姓名
   * @return userName
  **/

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult idCardNo(String idCardNo) {
    this.idCardNo = idCardNo;
    return this;
  }

   /**
   * 证件号
   * @return idCardNo
  **/

  public String getIdCardNo() {
    return idCardNo;
  }

  public void setIdCardNo(String idCardNo) {
    this.idCardNo = idCardNo;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult idCardType(String idCardType) {
    this.idCardType = idCardType;
    return this;
  }

   /**
   * 证件类型
   * @return idCardType
  **/

  public String getIdCardType() {
    return idCardType;
  }

  public void setIdCardType(String idCardType) {
    this.idCardType = idCardType;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult phone(String phone) {
    this.phone = phone;
    return this;
  }

   /**
   * 银行预留手机号
   * @return phone
  **/

  public String getPhone() {
    return phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult bankCode(String bankCode) {
    this.bankCode = bankCode;
    return this;
  }

   /**
   * 银行编码
   * @return bankCode
  **/

  public String getBankCode() {
    return bankCode;
  }

  public void setBankCode(String bankCode) {
    this.bankCode = bankCode;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult signSuccessNotifyUrl(String signSuccessNotifyUrl) {
    this.signSuccessNotifyUrl = signSuccessNotifyUrl;
    return this;
  }

   /**
   * 跳页签约回调地址，仅跳页签约返回
   * @return signSuccessNotifyUrl
  **/

  public String getSignSuccessNotifyUrl() {
    return signSuccessNotifyUrl;
  }

  public void setSignSuccessNotifyUrl(String signSuccessNotifyUrl) {
    this.signSuccessNotifyUrl = signSuccessNotifyUrl;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult bindId(String bindId) {
    this.bindId = bindId;
    return this;
  }

   /**
   * 签约/绑卡ID
   * @return bindId
  **/

  public String getBindId() {
    return bindId;
  }

  public void setBindId(String bindId) {
    this.bindId = bindId;
  }

  public BindcardQueryorderOpenQueryOrderResponseDTOResult signType(String signType) {
    this.signType = signType;
    return this;
  }

   /**
   * 签约类型
   * @return signType
  **/

  public String getSignType() {
    return signType;
  }

  public void setSignType(String signType) {
    this.signType = signType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BindcardQueryorderOpenQueryOrderResponseDTOResult bindcardQueryorderOpenQueryOrderResponseDTOResult = (BindcardQueryorderOpenQueryOrderResponseDTOResult) o;
    return ObjectUtils.equals(this.code, bindcardQueryorderOpenQueryOrderResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, bindcardQueryorderOpenQueryOrderResponseDTOResult.message) &&
    ObjectUtils.equals(this.merchantNo, bindcardQueryorderOpenQueryOrderResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.merchantFlowId, bindcardQueryorderOpenQueryOrderResponseDTOResult.merchantFlowId) &&
    ObjectUtils.equals(this.nopOrderId, bindcardQueryorderOpenQueryOrderResponseDTOResult.nopOrderId) &&
    ObjectUtils.equals(this.orderStatus, bindcardQueryorderOpenQueryOrderResponseDTOResult.orderStatus) &&
    ObjectUtils.equals(this.signStatus, bindcardQueryorderOpenQueryOrderResponseDTOResult.signStatus) &&
    ObjectUtils.equals(this.userNo, bindcardQueryorderOpenQueryOrderResponseDTOResult.userNo) &&
    ObjectUtils.equals(this.userType, bindcardQueryorderOpenQueryOrderResponseDTOResult.userType) &&
    ObjectUtils.equals(this.entrustProtocolId, bindcardQueryorderOpenQueryOrderResponseDTOResult.entrustProtocolId) &&
    ObjectUtils.equals(this.collectAmount, bindcardQueryorderOpenQueryOrderResponseDTOResult.collectAmount) &&
    ObjectUtils.equals(this.collectBeginDate, bindcardQueryorderOpenQueryOrderResponseDTOResult.collectBeginDate) &&
    ObjectUtils.equals(this.collectEndDate, bindcardQueryorderOpenQueryOrderResponseDTOResult.collectEndDate) &&
    ObjectUtils.equals(this.cycleFrequency, bindcardQueryorderOpenQueryOrderResponseDTOResult.cycleFrequency) &&
    ObjectUtils.equals(this.cycleStep, bindcardQueryorderOpenQueryOrderResponseDTOResult.cycleStep) &&
    ObjectUtils.equals(this.cycleStepUnit, bindcardQueryorderOpenQueryOrderResponseDTOResult.cycleStepUnit) &&
    ObjectUtils.equals(this.submitMethod, bindcardQueryorderOpenQueryOrderResponseDTOResult.submitMethod) &&
    ObjectUtils.equals(this.submitUrl, bindcardQueryorderOpenQueryOrderResponseDTOResult.submitUrl) &&
    ObjectUtils.equals(this.encoding, bindcardQueryorderOpenQueryOrderResponseDTOResult.encoding) &&
    ObjectUtils.equals(this.bankCardNo, bindcardQueryorderOpenQueryOrderResponseDTOResult.bankCardNo) &&
    ObjectUtils.equals(this.userName, bindcardQueryorderOpenQueryOrderResponseDTOResult.userName) &&
    ObjectUtils.equals(this.idCardNo, bindcardQueryorderOpenQueryOrderResponseDTOResult.idCardNo) &&
    ObjectUtils.equals(this.idCardType, bindcardQueryorderOpenQueryOrderResponseDTOResult.idCardType) &&
    ObjectUtils.equals(this.phone, bindcardQueryorderOpenQueryOrderResponseDTOResult.phone) &&
    ObjectUtils.equals(this.bankCode, bindcardQueryorderOpenQueryOrderResponseDTOResult.bankCode) &&
    ObjectUtils.equals(this.signSuccessNotifyUrl, bindcardQueryorderOpenQueryOrderResponseDTOResult.signSuccessNotifyUrl) &&
    ObjectUtils.equals(this.bindId, bindcardQueryorderOpenQueryOrderResponseDTOResult.bindId) &&
    ObjectUtils.equals(this.signType, bindcardQueryorderOpenQueryOrderResponseDTOResult.signType);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, merchantNo, merchantFlowId, nopOrderId, orderStatus, signStatus, userNo, userType, entrustProtocolId, collectAmount, collectBeginDate, collectEndDate, cycleFrequency, cycleStep, cycleStepUnit, submitMethod, submitUrl, encoding, bankCardNo, userName, idCardNo, idCardType, phone, bankCode, signSuccessNotifyUrl, bindId, signType);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindcardQueryorderOpenQueryOrderResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantFlowId: ").append(toIndentedString(merchantFlowId)).append("\n");
    sb.append("    nopOrderId: ").append(toIndentedString(nopOrderId)).append("\n");
    sb.append("    orderStatus: ").append(toIndentedString(orderStatus)).append("\n");
    sb.append("    signStatus: ").append(toIndentedString(signStatus)).append("\n");
    sb.append("    userNo: ").append(toIndentedString(userNo)).append("\n");
    sb.append("    userType: ").append(toIndentedString(userType)).append("\n");
    sb.append("    entrustProtocolId: ").append(toIndentedString(entrustProtocolId)).append("\n");
    sb.append("    collectAmount: ").append(toIndentedString(collectAmount)).append("\n");
    sb.append("    collectBeginDate: ").append(toIndentedString(collectBeginDate)).append("\n");
    sb.append("    collectEndDate: ").append(toIndentedString(collectEndDate)).append("\n");
    sb.append("    cycleFrequency: ").append(toIndentedString(cycleFrequency)).append("\n");
    sb.append("    cycleStep: ").append(toIndentedString(cycleStep)).append("\n");
    sb.append("    cycleStepUnit: ").append(toIndentedString(cycleStepUnit)).append("\n");
    sb.append("    submitMethod: ").append(toIndentedString(submitMethod)).append("\n");
    sb.append("    submitUrl: ").append(toIndentedString(submitUrl)).append("\n");
    sb.append("    encoding: ").append(toIndentedString(encoding)).append("\n");
    sb.append("    bankCardNo: ").append(toIndentedString(bankCardNo)).append("\n");
    sb.append("    userName: ").append(toIndentedString(userName)).append("\n");
    sb.append("    idCardNo: ").append(toIndentedString(idCardNo)).append("\n");
    sb.append("    idCardType: ").append(toIndentedString(idCardType)).append("\n");
    sb.append("    phone: ").append(toIndentedString(phone)).append("\n");
    sb.append("    bankCode: ").append(toIndentedString(bankCode)).append("\n");
    sb.append("    signSuccessNotifyUrl: ").append(toIndentedString(signSuccessNotifyUrl)).append("\n");
    sb.append("    bindId: ").append(toIndentedString(bindId)).append("\n");
    sb.append("    signType: ").append(toIndentedString(signType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

