/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.m_wallet.request.*;
import com.yeepay.yop.sdk.service.m_wallet.response.*;

public interface MWalletClient {

    /**
     * 开立钱包账户(支持人脸识别)
     * 商户调用此接口，输入用户的真实身份信息，易宝进行人脸识别认证通过后，会对应开立钱包账户。
     * @return AccountFaceCertifyOpenResponse
     * @throws YopClientException if fails to make API call
     */
    AccountFaceCertifyOpenResponse accountFaceCertifyOpen(AccountFaceCertifyOpenRequest request) throws YopClientException;

    /**
     * 开立钱包账户
     * 商户调用此接口，输入用户的真实身份信息，易宝通过公安认证渠道认证通过后，会对应开立钱包账户。
     * @return AccountOpenResponse
     * @throws YopClientException if fails to make API call
     */
    AccountOpenResponse accountOpen(AccountOpenRequest request) throws YopClientException;

    /**
     * 钱包开户成功通知
     * 调用此接口，通知商户开户成功结果
     * @return AccountOpenNotifyResponse
     * @throws YopClientException if fails to make API call
     */
    AccountOpenNotifyResponse accountOpenNotify(AccountOpenNotifyRequest request) throws YopClientException;

    /**
     * 查询钱包账户信息
     * 商户在易宝为会员开通钱包账户后，可通过接入该接口查询会员在易宝的钱包账户信息，包括钱包用户个人信息、钱包账户状态、钱包余额、认证情况及账户级别等
     * @return AccountQueryResponse
     * @throws YopClientException if fails to make API call
     */
    AccountQueryResponse accountQuery(AccountQueryRequest request) throws YopClientException;

    /**
     * 会员银行账户余额查询
     * 
     * @return AccountQueryBalanceResponse
     * @throws YopClientException if fails to make API call
     */
    AccountQueryBalanceResponse accountQueryBalance(AccountQueryBalanceRequest request) throws YopClientException;

    /**
     * 会员账户限额查询
     * 商户调用该接口，查询会员账户限额信息
     * @return AccountQueryQuotaResponse
     * @throws YopClientException if fails to make API call
     */
    AccountQueryQuotaResponse accountQueryQuota(AccountQueryQuotaRequest request) throws YopClientException;

    /**
     * 开立钱包账户(支持人脸识别)
     * 商户调用此接口，输入用户的真实身份信息，易宝进行人脸识别认证通过后，会对应开立钱包账户。
     * @return AccountFaceCertifyOpenResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AccountFaceCertifyOpenResponse account_face_certify_open(AccountFaceCertifyOpenRequest request) throws YopClientException;

    /**
     * 钱包开户成功通知
     * 调用此接口，通知商户开户成功结果
     * @return AccountOpenNotifyResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AccountOpenNotifyResponse account_open_notify(AccountOpenNotifyRequest request) throws YopClientException;

    /**
     * 开立钱包账户
     * 商户调用此接口，输入用户的真实身份信息，易宝通过公安认证渠道认证通过后，会对应开立钱包账户。
     * @return AccountOpenV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AccountOpenV10Response account_open_v1_0(AccountOpenV10Request request) throws YopClientException;

    /**
     * 会员银行账户余额查询
     * 
     * @return AccountQueryBalanceV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AccountQueryBalanceV10Response account_query_balance_v1_0(AccountQueryBalanceV10Request request) throws YopClientException;

    /**
     * 会员账户限额查询
     * 商户调用该接口，查询会员账户限额信息
     * @return AccountQueryQuotaResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AccountQueryQuotaResponse account_query_quota(AccountQueryQuotaRequest request) throws YopClientException;

    /**
     * 查询钱包账户信息
     * 商户在易宝为会员开通钱包账户后，可通过接入该接口查询会员在易宝的钱包账户信息，包括钱包用户个人信息、钱包账户状态、钱包余额、认证情况及账户级别等
     * @return AccountQueryV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AccountQueryV10Response account_query_v1_0(AccountQueryV10Request request) throws YopClientException;

    /**
     * 发起免密支付解约
     * 用户在商户端发起协议解约，商户调用此接口发起解约操作。
     * @return AgreementPaymentCancelResponse
     * @throws YopClientException if fails to make API call
     */
    AgreementPaymentCancelResponse agreementPaymentCancel(AgreementPaymentCancelRequest request) throws YopClientException;

    /**
     * 钱包免密支付协议查询接口
     * 钱包免密支付协议查询接口
     * @return AgreementPaymentQueryResponse
     * @throws YopClientException if fails to make API call
     */
    AgreementPaymentQueryResponse agreementPaymentQuery(AgreementPaymentQueryRequest request) throws YopClientException;

    /**
     * 钱包免密支付协议请求接口
     * 钱包免密支付协议请求接口
     * @return AgreementPaymentRequestResponse
     * @throws YopClientException if fails to make API call
     */
    AgreementPaymentRequestResponse agreementPaymentRequest(AgreementPaymentRequestRequest request) throws YopClientException;

    /**
     * 发起免密支付签约
     * 用户在商户端发起协议签约，商户调用此接口并打开对应url跳转到易宝验密签约页面，用户输入支付密码后验证并签约。
     * @return AgreementPaymentSignResponse
     * @throws YopClientException if fails to make API call
     */
    AgreementPaymentSignResponse agreementPaymentSign(AgreementPaymentSignRequest request) throws YopClientException;

    /**
     * 发起免密支付解约
     * 用户在商户端发起协议解约，商户调用此接口发起解约操作。
     * @return AgreementPaymentCancelV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AgreementPaymentCancelV10Response agreement_payment_cancel_v1_0(AgreementPaymentCancelV10Request request) throws YopClientException;

    /**
     * web3发起免密支付解约
     * 
     * @return AgreementPaymentCancelWeb3V10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AgreementPaymentCancelWeb3V10Response agreement_payment_cancel_web3_v1_0(AgreementPaymentCancelWeb3V10Request request) throws YopClientException;

    /**
     * 协议通知商户
     * 协议通知商户
     * @return AgreementPaymentNotifyWeb3Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AgreementPaymentNotifyWeb3Response agreement_payment_notify_web3(AgreementPaymentNotifyWeb3Request request) throws YopClientException;

    /**
     * 钱包免密支付协议查询接口
     * 钱包免密支付协议查询接口
     * @return AgreementPaymentQueryResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AgreementPaymentQueryResponse agreement_payment_query(AgreementPaymentQueryRequest request) throws YopClientException;

    /**
     * web3钱包免密支付协议查询接口
     * 钱包免密支付协议查询接口
     * @return AgreementPaymentQueryWeb3V10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AgreementPaymentQueryWeb3V10Response agreement_payment_query_web3_v1_0(AgreementPaymentQueryWeb3V10Request request) throws YopClientException;

    /**
     * 钱包免密支付协议请求接口
     * 钱包免密支付协议请求接口
     * @return AgreementPaymentRequestV1Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AgreementPaymentRequestV1Response agreement_payment_request_v1(AgreementPaymentRequestV1Request request) throws YopClientException;

    /**
     * 发起免密支付签约
     * 用户在商户端发起协议签约，商户调用此接口并打开对应url跳转到易宝验密签约页面，用户输入支付密码后验证并签约。
     * @return AgreementPaymentSignV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AgreementPaymentSignV10Response agreement_payment_sign_v1_0(AgreementPaymentSignV10Request request) throws YopClientException;

    /**
     * web3发起免密支付签约
     * 用户在商户端发起协议签约，商户调用此接口并打开对应url跳转到易宝验密签约页面，用户输入支付密码后验证并签约。
     * @return AgreementPaymentSignWeb3V10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AgreementPaymentSignWeb3V10Response agreement_payment_sign_web3_v1_0(AgreementPaymentSignWeb3V10Request request) throws YopClientException;

    /**
     * 发起会员自动提现
     * 发起会员自动提现，用户可查询自动提现结果
     * @return AutoWithdrawResponse
     * @throws YopClientException if fails to make API call
     */
    AutoWithdrawResponse autoWithdraw(AutoWithdrawRequest request) throws YopClientException;

    /**
     * 自动提现查询
     * 自动提现 订单 查询
     * @return AutoWithdrawQueryResponse
     * @throws YopClientException if fails to make API call
     */
    AutoWithdrawQueryResponse autoWithdrawQuery(AutoWithdrawQueryRequest request) throws YopClientException;

    /**
     * 会员自动扣款下单
     * 
     * @return AutoDeductionCreateResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AutoDeductionCreateResponse auto_deduction_create(AutoDeductionCreateRequest request) throws YopClientException;

    /**
     * 会员自动扣款查询
     * 
     * @return AutoDeductionQueryResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AutoDeductionQueryResponse auto_deduction_query(AutoDeductionQueryRequest request) throws YopClientException;

    /**
     * 发起会员自动提现
     * 发起会员自动提现，用户可查询自动提现结果
     * @return AutoWithdrawResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AutoWithdrawResponse auto_withdraw(AutoWithdrawRequest request) throws YopClientException;

    /**
     * 自动提现查询
     * 自动提现 订单 查询
     * @return AutoWithdrawQueryResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AutoWithdrawQueryResponse auto_withdraw_query(AutoWithdrawQueryRequest request) throws YopClientException;

    /**
     * 会员银行账户开户确认
     * 
     * @return BankAccountConfirmResponse
     * @throws YopClientException if fails to make API call
     */
    BankAccountConfirmResponse bankAccountConfirm(BankAccountConfirmRequest request) throws YopClientException;

    /**
     * 会员银行账户开户接口
     * 
     * @return BankAccountOpenResponse
     * @throws YopClientException if fails to make API call
     */
    BankAccountOpenResponse bankAccountOpen(BankAccountOpenRequest request) throws YopClientException;

    /**
     * 会员银行账户一分钱激活结果查询
     * 
     * @return BankAccountQueryActivationResponse
     * @throws YopClientException if fails to make API call
     */
    BankAccountQueryActivationResponse bankAccountQueryActivation(BankAccountQueryActivationRequest request) throws YopClientException;

    /**
     * 会员银行账户客诉订单查询
     * 
     * @return BankAccountQueryComplaintResponse
     * @throws YopClientException if fails to make API call
     */
    BankAccountQueryComplaintResponse bankAccountQueryComplaint(BankAccountQueryComplaintRequest request) throws YopClientException;

    /**
     * 会员银行账户开户结果查询
     * 
     * @return BankAccountQueryOpenResultResponse
     * @throws YopClientException if fails to make API call
     */
    BankAccountQueryOpenResultResponse bankAccountQueryOpenResult(BankAccountQueryOpenResultRequest request) throws YopClientException;

    /**
     * 会员银行账户交易流水查询
     * 
     * @return BankAccountQueryTradeResponse
     * @throws YopClientException if fails to make API call
     */
    BankAccountQueryTradeResponse bankAccountQueryTrade(BankAccountQueryTradeRequest request) throws YopClientException;

    /**
     * 会员银行账户提现查询
     * 
     * @return BankAccountQueryWithdrawResponse
     * @throws YopClientException if fails to make API call
     */
    BankAccountQueryWithdrawResponse bankAccountQueryWithdraw(BankAccountQueryWithdrawRequest request) throws YopClientException;

    /**
     * 会员银行账户开户发送短验
     * 
     * @return BankAccountSendMsgResponse
     * @throws YopClientException if fails to make API call
     */
    BankAccountSendMsgResponse bankAccountSendMsg(BankAccountSendMsgRequest request) throws YopClientException;

    /**
     * 会员银行账户关键字维护
     * 
     * @return BankAccountUpdateKeyWordsResponse
     * @throws YopClientException if fails to make API call
     */
    BankAccountUpdateKeyWordsResponse bankAccountUpdateKeyWords(BankAccountUpdateKeyWordsRequest request) throws YopClientException;

    /**
     * 会员银行账户提现
     * 
     * @return BankAccountWithdrawResponse
     * @throws YopClientException if fails to make API call
     */
    BankAccountWithdrawResponse bankAccountWithdraw(BankAccountWithdrawRequest request) throws YopClientException;

    /**
     * 会员银行账户开户确认
     * 
     * @return BankAccountConfirmV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BankAccountConfirmV10Response bank_account_confirm_v1_0(BankAccountConfirmV10Request request) throws YopClientException;

    /**
     * 会员银行账户开户接口
     * 
     * @return BankAccountOpenV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BankAccountOpenV10Response bank_account_open_v1_0(BankAccountOpenV10Request request) throws YopClientException;

    /**
     * 会员银行账户一分钱激活结果查询
     * 
     * @return BankAccountQueryActivationV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BankAccountQueryActivationV10Response bank_account_query_activation_v1_0(BankAccountQueryActivationV10Request request) throws YopClientException;

    /**
     * 会员银行账户客诉订单查询
     * 
     * @return BankAccountQueryComplaintV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BankAccountQueryComplaintV10Response bank_account_query_complaint_v1_0(BankAccountQueryComplaintV10Request request) throws YopClientException;

    /**
     * 会员银行账户开户结果查询
     * 
     * @return BankAccountQueryOpenResultV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BankAccountQueryOpenResultV10Response bank_account_query_open_result_v1_0(BankAccountQueryOpenResultV10Request request) throws YopClientException;

    /**
     * 会员银行账户交易流水查询
     * 
     * @return BankAccountQueryTradeV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BankAccountQueryTradeV10Response bank_account_query_trade_v1_0(BankAccountQueryTradeV10Request request) throws YopClientException;

    /**
     * 会员银行账户提现查询
     * 
     * @return BankAccountQueryWithdrawV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BankAccountQueryWithdrawV10Response bank_account_query_withdraw_v1_0(BankAccountQueryWithdrawV10Request request) throws YopClientException;

    /**
     * 会员银行账户开户发送短验
     * 
     * @return BankAccountSendMsgV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BankAccountSendMsgV10Response bank_account_send_msg_v1_0(BankAccountSendMsgV10Request request) throws YopClientException;

    /**
     * 会员银行账户关键字维护
     * 
     * @return BankAccountUpdateKeyWordsV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BankAccountUpdateKeyWordsV10Response bank_account_update_key_words_v1_0(BankAccountUpdateKeyWordsV10Request request) throws YopClientException;

    /**
     * 会员银行账户提现
     * 
     * @return BankAccountWithdrawV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BankAccountWithdrawV10Response bank_account_withdraw_v1_0(BankAccountWithdrawV10Request request) throws YopClientException;

    /**
     * 钱包账单详情查询
     * 商户通过调用该接口，查询账单详情信息
     * @return BillQueryDetailResponse
     * @throws YopClientException if fails to make API call
     */
    BillQueryDetailResponse billQueryDetail(BillQueryDetailRequest request) throws YopClientException;

    /**
     * 钱包账单列表查询
     * 
     * @return BillQueryListResponse
     * @throws YopClientException if fails to make API call
     */
    BillQueryListResponse billQueryList(BillQueryListRequest request) throws YopClientException;

    /**
     * 钱包账单列表查询
     * 商户通过调用该接口，查询账单列表信息
     * @return BillQueryListV2Response
     * @throws YopClientException if fails to make API call
     */
    BillQueryListV2Response billQueryListV2(BillQueryListV2Request request) throws YopClientException;

    /**
     * 钱包账单总览查询
     * 商户调用该接口，查询钱包账单总览信息
     * @return BillQueryOverviewResponse
     * @throws YopClientException if fails to make API call
     */
    BillQueryOverviewResponse billQueryOverview(BillQueryOverviewRequest request) throws YopClientException;

    /**
     * 钱包账单详情查询
     * 商户通过调用该接口，查询账单详情信息
     * @return BillQueryDetailResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BillQueryDetailResponse bill_query_detail(BillQueryDetailRequest request) throws YopClientException;

    /**
     * 钱包账单列表查询
     * 
     * @return BillQueryListResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BillQueryListResponse bill_query_list(BillQueryListRequest request) throws YopClientException;

    /**
     * 钱包账单总览查询
     * 商户调用该接口，查询钱包账单总览信息
     * @return BillQueryOverviewResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BillQueryOverviewResponse bill_query_overview(BillQueryOverviewRequest request) throws YopClientException;

    /**
     * 查询及绑定银行卡
     * 商户调用该接口，易宝返回url可跳转至对应用户的银行卡列表，此页面支持用户查询绑卡列表、绑定新卡和解绑已有银行卡。  绑定银行卡：用户在银行卡列表页申请绑定新卡，提交银行卡信息（包括姓银行卡号、手机号）申请绑定银行卡，完成银行卡信息认证；同时系统会根据认证渠道规则升级用户钱包账户。  解绑银行卡：用户在银行卡列表页申请解绑银行卡，确认支付密码完成解绑。
     * @return CardQueryResponse
     * @throws YopClientException if fails to make API call
     */
    CardQueryResponse cardQuery(CardQueryRequest request) throws YopClientException;

    /**
     * 查询及绑定银行卡
     * 商户调用该接口，易宝返回url可跳转至对应用户的银行卡列表，此页面支持用户查询绑卡列表、绑定新卡和解绑已有银行卡。  绑定银行卡：用户在银行卡列表页申请绑定新卡，提交银行卡信息（包括姓银行卡号、手机号）申请绑定银行卡，完成银行卡信息认证；同时系统会根据认证渠道规则升级用户钱包账户。  解绑银行卡：用户在银行卡列表页申请解绑银行卡，确认支付密码完成解绑。
     * @return CardQueryV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    CardQueryV10Response card_query_v1_0(CardQueryV10Request request) throws YopClientException;

    /**
     * 优惠券列表查询
     * 适配WEB3优惠券列表查询
     * @return CouponListQueryWeb3Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    CouponListQueryWeb3Response coupon_list_query_web3(CouponListQueryWeb3Request request) throws YopClientException;

    /**
     * 查询用户管理费扣费时间
     * 查询用户管理费扣费时间
     * @return ManageFeeQueryDeductResponse
     * @throws YopClientException if fails to make API call
     */
    ManageFeeQueryDeductResponse manageFeeQueryDeduct(ManageFeeQueryDeductRequest request) throws YopClientException;

    /**
     * 查询用户管理费扣费时间
     * 查询用户管理费扣费时间
     * @return ManageFeeQueryDeductResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    ManageFeeQueryDeductResponse manage_fee_query_deduct(ManageFeeQueryDeductRequest request) throws YopClientException;

    /**
     * 会员绑卡列表查询
     * 
     * @return MemberCardListResponse
     * @throws YopClientException if fails to make API call
     */
    MemberCardListResponse memberCardList(MemberCardListRequest request) throws YopClientException;

    /**
     * 钱包账户信息查询
     *  
     * @return MemberQueryResponse
     * @throws YopClientException if fails to make API call
     */
    MemberQueryResponse memberQuery(MemberQueryRequest request) throws YopClientException;

    /**
     * 会员绑卡列表查询
     * 
     * @return MemberCardListResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    MemberCardListResponse member_card_list(MemberCardListRequest request) throws YopClientException;

    /**
     * 钱包账户信息查询
     *  
     * @return MemberQueryV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    MemberQueryV10Response member_query_v1_0(MemberQueryV10Request request) throws YopClientException;

    /**
     * 安全设置
     * 商户调用此接口，跳转到易宝密码管理页面，此页面用于用户修改或重新设置钱包支付密码。
     * @return PasswordManageResponse
     * @throws YopClientException if fails to make API call
     */
    PasswordManageResponse passwordManage(PasswordManageRequest request) throws YopClientException;

    /**
     * 安全设置
     * 商户调用此接口，跳转到易宝密码管理页面，此页面用于用户修改或重新设置钱包支付密码。
     * @return PasswordManageV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    PasswordManageV10Response password_manage_v1_0(PasswordManageV10Request request) throws YopClientException;

    /**
     * 支付设置
     * 商户调用此接口，跳转到支付设置页面，此页面用于用户设置支付顺序。
     * @return PaymentManageWeb3V10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    PaymentManageWeb3V10Response payment_manage_web3_v1_0(PaymentManageWeb3V10Request request) throws YopClientException;

    /**
     * 发起充值
     * 用户在商户端发起充值，商户调用此接口并打开对应url跳转到易宝充值收银台，用户在收银台选择银行卡或绑定新卡并确认支付密码后完成充值。其中：充值的银行卡必须为借记卡即储蓄卡。
     * @return RechargeInitiateResponse
     * @throws YopClientException if fails to make API call
     */
    RechargeInitiateResponse rechargeInitiate(RechargeInitiateRequest request) throws YopClientException;

    /**
     * 充值查询
     * 商户通过请求该接口查询用户充值结果
     * @return RechargeQueryResponse
     * @throws YopClientException if fails to make API call
     */
    RechargeQueryResponse rechargeQuery(RechargeQueryRequest request) throws YopClientException;

    /**
     * 发起充值
     * 用户在商户端发起充值，商户调用此接口并打开对应url跳转到易宝充值收银台，用户在收银台选择银行卡或绑定新卡并确认支付密码后完成充值。其中：充值的银行卡必须为借记卡即储蓄卡。
     * @return RechargeInitiateV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    RechargeInitiateV10Response recharge_initiate_v1_0(RechargeInitiateV10Request request) throws YopClientException;

    /**
     * 充值查询
     * 商户通过请求该接口查询用户充值结果
     * @return RechargeQueryV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    RechargeQueryV10Response recharge_query_v1_0(RechargeQueryV10Request request) throws YopClientException;

    /**
     * 会员主体订阅有效期变更通知
     * 会员对应主体的订阅有效期变更通知
     * @return SubscribeExpireNotifyResponse
     * @throws YopClientException if fails to make API call
     */
    SubscribeExpireNotifyResponse subscribeExpireNotify(SubscribeExpireNotifyRequest request) throws YopClientException;

    /**
     * 会员主体订阅有效期变更通知
     * 会员对应主体的订阅有效期变更通知
     * @return SubscribeExpireNotifyResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    SubscribeExpireNotifyResponse subscribe_expire_notify(SubscribeExpireNotifyRequest request) throws YopClientException;

    /**
     * 会员自动扣款下单
     * 
     * @return TradeAutoDeductionCreateResponse
     * @throws YopClientException if fails to make API call
     */
    TradeAutoDeductionCreateResponse tradeAutoDeductionCreate(TradeAutoDeductionCreateRequest request) throws YopClientException;

    /**
     * 会员自动扣款查询
     * 
     * @return TradeAutoDeductionQueryResponse
     * @throws YopClientException if fails to make API call
     */
    TradeAutoDeductionQueryResponse tradeAutoDeductionQuery(TradeAutoDeductionQueryRequest request) throws YopClientException;

    /**
     * 钱包交易支付
     * 用户与商户之间发生商品交易行为，用户可使用钱包账户余额或绑定银行卡方式支付给商户，商户调用此接口完成支付行为。
     * @return TradeOrderResponse
     * @throws YopClientException if fails to make API call
     */
    TradeOrderResponse tradeOrder(TradeOrderRequest request) throws YopClientException;

    /**
     * 钱包交易下单
     * 用户与商户之间发生商品交易行为，用户可使用钱包账户余额或绑定银行卡方式支付给商户，商户调用此接口完成支付行为。
     * @return TradeOrderV2Response
     * @throws YopClientException if fails to make API call
     */
    TradeOrderV2Response tradeOrderV2(TradeOrderV2Request request) throws YopClientException;

    /**
     * 钱包交易支付
     * 用户与商户之间发生商品交易行为，用户可使用钱包账户余额或绑定银行卡方式支付给商户，商户调用此接口完成支付行为。
     * @return TradeOrderV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    TradeOrderV10Response trade_order_v1_0(TradeOrderV10Request request) throws YopClientException;

    /**
     * 钱包交易下单
     * 用户与商户之间发生商品交易行为，用户可使用钱包账户余额或绑定银行卡方式支付给商户，商户调用此接口完成支付行为。
     * @return TradeOrderV20Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    TradeOrderV20Response trade_order_v2_0(TradeOrderV20Request request) throws YopClientException;

    /**
     * 发起B2C转账
     * 商户与用户之间发生的交易行为，商户可向用户的钱包账户转账，商户调用此接口完成转账行为
     * @return TransferB2cInitiateResponse
     * @throws YopClientException if fails to make API call
     */
    TransferB2cInitiateResponse transferB2cInitiate(TransferB2cInitiateRequest request) throws YopClientException;

    /**
     * 营销红包转账
     * 商户与用户之间发生的交易行为，商户可向用户的钱包账户转账，商户调用此接口完成转账行为
     * @return TransferB2cMarketResponse
     * @throws YopClientException if fails to make API call
     */
    TransferB2cMarketResponse transferB2cMarket(TransferB2cMarketRequest request) throws YopClientException;

    /**
     * B2C转账查询
     * 商户通过请求该接口查询商户向用户转账的订单结果
     * @return TransferB2cQueryResponse
     * @throws YopClientException if fails to make API call
     */
    TransferB2cQueryResponse transferB2cQuery(TransferB2cQueryRequest request) throws YopClientException;

    /**
     * 发起B2C转账
     * 商户与用户之间发生的交易行为，商户可向用户的钱包账户转账，商户调用此接口完成转账行为
     * @return TransferB2cInitiateV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    TransferB2cInitiateV10Response transfer_b2c_initiate_v1_0(TransferB2cInitiateV10Request request) throws YopClientException;

    /**
     * 营销红包转账
     * 商户与用户之间发生的交易行为，商户可向用户的钱包账户转账，商户调用此接口完成转账行为
     * @return TransferB2cMarketV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    TransferB2cMarketV10Response transfer_b2c_market_v1_0(TransferB2cMarketV10Request request) throws YopClientException;

    /**
     * B2C转账查询
     * 商户通过请求该接口查询商户向用户转账的订单结果
     * @return TransferB2cQueryV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    TransferB2cQueryV10Response transfer_b2c_query_v1_0(TransferB2cQueryV10Request request) throws YopClientException;

    /**
     * 注销会员钱包
     * 商户注销会员钱包功能
     * @return WalletCancelResponse
     * @throws YopClientException if fails to make API call
     */
    WalletCancelResponse walletCancel(WalletCancelRequest request) throws YopClientException;

    /**
     * 钱包注册/登录接口
     * 通过该接口获取钱包注册/登陆页面
     * @return WalletIndexV2Response
     * @throws YopClientException if fails to make API call
     */
    WalletIndexV2Response walletIndexV2(WalletIndexV2Request request) throws YopClientException;

    /**
     * 注销会员钱包
     * 商户注销会员钱包功能
     * @return WalletCancelV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    WalletCancelV10Response wallet_cancel_v1_0(WalletCancelV10Request request) throws YopClientException;

    /**
     * 钱包注册/登录接口
     * 通过该接口获取钱包注册/登陆页面
     * @return WalletIndexV20Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    WalletIndexV20Response wallet_index_v2_0(WalletIndexV20Request request) throws YopClientException;

    /**
     * 协议通知商户
     * 协议通知商户
     * @return Web3AgreementNotifyResponse
     * @throws YopClientException if fails to make API call
     */
    Web3AgreementNotifyResponse web3AgreementNotify(Web3AgreementNotifyRequest request) throws YopClientException;

    /**
     * web3发起免密支付解约
     * 
     * @return Web3AgreementPaymentCancelResponse
     * @throws YopClientException if fails to make API call
     */
    Web3AgreementPaymentCancelResponse web3AgreementPaymentCancel(Web3AgreementPaymentCancelRequest request) throws YopClientException;

    /**
     * web3钱包免密支付协议查询接口
     * 钱包免密支付协议查询接口
     * @return Web3AgreementPaymentQueryResponse
     * @throws YopClientException if fails to make API call
     */
    Web3AgreementPaymentQueryResponse web3AgreementPaymentQuery(Web3AgreementPaymentQueryRequest request) throws YopClientException;

    /**
     * web3发起免密支付签约
     * 用户在商户端发起协议签约，商户调用此接口并打开对应url跳转到易宝验密签约页面，用户输入支付密码后验证并签约。
     * @return Web3AgreementPaymentSignResponse
     * @throws YopClientException if fails to make API call
     */
    Web3AgreementPaymentSignResponse web3AgreementPaymentSign(Web3AgreementPaymentSignRequest request) throws YopClientException;

    /**
     * 优惠券列表查询
     * 适配WEB3优惠券列表查询
     * @return Web3CouponListQueryResponse
     * @throws YopClientException if fails to make API call
     */
    Web3CouponListQueryResponse web3CouponListQuery(Web3CouponListQueryRequest request) throws YopClientException;

    /**
     * 支付设置
     * 商户调用此接口，跳转到支付设置页面，此页面用于用户设置支付顺序。
     * @return Web3PaymentManageResponse
     * @throws YopClientException if fails to make API call
     */
    Web3PaymentManageResponse web3PaymentManage(Web3PaymentManageRequest request) throws YopClientException;

    /**
     * 发起提现
     * 用户在商户端发起提现，商户调用此接口并打开对应url跳转到易宝提现收银台，用户在收银台选择银行卡或绑定新卡并确认支付密码后完成提现。其中：提现的银行卡必须为借记卡即储蓄卡。 提现到账类型说明（商户据此引导用户）： 1）实时到账，用户提交提现申请成功后，资金于（一小时以内）到账。 2）2小时到账，用户提交提现申请成功后，资金将于两小时后到账。 3）次日到账，用户提交提现申请成功后，资金将于次日7：00以后到账（例：周六发起周日7：00以后到账）
     * @return WithdrawInitiateResponse
     * @throws YopClientException if fails to make API call
     */
    WithdrawInitiateResponse withdrawInitiate(WithdrawInitiateRequest request) throws YopClientException;

    /**
     * 提现查询
     * 商户通过请求该接口查询用户提现信息
     * @return WithdrawQueryResponse
     * @throws YopClientException if fails to make API call
     */
    WithdrawQueryResponse withdrawQuery(WithdrawQueryRequest request) throws YopClientException;

    /**
     * 发起提现
     * 用户在商户端发起提现，商户调用此接口并打开对应url跳转到易宝提现收银台，用户在收银台选择银行卡或绑定新卡并确认支付密码后完成提现。其中：提现的银行卡必须为借记卡即储蓄卡。 提现到账类型说明（商户据此引导用户）： 1）实时到账，用户提交提现申请成功后，资金于（一小时以内）到账。 2）2小时到账，用户提交提现申请成功后，资金将于两小时后到账。 3）次日到账，用户提交提现申请成功后，资金将于次日7：00以后到账（例：周六发起周日7：00以后到账）
     * @return WithdrawInitiateV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    WithdrawInitiateV10Response withdraw_initiate_v1_0(WithdrawInitiateV10Request request) throws YopClientException;

    /**
     * 提现查询
     * 商户通过请求该接口查询用户提现信息
     * @return WithdrawQueryV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    WithdrawQueryV10Response withdraw_query_v1_0(WithdrawQueryV10Request request) throws YopClientException;


    /**
     * Shuts down this client object, releasing any resources that might be held open. This is an optional method, and
     * callers are not expected to call it, but can if they want to explicitly release any open resources. Once a client
     * has been shutdown, it should not be used to make any more requests.
     */
    void shutdown();
}
