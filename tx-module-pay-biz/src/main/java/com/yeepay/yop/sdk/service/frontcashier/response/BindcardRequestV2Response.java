/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.response;

import com.yeepay.yop.sdk.service.frontcashier.model.BindcardRequestV2OpenAuthBindCardResponseDTOResult;

public class BindcardRequestV2Response extends com.yeepay.yop.sdk.model.BaseResponse {
    private static final long serialVersionUID = 1L;

    private BindcardRequestV2OpenAuthBindCardResponseDTOResult result;

    public BindcardRequestV2OpenAuthBindCardResponseDTOResult getResult() {
        return result;
    }

    public void setResult(BindcardRequestV2OpenAuthBindCardResponseDTOResult result) {
        this.result = result;
    }

}
