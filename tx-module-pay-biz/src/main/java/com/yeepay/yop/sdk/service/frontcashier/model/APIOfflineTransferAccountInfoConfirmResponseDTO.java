/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 请修改我
 */
public class APIOfflineTransferAccountInfoConfirmResponseDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;div data-page-id&#x3D;\&quot;VAcYdB1hso405hxOgwDcyFkSnZg\&quot; data-lark-html-role&#x3D;\&quot;root\&quot; data-docx-has-block-data&#x3D;\&quot;false\&quot;&gt; &lt;div class&#x3D;\&quot;ace-line ace-line old-record-id-FxKAdYN6LoCso6xSJQgcz4A2nKh\&quot;&gt;返回码&lt;/div&gt; &lt;/div&gt;
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * &lt;p&gt;返回信息&lt;/p&gt;
   */
  @JsonProperty("msg")
  private String msg = null;

  /**
   * &lt;p&gt;入金状态&lt;/p&gt; &lt;p&gt;REFUND:入金退回&lt;/p&gt; &lt;p&gt;CONFIRM:入金确认&lt;/p&gt;
   */
  @JsonProperty("inAccountStatus")
  private String inAccountStatus = null;

  public APIOfflineTransferAccountInfoConfirmResponseDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * &lt;div data-page-id&#x3D;\&quot;VAcYdB1hso405hxOgwDcyFkSnZg\&quot; data-lark-html-role&#x3D;\&quot;root\&quot; data-docx-has-block-data&#x3D;\&quot;false\&quot;&gt; &lt;div class&#x3D;\&quot;ace-line ace-line old-record-id-FxKAdYN6LoCso6xSJQgcz4A2nKh\&quot;&gt;返回码&lt;/div&gt; &lt;/div&gt;
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public APIOfflineTransferAccountInfoConfirmResponseDTO msg(String msg) {
    this.msg = msg;
    return this;
  }

   /**
   * &lt;p&gt;返回信息&lt;/p&gt;
   * @return msg
  **/

  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }

  public APIOfflineTransferAccountInfoConfirmResponseDTO inAccountStatus(String inAccountStatus) {
    this.inAccountStatus = inAccountStatus;
    return this;
  }

   /**
   * &lt;p&gt;入金状态&lt;/p&gt; &lt;p&gt;REFUND:入金退回&lt;/p&gt; &lt;p&gt;CONFIRM:入金确认&lt;/p&gt;
   * @return inAccountStatus
  **/

  public String getInAccountStatus() {
    return inAccountStatus;
  }

  public void setInAccountStatus(String inAccountStatus) {
    this.inAccountStatus = inAccountStatus;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    APIOfflineTransferAccountInfoConfirmResponseDTO apIOfflineTransferAccountInfoConfirmResponseDTO = (APIOfflineTransferAccountInfoConfirmResponseDTO) o;
    return ObjectUtils.equals(this.code, apIOfflineTransferAccountInfoConfirmResponseDTO.code) &&
    ObjectUtils.equals(this.msg, apIOfflineTransferAccountInfoConfirmResponseDTO.msg) &&
    ObjectUtils.equals(this.inAccountStatus, apIOfflineTransferAccountInfoConfirmResponseDTO.inAccountStatus);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, msg, inAccountStatus);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class APIOfflineTransferAccountInfoConfirmResponseDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    msg: ").append(toIndentedString(msg)).append("\n");
    sb.append("    inAccountStatus: ").append(toIndentedString(inAccountStatus)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

