/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class BankAccountQueryTradeRequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<BankAccountQueryTradeRequest> {
    private final String serviceName = "MWallet";

    private final String resourcePath = "/rest/v1.0/m-wallet/bank-account/query-trade";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.GET;


    @Override
    public Request<BankAccountQueryTradeRequest> marshall(BankAccountQueryTradeRequest request) {
        Request<BankAccountQueryTradeRequest> internalRequest = new DefaultRequest<BankAccountQueryTradeRequest>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getMerchantMemberNo() != null) {
            internalRequest.addParameter("merchantMemberNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantMemberNo(), "String"));
        }
        if (request.getAccountNo() != null) {
            internalRequest.addParameter("accountNo", PrimitiveMarshallerUtils.marshalling(request.getAccountNo(), "String"));
        }
        if (request.getBeginDate() != null) {
            internalRequest.addParameter("beginDate", PrimitiveMarshallerUtils.marshalling(request.getBeginDate(), "String"));
        }
        if (request.getEndDate() != null) {
            internalRequest.addParameter("endDate", PrimitiveMarshallerUtils.marshalling(request.getEndDate(), "String"));
        }
        if (request.getParentMerchantNo() != null) {
            internalRequest.addParameter("parentMerchantNo", PrimitiveMarshallerUtils.marshalling(request.getParentMerchantNo(), "String"));
        }
        if (request.getMerchantNo() != null) {
            internalRequest.addParameter("merchantNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantNo(), "String"));
        }
        if (request.getLoanFlag() != null) {
            internalRequest.addParameter("loanFlag", PrimitiveMarshallerUtils.marshalling(request.getLoanFlag(), "String"));
        }
        if (request.getStartNum() != null) {
            internalRequest.addParameter("startNum", PrimitiveMarshallerUtils.marshalling(request.getStartNum(), "Integer"));
        }
        if (request.getQueryNum() != null) {
            internalRequest.addParameter("queryNum", PrimitiveMarshallerUtils.marshalling(request.getQueryNum(), "Integer"));
        }
        if (request.getQueryToken() != null) {
            internalRequest.addParameter("queryToken", PrimitiveMarshallerUtils.marshalling(request.getQueryToken(), "String"));
        }
        if (request.getQueryTime() != null) {
            internalRequest.addParameter("queryTime", PrimitiveMarshallerUtils.marshalling(request.getQueryTime(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static BankAccountQueryTradeRequestMarshaller INSTANCE = new BankAccountQueryTradeRequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static BankAccountQueryTradeRequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
