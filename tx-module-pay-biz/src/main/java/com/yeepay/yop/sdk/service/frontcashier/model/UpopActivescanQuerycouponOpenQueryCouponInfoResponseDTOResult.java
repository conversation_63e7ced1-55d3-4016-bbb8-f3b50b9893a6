/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * UpopActivescanQuerycouponOpenQueryCouponInfoResponseDTOResult
 */
public class UpopActivescanQuerycouponOpenQueryCouponInfoResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 错误码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 错误码描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 营销信息
   */
  @JsonProperty("couponInfo")
  private String couponInfo = null;

  public UpopActivescanQuerycouponOpenQueryCouponInfoResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 错误码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public UpopActivescanQuerycouponOpenQueryCouponInfoResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 错误码描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public UpopActivescanQuerycouponOpenQueryCouponInfoResponseDTOResult couponInfo(String couponInfo) {
    this.couponInfo = couponInfo;
    return this;
  }

   /**
   * 营销信息
   * @return couponInfo
  **/

  public String getCouponInfo() {
    return couponInfo;
  }

  public void setCouponInfo(String couponInfo) {
    this.couponInfo = couponInfo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    UpopActivescanQuerycouponOpenQueryCouponInfoResponseDTOResult upopActivescanQuerycouponOpenQueryCouponInfoResponseDTOResult = (UpopActivescanQuerycouponOpenQueryCouponInfoResponseDTOResult) o;
    return ObjectUtils.equals(this.code, upopActivescanQuerycouponOpenQueryCouponInfoResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, upopActivescanQuerycouponOpenQueryCouponInfoResponseDTOResult.message) &&
    ObjectUtils.equals(this.couponInfo, upopActivescanQuerycouponOpenQueryCouponInfoResponseDTOResult.couponInfo);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, couponInfo);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UpopActivescanQuerycouponOpenQueryCouponInfoResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    couponInfo: ").append(toIndentedString(couponInfo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

