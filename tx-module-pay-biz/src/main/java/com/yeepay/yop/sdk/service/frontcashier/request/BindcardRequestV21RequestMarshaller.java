/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class BindcardRequestV21RequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<BindcardRequestV21Request> {
    private final String serviceName = "Frontcashier";

    private final String resourcePath = "/rest/v2.1/frontcashier/bindcard/request";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.POST;


    @Override
    public Request<BindcardRequestV21Request> marshall(BindcardRequestV21Request request) {
        Request<BindcardRequestV21Request> internalRequest = new DefaultRequest<BindcardRequestV21Request>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getParentMerchantNo() != null) {
            internalRequest.addParameter("parentMerchantNo", PrimitiveMarshallerUtils.marshalling(request.getParentMerchantNo(), "String"));
        }
        if (request.getMerchantNo() != null) {
            internalRequest.addParameter("merchantNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantNo(), "String"));
        }
        if (request.getMerchantFlowId() != null) {
            internalRequest.addParameter("merchantFlowId", PrimitiveMarshallerUtils.marshalling(request.getMerchantFlowId(), "String"));
        }
        if (request.getUserNo() != null) {
            internalRequest.addParameter("userNo", PrimitiveMarshallerUtils.marshalling(request.getUserNo(), "String"));
        }
        if (request.getUserType() != null) {
            internalRequest.addParameter("userType", PrimitiveMarshallerUtils.marshalling(request.getUserType(), "String"));
        }
        if (request.getBankCardNo() != null) {
            internalRequest.addParameter("bankCardNo", PrimitiveMarshallerUtils.marshalling(request.getBankCardNo(), "String"));
        }
        if (request.getUserName() != null) {
            internalRequest.addParameter("userName", PrimitiveMarshallerUtils.marshalling(request.getUserName(), "String"));
        }
        if (request.getIdCardType() != null) {
            internalRequest.addParameter("idCardType", PrimitiveMarshallerUtils.marshalling(request.getIdCardType(), "String"));
        }
        if (request.getIdCardNo() != null) {
            internalRequest.addParameter("idCardNo", PrimitiveMarshallerUtils.marshalling(request.getIdCardNo(), "String"));
        }
        if (request.getPhone() != null) {
            internalRequest.addParameter("phone", PrimitiveMarshallerUtils.marshalling(request.getPhone(), "String"));
        }
        if (request.getCvv2() != null) {
            internalRequest.addParameter("cvv2", PrimitiveMarshallerUtils.marshalling(request.getCvv2(), "String"));
        }
        if (request.getValidthru() != null) {
            internalRequest.addParameter("validthru", PrimitiveMarshallerUtils.marshalling(request.getValidthru(), "String"));
        }
        if (request.getOrderValidate() != null) {
            internalRequest.addParameter("orderValidate", PrimitiveMarshallerUtils.marshalling(request.getOrderValidate(), "Integer"));
        }
        if (request.getAuthType() != null) {
            internalRequest.addParameter("authType", PrimitiveMarshallerUtils.marshalling(request.getAuthType(), "String"));
        }
        if (request.getCardType() != null) {
            internalRequest.addParameter("cardType", PrimitiveMarshallerUtils.marshalling(request.getCardType(), "String"));
        }
        if (request.getIsSMS() != null) {
            internalRequest.addParameter("isSMS", PrimitiveMarshallerUtils.marshalling(request.getIsSMS(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static BindcardRequestV21RequestMarshaller INSTANCE = new BindcardRequestV21RequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static BindcardRequestV21RequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
