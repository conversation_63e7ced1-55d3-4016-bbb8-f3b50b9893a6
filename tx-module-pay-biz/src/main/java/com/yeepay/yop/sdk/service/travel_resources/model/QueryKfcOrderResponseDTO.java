/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.travel_resources.model.QuerKfcOrderDataBeanDTO;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * kfc订单查询结果
 */
public class QueryKfcOrderResponseDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;订单状态需要关注orderStatus&lt;/p&gt;
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 
   */
  @JsonProperty("data")
  private QuerKfcOrderDataBeanDTO data = null;

  /**
   * &lt;pre&gt;订单状态:&lt;/pre&gt; &lt;pre&gt;0:待支付&lt;/pre&gt; &lt;pre&gt;1:待出货（已支付）&lt;/pre&gt; &lt;pre&gt;2:已出货&lt;/pre&gt; &lt;pre&gt;3:已取消&lt;/pre&gt;
   */
  @JsonProperty("orderStatus")
  private String orderStatus = null;

  /**
   * &lt;pre&gt;供应商渠道&lt;/pre&gt;
   */
  @JsonProperty("supplierChannel")
  private String supplierChannel = null;

  /**
   * 
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * &lt;pre&gt;支付请求流水号&lt;/pre&gt;
   */
  @JsonProperty("paymentUniqueOrderNo")
  private String paymentUniqueOrderNo = null;

  /**
   * &lt;pre&gt;订单金额&lt;/pre&gt;
   */
  @JsonProperty("orderAmount")
  private BigDecimal orderAmount = null;

  /**
   * &lt;pre&gt;完成时间。订单出货成功时返回&lt;/pre&gt;
   */
  @JsonProperty("complateTime")
  private String complateTime = null;

  /**
   * &lt;pre&gt;取消时间&lt;/pre&gt;
   */
  @JsonProperty("cancelTime")
  private String cancelTime = null;

  /**
   * &lt;pre&gt;下单成功时间。下单成功时返回&lt;/pre&gt;
   */
  @JsonProperty("orderSuccessTime")
  private String orderSuccessTime = null;

  /**
   * &lt;pre&gt;系统返回唯一订单号&lt;/pre&gt;
   */
  @JsonProperty("systemOrderNo")
  private String systemOrderNo = null;

  /**
   * &lt;pre&gt;支付成功时间。支付成功时返回&lt;/pre&gt;
   */
  @JsonProperty("paySuccessTime")
  private String paySuccessTime = null;

  /**
   * &lt;p&gt;请求商编&lt;/p&gt;
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;p&gt;商户订单号&lt;/p&gt;
   */
  @JsonProperty("merchantRequestNo")
  private String merchantRequestNo = null;

  /**
   * &lt;pre&gt;请求方订单号&lt;/pre&gt;
   */
  @JsonProperty("parentMerchantRequestNo")
  private String parentMerchantRequestNo = null;

  /**
   * &lt;pre&gt;资源方订单号&lt;/pre&gt;
   */
  @JsonProperty("supplierOrderNo")
  private String supplierOrderNo = null;

  /**
   * &lt;pre&gt;在字段用来表示通知类型是交易还是退款。&lt;/pre&gt; &lt;pre&gt;只有在异步通知结果里该参数才有值&lt;/pre&gt; &lt;pre&gt;&amp;nbsp;&lt;/pre&gt;
   */
  @JsonProperty("trxType")
  private String trxType = null;

  public QueryKfcOrderResponseDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * &lt;p&gt;订单状态需要关注orderStatus&lt;/p&gt;
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public QueryKfcOrderResponseDTO data(QuerKfcOrderDataBeanDTO data) {
    this.data = data;
    return this;
  }

   /**
   * Get data
   * @return data
  **/

  public QuerKfcOrderDataBeanDTO getData() {
    return data;
  }

  public void setData(QuerKfcOrderDataBeanDTO data) {
    this.data = data;
  }

  public QueryKfcOrderResponseDTO orderStatus(String orderStatus) {
    this.orderStatus = orderStatus;
    return this;
  }

   /**
   * &lt;pre&gt;订单状态:&lt;/pre&gt; &lt;pre&gt;0:待支付&lt;/pre&gt; &lt;pre&gt;1:待出货（已支付）&lt;/pre&gt; &lt;pre&gt;2:已出货&lt;/pre&gt; &lt;pre&gt;3:已取消&lt;/pre&gt;
   * @return orderStatus
  **/

  public String getOrderStatus() {
    return orderStatus;
  }

  public void setOrderStatus(String orderStatus) {
    this.orderStatus = orderStatus;
  }

  public QueryKfcOrderResponseDTO supplierChannel(String supplierChannel) {
    this.supplierChannel = supplierChannel;
    return this;
  }

   /**
   * &lt;pre&gt;供应商渠道&lt;/pre&gt;
   * @return supplierChannel
  **/

  public String getSupplierChannel() {
    return supplierChannel;
  }

  public void setSupplierChannel(String supplierChannel) {
    this.supplierChannel = supplierChannel;
  }

  public QueryKfcOrderResponseDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * Get message
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public QueryKfcOrderResponseDTO paymentUniqueOrderNo(String paymentUniqueOrderNo) {
    this.paymentUniqueOrderNo = paymentUniqueOrderNo;
    return this;
  }

   /**
   * &lt;pre&gt;支付请求流水号&lt;/pre&gt;
   * @return paymentUniqueOrderNo
  **/

  public String getPaymentUniqueOrderNo() {
    return paymentUniqueOrderNo;
  }

  public void setPaymentUniqueOrderNo(String paymentUniqueOrderNo) {
    this.paymentUniqueOrderNo = paymentUniqueOrderNo;
  }

  public QueryKfcOrderResponseDTO orderAmount(BigDecimal orderAmount) {
    this.orderAmount = orderAmount;
    return this;
  }

   /**
   * &lt;pre&gt;订单金额&lt;/pre&gt;
   * @return orderAmount
  **/

  public BigDecimal getOrderAmount() {
    return orderAmount;
  }

  public void setOrderAmount(BigDecimal orderAmount) {
    this.orderAmount = orderAmount;
  }

  public QueryKfcOrderResponseDTO complateTime(String complateTime) {
    this.complateTime = complateTime;
    return this;
  }

   /**
   * &lt;pre&gt;完成时间。订单出货成功时返回&lt;/pre&gt;
   * @return complateTime
  **/

  public String getComplateTime() {
    return complateTime;
  }

  public void setComplateTime(String complateTime) {
    this.complateTime = complateTime;
  }

  public QueryKfcOrderResponseDTO cancelTime(String cancelTime) {
    this.cancelTime = cancelTime;
    return this;
  }

   /**
   * &lt;pre&gt;取消时间&lt;/pre&gt;
   * @return cancelTime
  **/

  public String getCancelTime() {
    return cancelTime;
  }

  public void setCancelTime(String cancelTime) {
    this.cancelTime = cancelTime;
  }

  public QueryKfcOrderResponseDTO orderSuccessTime(String orderSuccessTime) {
    this.orderSuccessTime = orderSuccessTime;
    return this;
  }

   /**
   * &lt;pre&gt;下单成功时间。下单成功时返回&lt;/pre&gt;
   * @return orderSuccessTime
  **/

  public String getOrderSuccessTime() {
    return orderSuccessTime;
  }

  public void setOrderSuccessTime(String orderSuccessTime) {
    this.orderSuccessTime = orderSuccessTime;
  }

  public QueryKfcOrderResponseDTO systemOrderNo(String systemOrderNo) {
    this.systemOrderNo = systemOrderNo;
    return this;
  }

   /**
   * &lt;pre&gt;系统返回唯一订单号&lt;/pre&gt;
   * @return systemOrderNo
  **/

  public String getSystemOrderNo() {
    return systemOrderNo;
  }

  public void setSystemOrderNo(String systemOrderNo) {
    this.systemOrderNo = systemOrderNo;
  }

  public QueryKfcOrderResponseDTO paySuccessTime(String paySuccessTime) {
    this.paySuccessTime = paySuccessTime;
    return this;
  }

   /**
   * &lt;pre&gt;支付成功时间。支付成功时返回&lt;/pre&gt;
   * @return paySuccessTime
  **/

  public String getPaySuccessTime() {
    return paySuccessTime;
  }

  public void setPaySuccessTime(String paySuccessTime) {
    this.paySuccessTime = paySuccessTime;
  }

  public QueryKfcOrderResponseDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;请求商编&lt;/p&gt;
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public QueryKfcOrderResponseDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public QueryKfcOrderResponseDTO merchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;商户订单号&lt;/p&gt;
   * @return merchantRequestNo
  **/

  public String getMerchantRequestNo() {
    return merchantRequestNo;
  }

  public void setMerchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
  }

  public QueryKfcOrderResponseDTO parentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
    return this;
  }

   /**
   * &lt;pre&gt;请求方订单号&lt;/pre&gt;
   * @return parentMerchantRequestNo
  **/

  public String getParentMerchantRequestNo() {
    return parentMerchantRequestNo;
  }

  public void setParentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
  }

  public QueryKfcOrderResponseDTO supplierOrderNo(String supplierOrderNo) {
    this.supplierOrderNo = supplierOrderNo;
    return this;
  }

   /**
   * &lt;pre&gt;资源方订单号&lt;/pre&gt;
   * @return supplierOrderNo
  **/

  public String getSupplierOrderNo() {
    return supplierOrderNo;
  }

  public void setSupplierOrderNo(String supplierOrderNo) {
    this.supplierOrderNo = supplierOrderNo;
  }

  public QueryKfcOrderResponseDTO trxType(String trxType) {
    this.trxType = trxType;
    return this;
  }

   /**
   * &lt;pre&gt;在字段用来表示通知类型是交易还是退款。&lt;/pre&gt; &lt;pre&gt;只有在异步通知结果里该参数才有值&lt;/pre&gt; &lt;pre&gt;&amp;nbsp;&lt;/pre&gt;
   * @return trxType
  **/

  public String getTrxType() {
    return trxType;
  }

  public void setTrxType(String trxType) {
    this.trxType = trxType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QueryKfcOrderResponseDTO queryKfcOrderResponseDTO = (QueryKfcOrderResponseDTO) o;
    return ObjectUtils.equals(this.code, queryKfcOrderResponseDTO.code) &&
    ObjectUtils.equals(this.data, queryKfcOrderResponseDTO.data) &&
    ObjectUtils.equals(this.orderStatus, queryKfcOrderResponseDTO.orderStatus) &&
    ObjectUtils.equals(this.supplierChannel, queryKfcOrderResponseDTO.supplierChannel) &&
    ObjectUtils.equals(this.message, queryKfcOrderResponseDTO.message) &&
    ObjectUtils.equals(this.paymentUniqueOrderNo, queryKfcOrderResponseDTO.paymentUniqueOrderNo) &&
    ObjectUtils.equals(this.orderAmount, queryKfcOrderResponseDTO.orderAmount) &&
    ObjectUtils.equals(this.complateTime, queryKfcOrderResponseDTO.complateTime) &&
    ObjectUtils.equals(this.cancelTime, queryKfcOrderResponseDTO.cancelTime) &&
    ObjectUtils.equals(this.orderSuccessTime, queryKfcOrderResponseDTO.orderSuccessTime) &&
    ObjectUtils.equals(this.systemOrderNo, queryKfcOrderResponseDTO.systemOrderNo) &&
    ObjectUtils.equals(this.paySuccessTime, queryKfcOrderResponseDTO.paySuccessTime) &&
    ObjectUtils.equals(this.parentMerchantNo, queryKfcOrderResponseDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, queryKfcOrderResponseDTO.merchantNo) &&
    ObjectUtils.equals(this.merchantRequestNo, queryKfcOrderResponseDTO.merchantRequestNo) &&
    ObjectUtils.equals(this.parentMerchantRequestNo, queryKfcOrderResponseDTO.parentMerchantRequestNo) &&
    ObjectUtils.equals(this.supplierOrderNo, queryKfcOrderResponseDTO.supplierOrderNo) &&
    ObjectUtils.equals(this.trxType, queryKfcOrderResponseDTO.trxType);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, data, orderStatus, supplierChannel, message, paymentUniqueOrderNo, orderAmount, complateTime, cancelTime, orderSuccessTime, systemOrderNo, paySuccessTime, parentMerchantNo, merchantNo, merchantRequestNo, parentMerchantRequestNo, supplierOrderNo, trxType);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryKfcOrderResponseDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    data: ").append(toIndentedString(data)).append("\n");
    sb.append("    orderStatus: ").append(toIndentedString(orderStatus)).append("\n");
    sb.append("    supplierChannel: ").append(toIndentedString(supplierChannel)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    paymentUniqueOrderNo: ").append(toIndentedString(paymentUniqueOrderNo)).append("\n");
    sb.append("    orderAmount: ").append(toIndentedString(orderAmount)).append("\n");
    sb.append("    complateTime: ").append(toIndentedString(complateTime)).append("\n");
    sb.append("    cancelTime: ").append(toIndentedString(cancelTime)).append("\n");
    sb.append("    orderSuccessTime: ").append(toIndentedString(orderSuccessTime)).append("\n");
    sb.append("    systemOrderNo: ").append(toIndentedString(systemOrderNo)).append("\n");
    sb.append("    paySuccessTime: ").append(toIndentedString(paySuccessTime)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantRequestNo: ").append(toIndentedString(merchantRequestNo)).append("\n");
    sb.append("    parentMerchantRequestNo: ").append(toIndentedString(parentMerchantRequestNo)).append("\n");
    sb.append("    supplierOrderNo: ").append(toIndentedString(supplierOrderNo)).append("\n");
    sb.append("    trxType: ").append(toIndentedString(trxType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

