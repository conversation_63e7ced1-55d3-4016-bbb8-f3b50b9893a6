/*
 * 公益-月捐
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.month_donate.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户签约信息列表
 */
public class MonthDonateSignDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;签约号&lt;/p&gt;
   */
  @JsonProperty("signatureNo")
  private String signatureNo = null;

  /**
   * &lt;p&gt;&lt;/p&gt; &lt;div&gt; &lt;pre&gt;WECHAT 微信支付&lt;/pre&gt; &lt;div&gt; &lt;pre&gt;ONEKEY 一键支付&lt;/pre&gt; &lt;div&gt; &lt;pre&gt;DONATE_CARD_PAY 慈善卡支付&lt;/pre&gt; &lt;/div&gt; &lt;/div&gt; &lt;/div&gt;
   */
  @JsonProperty("payChannel")
  private String payChannel = null;

  /**
   * &lt;p&gt;用户ID&lt;/p&gt;
   */
  @JsonProperty("userId")
  private Long userId = null;

  /**
   * &lt;p&gt;项目ID&lt;/p&gt;
   */
  @JsonProperty("projectId")
  private Long projectId = null;

  /**
   * &lt;p&gt;签约渠道：&lt;/p&gt; &lt;div&gt; &lt;pre&gt;WECHAT_ACCOUNT 微信公众号&lt;/pre&gt; &lt;div&gt; &lt;pre&gt;WECHAT_MINI_PROGRAM 微信小程序&lt;/pre&gt; &lt;div&gt; &lt;pre&gt;BIND_CARD 绑卡&lt;/pre&gt; &lt;/div&gt; &lt;/div&gt; &lt;/div&gt;
   */
  @JsonProperty("signChannel")
  private String signChannel = null;

  public MonthDonateSignDTO signatureNo(String signatureNo) {
    this.signatureNo = signatureNo;
    return this;
  }

   /**
   * &lt;p&gt;签约号&lt;/p&gt;
   * @return signatureNo
  **/

  public String getSignatureNo() {
    return signatureNo;
  }

  public void setSignatureNo(String signatureNo) {
    this.signatureNo = signatureNo;
  }

  public MonthDonateSignDTO payChannel(String payChannel) {
    this.payChannel = payChannel;
    return this;
  }

   /**
   * &lt;p&gt;&lt;/p&gt; &lt;div&gt; &lt;pre&gt;WECHAT 微信支付&lt;/pre&gt; &lt;div&gt; &lt;pre&gt;ONEKEY 一键支付&lt;/pre&gt; &lt;div&gt; &lt;pre&gt;DONATE_CARD_PAY 慈善卡支付&lt;/pre&gt; &lt;/div&gt; &lt;/div&gt; &lt;/div&gt;
   * @return payChannel
  **/

  public String getPayChannel() {
    return payChannel;
  }

  public void setPayChannel(String payChannel) {
    this.payChannel = payChannel;
  }

  public MonthDonateSignDTO userId(Long userId) {
    this.userId = userId;
    return this;
  }

   /**
   * &lt;p&gt;用户ID&lt;/p&gt;
   * @return userId
  **/

  public Long getUserId() {
    return userId;
  }

  public void setUserId(Long userId) {
    this.userId = userId;
  }

  public MonthDonateSignDTO projectId(Long projectId) {
    this.projectId = projectId;
    return this;
  }

   /**
   * &lt;p&gt;项目ID&lt;/p&gt;
   * @return projectId
  **/

  public Long getProjectId() {
    return projectId;
  }

  public void setProjectId(Long projectId) {
    this.projectId = projectId;
  }

  public MonthDonateSignDTO signChannel(String signChannel) {
    this.signChannel = signChannel;
    return this;
  }

   /**
   * &lt;p&gt;签约渠道：&lt;/p&gt; &lt;div&gt; &lt;pre&gt;WECHAT_ACCOUNT 微信公众号&lt;/pre&gt; &lt;div&gt; &lt;pre&gt;WECHAT_MINI_PROGRAM 微信小程序&lt;/pre&gt; &lt;div&gt; &lt;pre&gt;BIND_CARD 绑卡&lt;/pre&gt; &lt;/div&gt; &lt;/div&gt; &lt;/div&gt;
   * @return signChannel
  **/

  public String getSignChannel() {
    return signChannel;
  }

  public void setSignChannel(String signChannel) {
    this.signChannel = signChannel;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    MonthDonateSignDTO monthDonateSignDTO = (MonthDonateSignDTO) o;
    return ObjectUtils.equals(this.signatureNo, monthDonateSignDTO.signatureNo) &&
    ObjectUtils.equals(this.payChannel, monthDonateSignDTO.payChannel) &&
    ObjectUtils.equals(this.userId, monthDonateSignDTO.userId) &&
    ObjectUtils.equals(this.projectId, monthDonateSignDTO.projectId) &&
    ObjectUtils.equals(this.signChannel, monthDonateSignDTO.signChannel);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(signatureNo, payChannel, userId, projectId, signChannel);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MonthDonateSignDTO {\n");
    
    sb.append("    signatureNo: ").append(toIndentedString(signatureNo)).append("\n");
    sb.append("    payChannel: ").append(toIndentedString(payChannel)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    projectId: ").append(toIndentedString(projectId)).append("\n");
    sb.append("    signChannel: ").append(toIndentedString(signChannel)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

