/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 查询电影订单券信息
 */
public class QueryCinemaOrderCouponBeanDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;pre&gt;券码id&lt;/pre&gt;
   */
  @JsonProperty("couponCodeId")
  private String couponCodeId = null;

  /**
   * &lt;pre&gt;券码url&lt;/pre&gt;
   */
  @JsonProperty("couponCodeImageUrl")
  private String couponCodeImageUrl = null;

  /**
   * &lt;pre&gt;是否已调座&lt;/pre&gt;
   */
  @JsonProperty("adjusted")
  private Boolean adjusted = null;

  /**
   * &lt;p&gt;券码&lt;/p&gt;
   */
  @JsonProperty("couponCode")
  private String couponCode = null;

  /**
   * &lt;p&gt;验证码&lt;/p&gt;
   */
  @JsonProperty("validCode")
  private String validCode = null;

  /**
   * &lt;p&gt;出票的座位&lt;/p&gt;
   */
  @JsonProperty("seats")
  private String seats = null;

  public QueryCinemaOrderCouponBeanDTO couponCodeId(String couponCodeId) {
    this.couponCodeId = couponCodeId;
    return this;
  }

   /**
   * &lt;pre&gt;券码id&lt;/pre&gt;
   * @return couponCodeId
  **/

  public String getCouponCodeId() {
    return couponCodeId;
  }

  public void setCouponCodeId(String couponCodeId) {
    this.couponCodeId = couponCodeId;
  }

  public QueryCinemaOrderCouponBeanDTO couponCodeImageUrl(String couponCodeImageUrl) {
    this.couponCodeImageUrl = couponCodeImageUrl;
    return this;
  }

   /**
   * &lt;pre&gt;券码url&lt;/pre&gt;
   * @return couponCodeImageUrl
  **/

  public String getCouponCodeImageUrl() {
    return couponCodeImageUrl;
  }

  public void setCouponCodeImageUrl(String couponCodeImageUrl) {
    this.couponCodeImageUrl = couponCodeImageUrl;
  }

  public QueryCinemaOrderCouponBeanDTO adjusted(Boolean adjusted) {
    this.adjusted = adjusted;
    return this;
  }

   /**
   * &lt;pre&gt;是否已调座&lt;/pre&gt;
   * @return adjusted
  **/

  public Boolean isAdjusted() {
    return adjusted;
  }

  public void setAdjusted(Boolean adjusted) {
    this.adjusted = adjusted;
  }

  public QueryCinemaOrderCouponBeanDTO couponCode(String couponCode) {
    this.couponCode = couponCode;
    return this;
  }

   /**
   * &lt;p&gt;券码&lt;/p&gt;
   * @return couponCode
  **/

  public String getCouponCode() {
    return couponCode;
  }

  public void setCouponCode(String couponCode) {
    this.couponCode = couponCode;
  }

  public QueryCinemaOrderCouponBeanDTO validCode(String validCode) {
    this.validCode = validCode;
    return this;
  }

   /**
   * &lt;p&gt;验证码&lt;/p&gt;
   * @return validCode
  **/

  public String getValidCode() {
    return validCode;
  }

  public void setValidCode(String validCode) {
    this.validCode = validCode;
  }

  public QueryCinemaOrderCouponBeanDTO seats(String seats) {
    this.seats = seats;
    return this;
  }

   /**
   * &lt;p&gt;出票的座位&lt;/p&gt;
   * @return seats
  **/

  public String getSeats() {
    return seats;
  }

  public void setSeats(String seats) {
    this.seats = seats;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QueryCinemaOrderCouponBeanDTO queryCinemaOrderCouponBeanDTO = (QueryCinemaOrderCouponBeanDTO) o;
    return ObjectUtils.equals(this.couponCodeId, queryCinemaOrderCouponBeanDTO.couponCodeId) &&
    ObjectUtils.equals(this.couponCodeImageUrl, queryCinemaOrderCouponBeanDTO.couponCodeImageUrl) &&
    ObjectUtils.equals(this.adjusted, queryCinemaOrderCouponBeanDTO.adjusted) &&
    ObjectUtils.equals(this.couponCode, queryCinemaOrderCouponBeanDTO.couponCode) &&
    ObjectUtils.equals(this.validCode, queryCinemaOrderCouponBeanDTO.validCode) &&
    ObjectUtils.equals(this.seats, queryCinemaOrderCouponBeanDTO.seats);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(couponCodeId, couponCodeImageUrl, adjusted, couponCode, validCode, seats);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryCinemaOrderCouponBeanDTO {\n");
    
    sb.append("    couponCodeId: ").append(toIndentedString(couponCodeId)).append("\n");
    sb.append("    couponCodeImageUrl: ").append(toIndentedString(couponCodeImageUrl)).append("\n");
    sb.append("    adjusted: ").append(toIndentedString(adjusted)).append("\n");
    sb.append("    couponCode: ").append(toIndentedString(couponCode)).append("\n");
    sb.append("    validCode: ").append(toIndentedString(validCode)).append("\n");
    sb.append("    seats: ").append(toIndentedString(seats)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

