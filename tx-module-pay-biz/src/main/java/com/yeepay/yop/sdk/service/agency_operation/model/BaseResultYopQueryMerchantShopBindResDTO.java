/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.agency_operation.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.agency_operation.model.YopQueryMerchantShopBindResDTO;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 门店绑定查询结果
 */
public class BaseResultYopQueryMerchantShopBindResDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;msg&lt;/p&gt;
   */
  @JsonProperty("msg")
  private String msg = null;

  /**
   * &lt;p&gt;返回code&lt;/p&gt;
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 
   */
  @JsonProperty("data")
  private YopQueryMerchantShopBindResDTO data = null;

  public BaseResultYopQueryMerchantShopBindResDTO msg(String msg) {
    this.msg = msg;
    return this;
  }

   /**
   * &lt;p&gt;msg&lt;/p&gt;
   * @return msg
  **/

  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }

  public BaseResultYopQueryMerchantShopBindResDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * &lt;p&gt;返回code&lt;/p&gt;
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BaseResultYopQueryMerchantShopBindResDTO data(YopQueryMerchantShopBindResDTO data) {
    this.data = data;
    return this;
  }

   /**
   * Get data
   * @return data
  **/

  public YopQueryMerchantShopBindResDTO getData() {
    return data;
  }

  public void setData(YopQueryMerchantShopBindResDTO data) {
    this.data = data;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BaseResultYopQueryMerchantShopBindResDTO baseResultYopQueryMerchantShopBindResDTO = (BaseResultYopQueryMerchantShopBindResDTO) o;
    return ObjectUtils.equals(this.msg, baseResultYopQueryMerchantShopBindResDTO.msg) &&
    ObjectUtils.equals(this.code, baseResultYopQueryMerchantShopBindResDTO.code) &&
    ObjectUtils.equals(this.data, baseResultYopQueryMerchantShopBindResDTO.data);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(msg, code, data);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BaseResultYopQueryMerchantShopBindResDTO {\n");
    
    sb.append("    msg: ").append(toIndentedString(msg)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    data: ").append(toIndentedString(data)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

