/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class UpopActivescanQuerycouponRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String merchantFlowId;

    private String paySerialNo;

    private BigDecimal tradeAmount;

    private Long bindId;

    private String userNo;

    private String userType;

    private String riskInfoDeviceID;

    private String riskInfoDeviceType;

    private String riskInfoAccountIDHash;

    private String riskInfoSourceIP;


    /**
     * 
     * @return merchantFlowId
     **/
    
    public String getMerchantFlowId() {
        return merchantFlowId;
    }

    public void setMerchantFlowId(String merchantFlowId) {
        this.merchantFlowId = merchantFlowId;
    }

    /**
     * 
     * @return paySerialNo
     **/
    
    public String getPaySerialNo() {
        return paySerialNo;
    }

    public void setPaySerialNo(String paySerialNo) {
        this.paySerialNo = paySerialNo;
    }

    /**
     * 99.99
     * @return tradeAmount
     **/
    
    public BigDecimal getTradeAmount() {
        return tradeAmount;
    }

    public void setTradeAmount(BigDecimal tradeAmount) {
        this.tradeAmount = tradeAmount;
    }

    /**
     * 
     * @return bindId
     **/
    
    public Long getBindId() {
        return bindId;
    }

    public void setBindId(Long bindId) {
        this.bindId = bindId;
    }

    /**
     * 
     * @return userNo
     **/
    
    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    /**
     * 用户标识类型&lt;br&gt;可选项如下:&lt;br&gt;USER_ID:用户ID&lt;br&gt;IMEI:imei&lt;br&gt;MAC:网卡地址&lt;br&gt;EMAIL:用户注册地址&lt;br&gt;PHONE:用户注册手机号&lt;br&gt;ID_CARD:用户身份证号&lt;br&gt;AGREEMENT_NO:用户纸质订单协议号&lt;br&gt;WECHAT:微信号
     * @return userType
     **/
    
    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    /**
     * 
     * @return riskInfoDeviceID
     **/
    
    public String getRiskInfoDeviceID() {
        return riskInfoDeviceID;
    }

    public void setRiskInfoDeviceID(String riskInfoDeviceID) {
        this.riskInfoDeviceID = riskInfoDeviceID;
    }

    /**
     * 设备类型&lt;br&gt;可选项如下:&lt;br&gt;PHONE:手机&lt;br&gt;TABLET:平板&lt;br&gt;PC:电脑&lt;br&gt;WATCH:只能手表
     * @return riskInfoDeviceType
     **/
    
    public String getRiskInfoDeviceType() {
        return riskInfoDeviceType;
    }

    public void setRiskInfoDeviceType(String riskInfoDeviceType) {
        this.riskInfoDeviceType = riskInfoDeviceType;
    }

    /**
     * 应用提供方下的用户登录账号的hash值
     * @return riskInfoAccountIDHash
     **/
    
    public String getRiskInfoAccountIDHash() {
        return riskInfoAccountIDHash;
    }

    public void setRiskInfoAccountIDHash(String riskInfoAccountIDHash) {
        this.riskInfoAccountIDHash = riskInfoAccountIDHash;
    }

    /**
     * 
     * @return riskInfoSourceIP
     **/
    
    public String getRiskInfoSourceIP() {
        return riskInfoSourceIP;
    }

    public void setRiskInfoSourceIP(String riskInfoSourceIP) {
        this.riskInfoSourceIP = riskInfoSourceIP;
    }

    @Override
    public String getOperationId() {
        return "upopActivescanQuerycoupon";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
