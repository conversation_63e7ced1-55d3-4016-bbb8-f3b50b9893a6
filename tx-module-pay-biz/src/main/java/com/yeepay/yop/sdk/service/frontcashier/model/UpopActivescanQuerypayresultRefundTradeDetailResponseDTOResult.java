/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 */
public class UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 付款订单号
   */
  @JsonProperty("payOrderNo")
  private String payOrderNo = null;

  /**
   * 退款金额
   */
  @JsonProperty("refundAmount")
  private BigDecimal refundAmount = null;

  /**
   * 退款订单号
   */
  @JsonProperty("refundOrderNo")
  private String refundOrderNo = null;

  /**
   * 退款状态
   */
  @JsonProperty("status")
  private String status = null;

  /**
   * 退款时间
   */
  @JsonProperty("createTime")
  private String createTime = null;

  /**
   * 退款营销信息
   */
  @JsonProperty("refundCouponInfo")
  private String refundCouponInfo = null;

  /**
   * 申请退款金额（包含营销金额）
   */
  @JsonProperty("refundOrigTxnAmt")
  private BigDecimal refundOrigTxnAmt = null;

  public UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult payOrderNo(String payOrderNo) {
    this.payOrderNo = payOrderNo;
    return this;
  }

   /**
   * 付款订单号
   * @return payOrderNo
  **/

  public String getPayOrderNo() {
    return payOrderNo;
  }

  public void setPayOrderNo(String payOrderNo) {
    this.payOrderNo = payOrderNo;
  }

  public UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult refundAmount(BigDecimal refundAmount) {
    this.refundAmount = refundAmount;
    return this;
  }

   /**
   * 退款金额
   * @return refundAmount
  **/

  public BigDecimal getRefundAmount() {
    return refundAmount;
  }

  public void setRefundAmount(BigDecimal refundAmount) {
    this.refundAmount = refundAmount;
  }

  public UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult refundOrderNo(String refundOrderNo) {
    this.refundOrderNo = refundOrderNo;
    return this;
  }

   /**
   * 退款订单号
   * @return refundOrderNo
  **/

  public String getRefundOrderNo() {
    return refundOrderNo;
  }

  public void setRefundOrderNo(String refundOrderNo) {
    this.refundOrderNo = refundOrderNo;
  }

  public UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult status(String status) {
    this.status = status;
    return this;
  }

   /**
   * 退款状态
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult createTime(String createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 退款时间
   * @return createTime
  **/

  public String getCreateTime() {
    return createTime;
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult refundCouponInfo(String refundCouponInfo) {
    this.refundCouponInfo = refundCouponInfo;
    return this;
  }

   /**
   * 退款营销信息
   * @return refundCouponInfo
  **/

  public String getRefundCouponInfo() {
    return refundCouponInfo;
  }

  public void setRefundCouponInfo(String refundCouponInfo) {
    this.refundCouponInfo = refundCouponInfo;
  }

  public UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult refundOrigTxnAmt(BigDecimal refundOrigTxnAmt) {
    this.refundOrigTxnAmt = refundOrigTxnAmt;
    return this;
  }

   /**
   * 申请退款金额（包含营销金额）
   * @return refundOrigTxnAmt
  **/

  public BigDecimal getRefundOrigTxnAmt() {
    return refundOrigTxnAmt;
  }

  public void setRefundOrigTxnAmt(BigDecimal refundOrigTxnAmt) {
    this.refundOrigTxnAmt = refundOrigTxnAmt;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult upopActivescanQuerypayresultRefundTradeDetailResponseDTOResult = (UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult) o;
    return ObjectUtils.equals(this.payOrderNo, upopActivescanQuerypayresultRefundTradeDetailResponseDTOResult.payOrderNo) &&
    ObjectUtils.equals(this.refundAmount, upopActivescanQuerypayresultRefundTradeDetailResponseDTOResult.refundAmount) &&
    ObjectUtils.equals(this.refundOrderNo, upopActivescanQuerypayresultRefundTradeDetailResponseDTOResult.refundOrderNo) &&
    ObjectUtils.equals(this.status, upopActivescanQuerypayresultRefundTradeDetailResponseDTOResult.status) &&
    ObjectUtils.equals(this.createTime, upopActivescanQuerypayresultRefundTradeDetailResponseDTOResult.createTime) &&
    ObjectUtils.equals(this.refundCouponInfo, upopActivescanQuerypayresultRefundTradeDetailResponseDTOResult.refundCouponInfo) &&
    ObjectUtils.equals(this.refundOrigTxnAmt, upopActivescanQuerypayresultRefundTradeDetailResponseDTOResult.refundOrigTxnAmt);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(payOrderNo, refundAmount, refundOrderNo, status, createTime, refundCouponInfo, refundOrigTxnAmt);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult {\n");
    
    sb.append("    payOrderNo: ").append(toIndentedString(payOrderNo)).append("\n");
    sb.append("    refundAmount: ").append(toIndentedString(refundAmount)).append("\n");
    sb.append("    refundOrderNo: ").append(toIndentedString(refundOrderNo)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    refundCouponInfo: ").append(toIndentedString(refundCouponInfo)).append("\n");
    sb.append("    refundOrigTxnAmt: ").append(toIndentedString(refundOrigTxnAmt)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

