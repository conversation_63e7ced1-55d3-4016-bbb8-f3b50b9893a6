/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.promtion;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.promtion.request.*;
import com.yeepay.yop.sdk.service.promtion.response.*;

public interface PromtionClient {

    /**
     * 营销活动列表查询
     * 营销活动列表查询
     * @return ActivityListQueryResponse
     * @throws YopClientException if fails to make API call
     */
    ActivityListQueryResponse activityListQuery(ActivityListQueryRequest request) throws YopClientException;

    /**
     * 新增权益
     * 
     * @return AddRightsResponse
     * @throws YopClientException if fails to make API call
     */
    AddRightsResponse addRights(AddRightsRequest request) throws YopClientException;

    /**
     * 新增权益
     * 
     * @return AddRightsResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AddRightsResponse add_rights(AddRightsRequest request) throws YopClientException;

    /**
     * 优惠券发放接口
     * 优惠券发放接口
     * @return CouponApplyResponse
     * @throws YopClientException if fails to make API call
     */
    CouponApplyResponse couponApply(CouponApplyRequest request) throws YopClientException;

    /**
     * 优惠券查询接口
     * 优惠券查询接口
     * @return CouponListQueryResponse
     * @throws YopClientException if fails to make API call
     */
    CouponListQueryResponse couponListQuery(CouponListQueryRequest request) throws YopClientException;

    /**
     * 生成权益二维码信息
     * 
     * @return CreateRightsQrcodeAdapterResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    CreateRightsQrcodeAdapterResponse create_rights_qrcode_adapter(CreateRightsQrcodeAdapterRequest request) throws YopClientException;

    /**
     * 权益冻结
     * 
     * @return FrozenRightsResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    FrozenRightsResponse frozen_rights(FrozenRightsRequest request) throws YopClientException;

    /**
     * 营销积分账户开立
     * 营销积分账户开立
     * @return PointCreateResponse
     * @throws YopClientException if fails to make API call
     */
    PointCreateResponse pointCreate(PointCreateRequest request) throws YopClientException;

    /**
     * 营销账户积分变更
     * 营销账户积分变更
     * @return PointOperateResponse
     * @throws YopClientException if fails to make API call
     */
    PointOperateResponse pointOperate(PointOperateRequest request) throws YopClientException;

    /**
     * 营销账户积分查询
     * 营销账户积分查询
     * @return PointQueryResponse
     * @throws YopClientException if fails to make API call
     */
    PointQueryResponse pointQuery(PointQueryRequest request) throws YopClientException;

    /**
     * 查询核销记录
     * 
     * @return QueryConsumeRecordAdapterResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    QueryConsumeRecordAdapterResponse query_consume_record_adapter(QueryConsumeRecordAdapterRequest request) throws YopClientException;

    /**
     * 查询权益列表
     * 
     * @return QueryRightsResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    QueryRightsResponse query_rights(QueryRightsRequest request) throws YopClientException;

    /**
     * 生成权益二维码信息
     * 
     * @return RightsCreateQrcodeResponse
     * @throws YopClientException if fails to make API call
     */
    RightsCreateQrcodeResponse rightsCreateQrcode(RightsCreateQrcodeRequest request) throws YopClientException;

    /**
     * 权益冻结
     * 
     * @return RightsFrozenRightsResponse
     * @throws YopClientException if fails to make API call
     */
    RightsFrozenRightsResponse rightsFrozenRights(RightsFrozenRightsRequest request) throws YopClientException;

    /**
     * 查询核销记录
     * 
     * @return RightsQueryConsumeRecordsResponse
     * @throws YopClientException if fails to make API call
     */
    RightsQueryConsumeRecordsResponse rightsQueryConsumeRecords(RightsQueryConsumeRecordsRequest request) throws YopClientException;

    /**
     * 查询权益列表
     * 
     * @return RightsQueryRightsResponse
     * @throws YopClientException if fails to make API call
     */
    RightsQueryRightsResponse rightsQueryRights(RightsQueryRightsRequest request) throws YopClientException;

    /**
     * 权益转移
     * 
     * @return RightsTransferResponse
     * @throws YopClientException if fails to make API call
     */
    RightsTransferResponse rightsTransfer(RightsTransferRequest request) throws YopClientException;

    /**
     * 权益转移
     * 
     * @return RightsTransferResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    RightsTransferResponse rights_transfer(RightsTransferRequest request) throws YopClientException;

    /**
     * 申请营销补贴
     * 商户申请营销补贴
     * @return SubsidyApplyResponse
     * @throws YopClientException if fails to make API call
     */
    SubsidyApplyResponse subsidyApply(SubsidyApplyRequest request) throws YopClientException;

    /**
     * 申请营销补贴退回
     * 商户申请营销补贴退回
     * @return SubsidyBackResponse
     * @throws YopClientException if fails to make API call
     */
    SubsidyBackResponse subsidyBack(SubsidyBackRequest request) throws YopClientException;

    /**
     * 查询营销补贴退回
     * 商户申请营销补贴退回查询
     * @return SubsidyBackQueryResponse
     * @throws YopClientException if fails to make API call
     */
    SubsidyBackQueryResponse subsidyBackQuery(SubsidyBackQueryRequest request) throws YopClientException;

    /**
     * 查询营销补贴
     * 商户申请营销补贴查询
     * @return SubsidyQueryResponse
     * @throws YopClientException if fails to make API call
     */
    SubsidyQueryResponse subsidyQuery(SubsidyQueryRequest request) throws YopClientException;


    /**
     * Shuts down this client object, releasing any resources that might be held open. This is an optional method, and
     * callers are not expected to call it, but can if they want to explicitly release any open resources. Once a client
     * has been shutdown, it should not be used to make any more requests.
     */
    void shutdown();
}
