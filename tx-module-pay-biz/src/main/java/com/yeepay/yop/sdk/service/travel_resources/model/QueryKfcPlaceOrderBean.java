/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.travel_resources.model.QueryKfcItemsBean;
import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * kfc广场订单信息
 */
public class QueryKfcPlaceOrderBean implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;pre&gt;城市名称&lt;/pre&gt;
   */
  @JsonProperty("cityName")
  private String cityName = null;

  /**
   * &lt;pre&gt;1:堂食 2:外带&lt;/pre&gt;
   */
  @JsonProperty("eatType")
  private Integer eatType = null;

  /**
   * &lt;pre&gt;餐厅地址&lt;/pre&gt;
   */
  @JsonProperty("storeAddress")
  private String storeAddress = null;

  /**
   * &lt;pre&gt;餐厅名称&lt;/pre&gt;
   */
  @JsonProperty("storeName")
  private String storeName = null;

  /**
   * &lt;pre&gt;餐品明细&lt;/pre&gt;
   */
  @JsonProperty("items")
  private List<QueryKfcItemsBean> items = null;

  /**
   * &lt;pre&gt;门店编号&lt;/pre&gt;
   */
  @JsonProperty("storeCode")
  private String storeCode = null;

  public QueryKfcPlaceOrderBean cityName(String cityName) {
    this.cityName = cityName;
    return this;
  }

   /**
   * &lt;pre&gt;城市名称&lt;/pre&gt;
   * @return cityName
  **/

  public String getCityName() {
    return cityName;
  }

  public void setCityName(String cityName) {
    this.cityName = cityName;
  }

  public QueryKfcPlaceOrderBean eatType(Integer eatType) {
    this.eatType = eatType;
    return this;
  }

   /**
   * &lt;pre&gt;1:堂食 2:外带&lt;/pre&gt;
   * @return eatType
  **/

  public Integer getEatType() {
    return eatType;
  }

  public void setEatType(Integer eatType) {
    this.eatType = eatType;
  }

  public QueryKfcPlaceOrderBean storeAddress(String storeAddress) {
    this.storeAddress = storeAddress;
    return this;
  }

   /**
   * &lt;pre&gt;餐厅地址&lt;/pre&gt;
   * @return storeAddress
  **/

  public String getStoreAddress() {
    return storeAddress;
  }

  public void setStoreAddress(String storeAddress) {
    this.storeAddress = storeAddress;
  }

  public QueryKfcPlaceOrderBean storeName(String storeName) {
    this.storeName = storeName;
    return this;
  }

   /**
   * &lt;pre&gt;餐厅名称&lt;/pre&gt;
   * @return storeName
  **/

  public String getStoreName() {
    return storeName;
  }

  public void setStoreName(String storeName) {
    this.storeName = storeName;
  }

  public QueryKfcPlaceOrderBean items(List<QueryKfcItemsBean> items) {
    this.items = items;
    return this;
  }

  public QueryKfcPlaceOrderBean addItemsItem(QueryKfcItemsBean itemsItem) {
    if (this.items == null) {
      this.items = new ArrayList<>();
    }
    this.items.add(itemsItem);
    return this;
  }

   /**
   * &lt;pre&gt;餐品明细&lt;/pre&gt;
   * @return items
  **/

  public List<QueryKfcItemsBean> getItems() {
    return items;
  }

  public void setItems(List<QueryKfcItemsBean> items) {
    this.items = items;
  }

  public QueryKfcPlaceOrderBean storeCode(String storeCode) {
    this.storeCode = storeCode;
    return this;
  }

   /**
   * &lt;pre&gt;门店编号&lt;/pre&gt;
   * @return storeCode
  **/

  public String getStoreCode() {
    return storeCode;
  }

  public void setStoreCode(String storeCode) {
    this.storeCode = storeCode;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QueryKfcPlaceOrderBean queryKfcPlaceOrderBean = (QueryKfcPlaceOrderBean) o;
    return ObjectUtils.equals(this.cityName, queryKfcPlaceOrderBean.cityName) &&
    ObjectUtils.equals(this.eatType, queryKfcPlaceOrderBean.eatType) &&
    ObjectUtils.equals(this.storeAddress, queryKfcPlaceOrderBean.storeAddress) &&
    ObjectUtils.equals(this.storeName, queryKfcPlaceOrderBean.storeName) &&
    ObjectUtils.equals(this.items, queryKfcPlaceOrderBean.items) &&
    ObjectUtils.equals(this.storeCode, queryKfcPlaceOrderBean.storeCode);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(cityName, eatType, storeAddress, storeName, items, storeCode);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryKfcPlaceOrderBean {\n");
    
    sb.append("    cityName: ").append(toIndentedString(cityName)).append("\n");
    sb.append("    eatType: ").append(toIndentedString(eatType)).append("\n");
    sb.append("    storeAddress: ").append(toIndentedString(storeAddress)).append("\n");
    sb.append("    storeName: ").append(toIndentedString(storeName)).append("\n");
    sb.append("    items: ").append(toIndentedString(items)).append("\n");
    sb.append("    storeCode: ").append(toIndentedString(storeCode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

