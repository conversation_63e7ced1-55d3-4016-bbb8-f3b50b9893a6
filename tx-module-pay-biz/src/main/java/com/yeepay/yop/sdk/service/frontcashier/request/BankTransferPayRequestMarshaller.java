/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class BankTransferPayRequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<BankTransferPayRequest> {
    private final String serviceName = "Frontcashier";

    private final String resourcePath = "/rest/v1.0/frontcashier/bank-transfer/pay";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.POST;


    @Override
    public Request<BankTransferPayRequest> marshall(BankTransferPayRequest request) {
        Request<BankTransferPayRequest> internalRequest = new DefaultRequest<BankTransferPayRequest>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getParentMerchantNo() != null) {
            internalRequest.addParameter("parentMerchantNo", PrimitiveMarshallerUtils.marshalling(request.getParentMerchantNo(), "String"));
        }
        if (request.getMerchantNo() != null) {
            internalRequest.addParameter("merchantNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantNo(), "String"));
        }
        if (request.getOrderId() != null) {
            internalRequest.addParameter("orderId", PrimitiveMarshallerUtils.marshalling(request.getOrderId(), "String"));
        }
        if (request.getOrderAmount() != null) {
            internalRequest.addParameter("orderAmount", PrimitiveMarshallerUtils.marshalling(request.getOrderAmount(), "BigDecimal"));
        }
        if (request.getExpiredTime() != null) {
            internalRequest.addParameter("expiredTime", PrimitiveMarshallerUtils.marshalling(request.getExpiredTime(), "String"));
        }
        if (request.getNotifyUrl() != null) {
            internalRequest.addParameter("notifyUrl", PrimitiveMarshallerUtils.marshalling(request.getNotifyUrl(), "String"));
        }
        if (request.getMemo() != null) {
            internalRequest.addParameter("memo", PrimitiveMarshallerUtils.marshalling(request.getMemo(), "String"));
        }
        if (request.getGoodsName() != null) {
            internalRequest.addParameter("goodsName", PrimitiveMarshallerUtils.marshalling(request.getGoodsName(), "String"));
        }
        if (request.getFundProcessType() != null) {
            internalRequest.addParameter("fundProcessType", PrimitiveMarshallerUtils.marshalling(request.getFundProcessType(), "String"));
        }
        if (request.getCsUrl() != null) {
            internalRequest.addParameter("csUrl", PrimitiveMarshallerUtils.marshalling(request.getCsUrl(), "String"));
        }
        if (request.getPayerAccountName() != null) {
            internalRequest.addParameter("payerAccountName", PrimitiveMarshallerUtils.marshalling(request.getPayerAccountName(), "String"));
        }
        if (request.getCheckType() != null) {
            internalRequest.addParameter("checkType", PrimitiveMarshallerUtils.marshalling(request.getCheckType(), "String"));
        }
        if (request.getToken() != null) {
            internalRequest.addParameter("token", PrimitiveMarshallerUtils.marshalling(request.getToken(), "String"));
        }
        if (request.getBankAccountNo() != null) {
            internalRequest.addParameter("bankAccountNo", PrimitiveMarshallerUtils.marshalling(request.getBankAccountNo(), "String"));
        }
        if (request.getCustomerId() != null) {
            internalRequest.addParameter("customerId", PrimitiveMarshallerUtils.marshalling(request.getCustomerId(), "String"));
        }
        if (request.getPayerAccountNo() != null) {
            internalRequest.addParameter("payerAccountNo", PrimitiveMarshallerUtils.marshalling(request.getPayerAccountNo(), "String"));
        }
        if (request.getAccountNotifyUrl() != null) {
            internalRequest.addParameter("accountNotifyUrl", PrimitiveMarshallerUtils.marshalling(request.getAccountNotifyUrl(), "String"));
        }
        if (request.getBusinessInfo() != null) {
            internalRequest.addParameter("businessInfo", PrimitiveMarshallerUtils.marshalling(request.getBusinessInfo(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static BankTransferPayRequestMarshaller INSTANCE = new BankTransferPayRequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static BankTransferPayRequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
