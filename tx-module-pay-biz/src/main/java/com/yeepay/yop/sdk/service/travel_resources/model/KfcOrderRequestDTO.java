/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yeepay.yop.sdk.service.base.request.serializer.YopCustomSerializer;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * Kfc订单请求参数
 */
public class KfcOrderRequestDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;请求商编&lt;/p&gt;
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;p&gt;商户订单号（与千猪的订单号要相同）&lt;/p&gt;
   */
  @JsonProperty("merchantRequestNo")
  private String merchantRequestNo = null;

  /**
   * &lt;pre&gt;请求方订单号&lt;/pre&gt;
   */
  @JsonProperty("parentMerchantRequestNo")
  private String parentMerchantRequestNo = null;

  /**
   * &lt;p&gt;采购渠道&lt;/p&gt;
   */
  @JsonProperty("supplierChannel")
  private String supplierChannel = null;

  /**
   * &lt;pre&gt;供应方平台给采购方分配的秘钥&lt;/pre&gt; &lt;pre&gt;platformId与secret要拼接起来使用aes加密后传递&lt;/pre&gt; &lt;pre&gt;aes key在对接时找易宝技术支持&lt;/pre&gt; &lt;pre&gt;加密方式：&lt;/pre&gt; &lt;pre&gt;platformId&#x3D;123&amp;amp;secret&#x3D;abc 将拼接的字符串直接使用aes加密&lt;/pre&gt;
   */
  @JsonProperty("platformIdAccountInfo")
  private String platformIdAccountInfo = null;

  /**
   * &lt;pre&gt;订单完成通知地址：订单发货成功、订单取消、订单已完成&lt;/pre&gt;
   */
  @JsonProperty("complateNotifyUrl")
  private String complateNotifyUrl = null;

  public KfcOrderRequestDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;请求商编&lt;/p&gt;
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public KfcOrderRequestDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public KfcOrderRequestDTO merchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;商户订单号（与千猪的订单号要相同）&lt;/p&gt;
   * @return merchantRequestNo
  **/

  public String getMerchantRequestNo() {
    return merchantRequestNo;
  }

  public void setMerchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
  }

  public KfcOrderRequestDTO parentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
    return this;
  }

   /**
   * &lt;pre&gt;请求方订单号&lt;/pre&gt;
   * @return parentMerchantRequestNo
  **/

  public String getParentMerchantRequestNo() {
    return parentMerchantRequestNo;
  }

  public void setParentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
  }

  public KfcOrderRequestDTO supplierChannel(String supplierChannel) {
    this.supplierChannel = supplierChannel;
    return this;
  }

   /**
   * &lt;p&gt;采购渠道&lt;/p&gt;
   * @return supplierChannel
  **/

  public String getSupplierChannel() {
    return supplierChannel;
  }

  public void setSupplierChannel(String supplierChannel) {
    this.supplierChannel = supplierChannel;
  }

  public KfcOrderRequestDTO platformIdAccountInfo(String platformIdAccountInfo) {
    this.platformIdAccountInfo = platformIdAccountInfo;
    return this;
  }

   /**
   * &lt;pre&gt;供应方平台给采购方分配的秘钥&lt;/pre&gt; &lt;pre&gt;platformId与secret要拼接起来使用aes加密后传递&lt;/pre&gt; &lt;pre&gt;aes key在对接时找易宝技术支持&lt;/pre&gt; &lt;pre&gt;加密方式：&lt;/pre&gt; &lt;pre&gt;platformId&#x3D;123&amp;amp;secret&#x3D;abc 将拼接的字符串直接使用aes加密&lt;/pre&gt;
   * @return platformIdAccountInfo
  **/

  public String getPlatformIdAccountInfo() {
    return platformIdAccountInfo;
  }

  public void setPlatformIdAccountInfo(String platformIdAccountInfo) {
    this.platformIdAccountInfo = platformIdAccountInfo;
  }

  public KfcOrderRequestDTO complateNotifyUrl(String complateNotifyUrl) {
    this.complateNotifyUrl = complateNotifyUrl;
    return this;
  }

   /**
   * &lt;pre&gt;订单完成通知地址：订单发货成功、订单取消、订单已完成&lt;/pre&gt;
   * @return complateNotifyUrl
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getComplateNotifyUrl() {
    return complateNotifyUrl;
  }

  public void setComplateNotifyUrl(String complateNotifyUrl) {
    this.complateNotifyUrl = complateNotifyUrl;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    KfcOrderRequestDTO kfcOrderRequestDTO = (KfcOrderRequestDTO) o;
    return ObjectUtils.equals(this.parentMerchantNo, kfcOrderRequestDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, kfcOrderRequestDTO.merchantNo) &&
    ObjectUtils.equals(this.merchantRequestNo, kfcOrderRequestDTO.merchantRequestNo) &&
    ObjectUtils.equals(this.parentMerchantRequestNo, kfcOrderRequestDTO.parentMerchantRequestNo) &&
    ObjectUtils.equals(this.supplierChannel, kfcOrderRequestDTO.supplierChannel) &&
    ObjectUtils.equals(this.platformIdAccountInfo, kfcOrderRequestDTO.platformIdAccountInfo) &&
    ObjectUtils.equals(this.complateNotifyUrl, kfcOrderRequestDTO.complateNotifyUrl);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(parentMerchantNo, merchantNo, merchantRequestNo, parentMerchantRequestNo, supplierChannel, platformIdAccountInfo, complateNotifyUrl);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class KfcOrderRequestDTO {\n");
    
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantRequestNo: ").append(toIndentedString(merchantRequestNo)).append("\n");
    sb.append("    parentMerchantRequestNo: ").append(toIndentedString(parentMerchantRequestNo)).append("\n");
    sb.append("    supplierChannel: ").append(toIndentedString(supplierChannel)).append("\n");
    sb.append("    platformIdAccountInfo: ").append(toIndentedString(platformIdAccountInfo)).append("\n");
    sb.append("    complateNotifyUrl: ").append(toIndentedString(complateNotifyUrl)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

