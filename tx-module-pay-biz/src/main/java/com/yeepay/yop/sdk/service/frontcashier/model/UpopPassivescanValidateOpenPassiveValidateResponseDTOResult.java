/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * UpopPassivescanValidateOpenPassiveValidateResponseDTOResult
 */
public class UpopPassivescanValidateOpenPassiveValidateResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 响应码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 响应码描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 商户订单号
   */
  @JsonProperty("merchantFlowId")
  private String merchantFlowId = null;

  /**
   * 处理状态
   */
  @JsonProperty("dealStatus")
  private String dealStatus = null;

  public UpopPassivescanValidateOpenPassiveValidateResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 响应码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public UpopPassivescanValidateOpenPassiveValidateResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 响应码描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public UpopPassivescanValidateOpenPassiveValidateResponseDTOResult merchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
    return this;
  }

   /**
   * 商户订单号
   * @return merchantFlowId
  **/

  public String getMerchantFlowId() {
    return merchantFlowId;
  }

  public void setMerchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
  }

  public UpopPassivescanValidateOpenPassiveValidateResponseDTOResult dealStatus(String dealStatus) {
    this.dealStatus = dealStatus;
    return this;
  }

   /**
   * 处理状态
   * @return dealStatus
  **/

  public String getDealStatus() {
    return dealStatus;
  }

  public void setDealStatus(String dealStatus) {
    this.dealStatus = dealStatus;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    UpopPassivescanValidateOpenPassiveValidateResponseDTOResult upopPassivescanValidateOpenPassiveValidateResponseDTOResult = (UpopPassivescanValidateOpenPassiveValidateResponseDTOResult) o;
    return ObjectUtils.equals(this.code, upopPassivescanValidateOpenPassiveValidateResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, upopPassivescanValidateOpenPassiveValidateResponseDTOResult.message) &&
    ObjectUtils.equals(this.merchantFlowId, upopPassivescanValidateOpenPassiveValidateResponseDTOResult.merchantFlowId) &&
    ObjectUtils.equals(this.dealStatus, upopPassivescanValidateOpenPassiveValidateResponseDTOResult.dealStatus);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, merchantFlowId, dealStatus);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UpopPassivescanValidateOpenPassiveValidateResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    merchantFlowId: ").append(toIndentedString(merchantFlowId)).append("\n");
    sb.append("    dealStatus: ").append(toIndentedString(dealStatus)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

