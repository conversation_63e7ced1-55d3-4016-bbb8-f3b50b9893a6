/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier;

import com.yeepay.yop.sdk.client.*;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.HttpResponseAnalyzerSupport;
import com.yeepay.yop.sdk.http.HttpResponseHandler;
import com.yeepay.yop.sdk.http.handler.DefaultHttpResponseHandler;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.service.frontcashier.request.*;
import com.yeepay.yop.sdk.service.frontcashier.response.*;

public class FrontcashierClientImpl implements FrontcashierClient {

    private final ClientHandler clientHandler;

    FrontcashierClientImpl(ClientParams clientParams) {
        this.clientHandler = new ClientHandlerImpl(new ClientHandlerParams().withClientParams(clientParams));
    }

    @Override
    public AccountConfirmResponse accountConfirm(AccountConfirmRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountConfirmRequest> requestMarshaller = AccountConfirmRequestMarshaller.getInstance();
        HttpResponseHandler<AccountConfirmResponse> responseHandler =
                new DefaultHttpResponseHandler<AccountConfirmResponse>(AccountConfirmResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountConfirmRequest, AccountConfirmResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AccountInfoConfirmResponse accountInfoConfirm(AccountInfoConfirmRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountInfoConfirmRequest> requestMarshaller = AccountInfoConfirmRequestMarshaller.getInstance();
        HttpResponseHandler<AccountInfoConfirmResponse> responseHandler =
                new DefaultHttpResponseHandler<AccountInfoConfirmResponse>(AccountInfoConfirmResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountInfoConfirmRequest, AccountInfoConfirmResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankTransferPayResponse bankTransferPay(BankTransferPayRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankTransferPayRequest> requestMarshaller = BankTransferPayRequestMarshaller.getInstance();
        HttpResponseHandler<BankTransferPayResponse> responseHandler =
                new DefaultHttpResponseHandler<BankTransferPayResponse>(BankTransferPayResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankTransferPayRequest, BankTransferPayResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankTransferQueryResponse bankTransferQuery(BankTransferQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankTransferQueryRequest> requestMarshaller = BankTransferQueryRequestMarshaller.getInstance();
        HttpResponseHandler<BankTransferQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<BankTransferQueryResponse>(BankTransferQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankTransferQueryRequest, BankTransferQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardBindcardlistResponse bindcardBindcardlist(BindcardBindcardlistRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardBindcardlistRequest> requestMarshaller = BindcardBindcardlistRequestMarshaller.getInstance();
        HttpResponseHandler<BindcardBindcardlistResponse> responseHandler =
                new DefaultHttpResponseHandler<BindcardBindcardlistResponse>(BindcardBindcardlistResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardBindcardlistRequest, BindcardBindcardlistResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardConfirmResponse bindcardConfirm(BindcardConfirmRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardConfirmRequest> requestMarshaller = BindcardConfirmRequestMarshaller.getInstance();
        HttpResponseHandler<BindcardConfirmResponse> responseHandler =
                new DefaultHttpResponseHandler<BindcardConfirmResponse>(BindcardConfirmResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardConfirmRequest, BindcardConfirmResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardConfirmV2Response bindcardConfirmV2(BindcardConfirmV2Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardConfirmV2Request> requestMarshaller = BindcardConfirmV2RequestMarshaller.getInstance();
        HttpResponseHandler<BindcardConfirmV2Response> responseHandler =
                new DefaultHttpResponseHandler<BindcardConfirmV2Response>(BindcardConfirmV2Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardConfirmV2Request, BindcardConfirmV2Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardGetcardbinResponse bindcardGetcardbin(BindcardGetcardbinRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardGetcardbinRequest> requestMarshaller = BindcardGetcardbinRequestMarshaller.getInstance();
        HttpResponseHandler<BindcardGetcardbinResponse> responseHandler =
                new DefaultHttpResponseHandler<BindcardGetcardbinResponse>(BindcardGetcardbinResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardGetcardbinRequest, BindcardGetcardbinResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardPayerrequestResponse bindcardPayerrequest(BindcardPayerrequestRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardPayerrequestRequest> requestMarshaller = BindcardPayerrequestRequestMarshaller.getInstance();
        HttpResponseHandler<BindcardPayerrequestResponse> responseHandler =
                new DefaultHttpResponseHandler<BindcardPayerrequestResponse>(BindcardPayerrequestResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardPayerrequestRequest, BindcardPayerrequestResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardQueryorderResponse bindcardQueryorder(BindcardQueryorderRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardQueryorderRequest> requestMarshaller = BindcardQueryorderRequestMarshaller.getInstance();
        HttpResponseHandler<BindcardQueryorderResponse> responseHandler =
                new DefaultHttpResponseHandler<BindcardQueryorderResponse>(BindcardQueryorderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardQueryorderRequest, BindcardQueryorderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardQueryorderinfoResponse bindcardQueryorderinfo(BindcardQueryorderinfoRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardQueryorderinfoRequest> requestMarshaller = BindcardQueryorderinfoRequestMarshaller.getInstance();
        HttpResponseHandler<BindcardQueryorderinfoResponse> responseHandler =
                new DefaultHttpResponseHandler<BindcardQueryorderinfoResponse>(BindcardQueryorderinfoResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardQueryorderinfoRequest, BindcardQueryorderinfoResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardRequestResponse bindcardRequest(BindcardRequestRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardRequestRequest> requestMarshaller = BindcardRequestRequestMarshaller.getInstance();
        HttpResponseHandler<BindcardRequestResponse> responseHandler =
                new DefaultHttpResponseHandler<BindcardRequestResponse>(BindcardRequestResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardRequestRequest, BindcardRequestResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardRequestV2Response bindcardRequestV2(BindcardRequestV2Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardRequestV2Request> requestMarshaller = BindcardRequestV2RequestMarshaller.getInstance();
        HttpResponseHandler<BindcardRequestV2Response> responseHandler =
                new DefaultHttpResponseHandler<BindcardRequestV2Response>(BindcardRequestV2Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardRequestV2Request, BindcardRequestV2Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardRequestV21Response bindcardRequestV2_1(BindcardRequestV21Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardRequestV21Request> requestMarshaller = BindcardRequestV21RequestMarshaller.getInstance();
        HttpResponseHandler<BindcardRequestV21Response> responseHandler =
                new DefaultHttpResponseHandler<BindcardRequestV21Response>(BindcardRequestV21Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardRequestV21Request, BindcardRequestV21Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardResendsmsResponse bindcardResendsms(BindcardResendsmsRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardResendsmsRequest> requestMarshaller = BindcardResendsmsRequestMarshaller.getInstance();
        HttpResponseHandler<BindcardResendsmsResponse> responseHandler =
                new DefaultHttpResponseHandler<BindcardResendsmsResponse>(BindcardResendsmsResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardResendsmsRequest, BindcardResendsmsResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardResendsmsV2Response bindcardResendsmsV2(BindcardResendsmsV2Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardResendsmsV2Request> requestMarshaller = BindcardResendsmsV2RequestMarshaller.getInstance();
        HttpResponseHandler<BindcardResendsmsV2Response> responseHandler =
                new DefaultHttpResponseHandler<BindcardResendsmsV2Response>(BindcardResendsmsV2Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardResendsmsV2Request, BindcardResendsmsV2Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardUnbindcardResponse bindcardUnbindcard(BindcardUnbindcardRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardUnbindcardRequest> requestMarshaller = BindcardUnbindcardRequestMarshaller.getInstance();
        HttpResponseHandler<BindcardUnbindcardResponse> responseHandler =
                new DefaultHttpResponseHandler<BindcardUnbindcardResponse>(BindcardUnbindcardResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardUnbindcardRequest, BindcardUnbindcardResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindpayConfirmResponse bindpayConfirm(BindpayConfirmRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindpayConfirmRequest> requestMarshaller = BindpayConfirmRequestMarshaller.getInstance();
        HttpResponseHandler<BindpayConfirmResponse> responseHandler =
                new DefaultHttpResponseHandler<BindpayConfirmResponse>(BindpayConfirmResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindpayConfirmRequest, BindpayConfirmResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindpayRequestResponse bindpayRequest(BindpayRequestRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindpayRequestRequest> requestMarshaller = BindpayRequestRequestMarshaller.getInstance();
        HttpResponseHandler<BindpayRequestResponse> responseHandler =
                new DefaultHttpResponseHandler<BindpayRequestResponse>(BindpayRequestResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindpayRequestRequest, BindpayRequestResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindpaySendsmsResponse bindpaySendsms(BindpaySendsmsRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindpaySendsmsRequest> requestMarshaller = BindpaySendsmsRequestMarshaller.getInstance();
        HttpResponseHandler<BindpaySendsmsResponse> responseHandler =
                new DefaultHttpResponseHandler<BindpaySendsmsResponse>(BindpaySendsmsResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindpaySendsmsRequest, BindpaySendsmsResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public FastbindcardRequestResponse fastbindcardRequest(FastbindcardRequestRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<FastbindcardRequestRequest> requestMarshaller = FastbindcardRequestRequestMarshaller.getInstance();
        HttpResponseHandler<FastbindcardRequestResponse> responseHandler =
                new DefaultHttpResponseHandler<FastbindcardRequestResponse>(FastbindcardRequestResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<FastbindcardRequestRequest, FastbindcardRequestResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public GetcardbinResponse getcardbin(GetcardbinRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<GetcardbinRequest> requestMarshaller = GetcardbinRequestMarshaller.getInstance();
        HttpResponseHandler<GetcardbinResponse> responseHandler =
                new DefaultHttpResponseHandler<GetcardbinResponse>(GetcardbinResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<GetcardbinRequest, GetcardbinResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public UpopActivescanPayResponse upopActivescanPay(UpopActivescanPayRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<UpopActivescanPayRequest> requestMarshaller = UpopActivescanPayRequestMarshaller.getInstance();
        HttpResponseHandler<UpopActivescanPayResponse> responseHandler =
                new DefaultHttpResponseHandler<UpopActivescanPayResponse>(UpopActivescanPayResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<UpopActivescanPayRequest, UpopActivescanPayResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public UpopActivescanQuerycouponResponse upopActivescanQuerycoupon(UpopActivescanQuerycouponRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<UpopActivescanQuerycouponRequest> requestMarshaller = UpopActivescanQuerycouponRequestMarshaller.getInstance();
        HttpResponseHandler<UpopActivescanQuerycouponResponse> responseHandler =
                new DefaultHttpResponseHandler<UpopActivescanQuerycouponResponse>(UpopActivescanQuerycouponResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<UpopActivescanQuerycouponRequest, UpopActivescanQuerycouponResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public UpopActivescanQuerypayeeorderResponse upopActivescanQuerypayeeorder(UpopActivescanQuerypayeeorderRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<UpopActivescanQuerypayeeorderRequest> requestMarshaller = UpopActivescanQuerypayeeorderRequestMarshaller.getInstance();
        HttpResponseHandler<UpopActivescanQuerypayeeorderResponse> responseHandler =
                new DefaultHttpResponseHandler<UpopActivescanQuerypayeeorderResponse>(UpopActivescanQuerypayeeorderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<UpopActivescanQuerypayeeorderRequest, UpopActivescanQuerypayeeorderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public UpopActivescanQuerypayresultResponse upopActivescanQuerypayresult(UpopActivescanQuerypayresultRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<UpopActivescanQuerypayresultRequest> requestMarshaller = UpopActivescanQuerypayresultRequestMarshaller.getInstance();
        HttpResponseHandler<UpopActivescanQuerypayresultResponse> responseHandler =
                new DefaultHttpResponseHandler<UpopActivescanQuerypayresultResponse>(UpopActivescanQuerypayresultResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<UpopActivescanQuerypayresultRequest, UpopActivescanQuerypayresultResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public UpopPassivescanBindQrcodeResponse upopPassivescanBindQrcode(UpopPassivescanBindQrcodeRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<UpopPassivescanBindQrcodeRequest> requestMarshaller = UpopPassivescanBindQrcodeRequestMarshaller.getInstance();
        HttpResponseHandler<UpopPassivescanBindQrcodeResponse> responseHandler =
                new DefaultHttpResponseHandler<UpopPassivescanBindQrcodeResponse>(UpopPassivescanBindQrcodeResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<UpopPassivescanBindQrcodeRequest, UpopPassivescanBindQrcodeResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public UpopPassivescanValidateResponse upopPassivescanValidate(UpopPassivescanValidateRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<UpopPassivescanValidateRequest> requestMarshaller = UpopPassivescanValidateRequestMarshaller.getInstance();
        HttpResponseHandler<UpopPassivescanValidateResponse> responseHandler =
                new DefaultHttpResponseHandler<UpopPassivescanValidateResponse>(UpopPassivescanValidateResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<UpopPassivescanValidateRequest, UpopPassivescanValidateResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public YjzfBindpayrequestResponse yjzfBindpayrequest(YjzfBindpayrequestRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<YjzfBindpayrequestRequest> requestMarshaller = YjzfBindpayrequestRequestMarshaller.getInstance();
        HttpResponseHandler<YjzfBindpayrequestResponse> responseHandler =
                new DefaultHttpResponseHandler<YjzfBindpayrequestResponse>(YjzfBindpayrequestResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<YjzfBindpayrequestRequest, YjzfBindpayrequestResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public YjzfFirstpayrequestResponse yjzfFirstpayrequest(YjzfFirstpayrequestRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<YjzfFirstpayrequestRequest> requestMarshaller = YjzfFirstpayrequestRequestMarshaller.getInstance();
        HttpResponseHandler<YjzfFirstpayrequestResponse> responseHandler =
                new DefaultHttpResponseHandler<YjzfFirstpayrequestResponse>(YjzfFirstpayrequestResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<YjzfFirstpayrequestRequest, YjzfFirstpayrequestResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public YjzfPaymentconfirmResponse yjzfPaymentconfirm(YjzfPaymentconfirmRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<YjzfPaymentconfirmRequest> requestMarshaller = YjzfPaymentconfirmRequestMarshaller.getInstance();
        HttpResponseHandler<YjzfPaymentconfirmResponse> responseHandler =
                new DefaultHttpResponseHandler<YjzfPaymentconfirmResponse>(YjzfPaymentconfirmResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<YjzfPaymentconfirmRequest, YjzfPaymentconfirmResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public YjzfSendsmsResponse yjzfSendsms(YjzfSendsmsRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<YjzfSendsmsRequest> requestMarshaller = YjzfSendsmsRequestMarshaller.getInstance();
        HttpResponseHandler<YjzfSendsmsResponse> responseHandler =
                new DefaultHttpResponseHandler<YjzfSendsmsResponse>(YjzfSendsmsResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<YjzfSendsmsRequest, YjzfSendsmsResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }

    @Override
    public void shutdown() {
        clientHandler.shutdown();
    }

}
