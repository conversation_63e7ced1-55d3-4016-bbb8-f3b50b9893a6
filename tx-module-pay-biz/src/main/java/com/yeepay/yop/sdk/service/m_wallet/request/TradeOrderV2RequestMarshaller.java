/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class TradeOrderV2RequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<TradeOrderV2Request> {
    private final String serviceName = "MWallet";

    private final String resourcePath = "/rest/v2.0/m-wallet/trade/order";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.POST;


    @Override
    public Request<TradeOrderV2Request> marshall(TradeOrderV2Request request) {
        Request<TradeOrderV2Request> internalRequest = new DefaultRequest<TradeOrderV2Request>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getParentMerchantNo() != null) {
            internalRequest.addParameter("parentMerchantNo", PrimitiveMarshallerUtils.marshalling(request.getParentMerchantNo(), "String"));
        }
        if (request.getMerchantNo() != null) {
            internalRequest.addParameter("merchantNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantNo(), "String"));
        }
        if (request.getOrderId() != null) {
            internalRequest.addParameter("orderId", PrimitiveMarshallerUtils.marshalling(request.getOrderId(), "String"));
        }
        if (request.getOrderAmount() != null) {
            internalRequest.addParameter("orderAmount", PrimitiveMarshallerUtils.marshalling(request.getOrderAmount(), "BigDecimal"));
        }
        if (request.getExpiredTime() != null) {
            internalRequest.addParameter("expiredTime", PrimitiveMarshallerUtils.marshalling(request.getExpiredTime(), "DateTime"));
        }
        if (request.getNotifyUrl() != null) {
            internalRequest.addParameter("notifyUrl", PrimitiveMarshallerUtils.marshalling(request.getNotifyUrl(), "String"));
        }
        if (request.getMemo() != null) {
            internalRequest.addParameter("memo", PrimitiveMarshallerUtils.marshalling(request.getMemo(), "String"));
        }
        if (request.getGoodsName() != null) {
            internalRequest.addParameter("goodsName", PrimitiveMarshallerUtils.marshalling(request.getGoodsName(), "String"));
        }
        if (request.getFundProcessType() != null) {
            internalRequest.addParameter("fundProcessType", PrimitiveMarshallerUtils.marshalling(request.getFundProcessType(), "String"));
        }
        if (request.getMemberNo() != null) {
            internalRequest.addParameter("memberNo", PrimitiveMarshallerUtils.marshalling(request.getMemberNo(), "String"));
        }
        if (request.getPayerIp() != null) {
            internalRequest.addParameter("payerIp", PrimitiveMarshallerUtils.marshalling(request.getPayerIp(), "String"));
        }
        if (request.getPayAgreement() != null) {
            internalRequest.addParameter("payAgreement", PrimitiveMarshallerUtils.marshalling(request.getPayAgreement(), "String"));
        }
        if (request.getCsUrl() != null) {
            internalRequest.addParameter("csUrl", PrimitiveMarshallerUtils.marshalling(request.getCsUrl(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static TradeOrderV2RequestMarshaller INSTANCE = new TradeOrderV2RequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static TradeOrderV2RequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
