/*
 * 再处理
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.reprocess.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 响应结果
 */
public class YopQueryMigrateOrderResDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码,OPR00000 成功
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 收单订单的发起方商编
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * 收款订单的商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 易宝资金迁移订单号
   */
  @JsonProperty("uniqueMigrateNo")
  private String uniqueMigrateNo = null;

  /**
   * 本次请求的请求号，收款商编下唯一
   */
  @JsonProperty("migrateRequestId")
  private String migrateRequestId = null;

  /**
   * 单位：元，精确到小数点后两位
   */
  @JsonProperty("migrateAmount")
  private String migrateAmount = null;

  /**
   * 可选项如下:&lt;br&gt;PROCESSING:迁移处理中&lt;br&gt;FAIL:迁移失败&lt;br&gt;SUCCESS:迁移成功
   */
  @JsonProperty("status")
  private String status = null;

  /**
   * 失败时返回
   */
  @JsonProperty("failReason")
  private String failReason = null;

  public YopQueryMigrateOrderResDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码,OPR00000 成功
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public YopQueryMigrateOrderResDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回信息
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public YopQueryMigrateOrderResDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * 收单订单的发起方商编
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public YopQueryMigrateOrderResDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 收款订单的商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public YopQueryMigrateOrderResDTO uniqueMigrateNo(String uniqueMigrateNo) {
    this.uniqueMigrateNo = uniqueMigrateNo;
    return this;
  }

   /**
   * 易宝资金迁移订单号
   * @return uniqueMigrateNo
  **/

  public String getUniqueMigrateNo() {
    return uniqueMigrateNo;
  }

  public void setUniqueMigrateNo(String uniqueMigrateNo) {
    this.uniqueMigrateNo = uniqueMigrateNo;
  }

  public YopQueryMigrateOrderResDTO migrateRequestId(String migrateRequestId) {
    this.migrateRequestId = migrateRequestId;
    return this;
  }

   /**
   * 本次请求的请求号，收款商编下唯一
   * @return migrateRequestId
  **/

  public String getMigrateRequestId() {
    return migrateRequestId;
  }

  public void setMigrateRequestId(String migrateRequestId) {
    this.migrateRequestId = migrateRequestId;
  }

  public YopQueryMigrateOrderResDTO migrateAmount(String migrateAmount) {
    this.migrateAmount = migrateAmount;
    return this;
  }

   /**
   * 单位：元，精确到小数点后两位
   * @return migrateAmount
  **/

  public String getMigrateAmount() {
    return migrateAmount;
  }

  public void setMigrateAmount(String migrateAmount) {
    this.migrateAmount = migrateAmount;
  }

  public YopQueryMigrateOrderResDTO status(String status) {
    this.status = status;
    return this;
  }

   /**
   * 可选项如下:&lt;br&gt;PROCESSING:迁移处理中&lt;br&gt;FAIL:迁移失败&lt;br&gt;SUCCESS:迁移成功
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public YopQueryMigrateOrderResDTO failReason(String failReason) {
    this.failReason = failReason;
    return this;
  }

   /**
   * 失败时返回
   * @return failReason
  **/

  public String getFailReason() {
    return failReason;
  }

  public void setFailReason(String failReason) {
    this.failReason = failReason;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    YopQueryMigrateOrderResDTO yopQueryMigrateOrderResDTO = (YopQueryMigrateOrderResDTO) o;
    return ObjectUtils.equals(this.code, yopQueryMigrateOrderResDTO.code) &&
    ObjectUtils.equals(this.message, yopQueryMigrateOrderResDTO.message) &&
    ObjectUtils.equals(this.parentMerchantNo, yopQueryMigrateOrderResDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, yopQueryMigrateOrderResDTO.merchantNo) &&
    ObjectUtils.equals(this.uniqueMigrateNo, yopQueryMigrateOrderResDTO.uniqueMigrateNo) &&
    ObjectUtils.equals(this.migrateRequestId, yopQueryMigrateOrderResDTO.migrateRequestId) &&
    ObjectUtils.equals(this.migrateAmount, yopQueryMigrateOrderResDTO.migrateAmount) &&
    ObjectUtils.equals(this.status, yopQueryMigrateOrderResDTO.status) &&
    ObjectUtils.equals(this.failReason, yopQueryMigrateOrderResDTO.failReason);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, parentMerchantNo, merchantNo, uniqueMigrateNo, migrateRequestId, migrateAmount, status, failReason);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class YopQueryMigrateOrderResDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    uniqueMigrateNo: ").append(toIndentedString(uniqueMigrateNo)).append("\n");
    sb.append("    migrateRequestId: ").append(toIndentedString(migrateRequestId)).append("\n");
    sb.append("    migrateAmount: ").append(toIndentedString(migrateAmount)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    failReason: ").append(toIndentedString(failReason)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

