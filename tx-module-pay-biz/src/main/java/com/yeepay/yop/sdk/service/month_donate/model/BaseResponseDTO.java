/*
 * 公益-月捐
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.month_donate.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 响应参数
 */
public class BaseResponseDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;响应code码&lt;/p&gt;
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * &lt;p&gt;响应信息描述&lt;/p&gt;
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * &lt;p&gt;响应状态&lt;/p&gt;
   */
  @JsonProperty("status")
  private String status = null;

  public BaseResponseDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * &lt;p&gt;响应code码&lt;/p&gt;
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BaseResponseDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * &lt;p&gt;响应信息描述&lt;/p&gt;
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public BaseResponseDTO status(String status) {
    this.status = status;
    return this;
  }

   /**
   * &lt;p&gt;响应状态&lt;/p&gt;
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BaseResponseDTO baseResponseDTO = (BaseResponseDTO) o;
    return ObjectUtils.equals(this.code, baseResponseDTO.code) &&
    ObjectUtils.equals(this.message, baseResponseDTO.message) &&
    ObjectUtils.equals(this.status, baseResponseDTO.status);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, status);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BaseResponseDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

