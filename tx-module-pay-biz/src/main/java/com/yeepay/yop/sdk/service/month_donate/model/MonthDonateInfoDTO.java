/*
 * 公益-月捐
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.month_donate.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import org.joda.time.DateTime;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 月捐信息
 */
public class MonthDonateInfoDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   *  &lt;div&gt; &lt;pre&gt;OPENED(\&quot;开通中\&quot;),&lt;br /&gt;&lt;br /&gt;MONTH_DONATING(\&quot;月捐进行中\&quot;),&lt;br /&gt;&lt;br /&gt;CANCELED(\&quot;已取消\&quot;)&lt;/pre&gt; &lt;/div&gt;
   */
  @JsonProperty("monthStatus")
  private String monthStatus = null;

  /**
   * &lt;p&gt;月捐金额&lt;/p&gt;
   */
  @JsonProperty("monthDonateAmount")
  private BigDecimal monthDonateAmount = null;

  /**
   * &lt;p&gt;总月捐金额&lt;/p&gt;
   */
  @JsonProperty("totalAmount")
  private BigDecimal totalAmount = null;

  /**
   * &lt;p&gt;月捐日期&lt;/p&gt;
   */
  @JsonProperty("monthDonateDate")
  private DateTime monthDonateDate = null;

  /**
   * &lt;p&gt;月捐笔数&lt;/p&gt;
   */
  @JsonProperty("monthDonateCount")
  private Long monthDonateCount = null;

  /**
   * &lt;p&gt;项目名称&lt;/p&gt;
   */
  @JsonProperty("projectName")
  private String projectName = null;

  /**
   * &lt;p&gt;开通日期&lt;/p&gt;
   */
  @JsonProperty("openDate")
  private DateTime openDate = null;

  /**
   * &lt;p&gt;项目ID&lt;/p&gt;
   */
  @JsonProperty("projectId")
  private Long projectId = null;

  public MonthDonateInfoDTO monthStatus(String monthStatus) {
    this.monthStatus = monthStatus;
    return this;
  }

   /**
   *  &lt;div&gt; &lt;pre&gt;OPENED(\&quot;开通中\&quot;),&lt;br /&gt;&lt;br /&gt;MONTH_DONATING(\&quot;月捐进行中\&quot;),&lt;br /&gt;&lt;br /&gt;CANCELED(\&quot;已取消\&quot;)&lt;/pre&gt; &lt;/div&gt;
   * @return monthStatus
  **/

  public String getMonthStatus() {
    return monthStatus;
  }

  public void setMonthStatus(String monthStatus) {
    this.monthStatus = monthStatus;
  }

  public MonthDonateInfoDTO monthDonateAmount(BigDecimal monthDonateAmount) {
    this.monthDonateAmount = monthDonateAmount;
    return this;
  }

   /**
   * &lt;p&gt;月捐金额&lt;/p&gt;
   * minimum: 0
   * @return monthDonateAmount
  **/

  public BigDecimal getMonthDonateAmount() {
    return monthDonateAmount;
  }

  public void setMonthDonateAmount(BigDecimal monthDonateAmount) {
    this.monthDonateAmount = monthDonateAmount;
  }

  public MonthDonateInfoDTO totalAmount(BigDecimal totalAmount) {
    this.totalAmount = totalAmount;
    return this;
  }

   /**
   * &lt;p&gt;总月捐金额&lt;/p&gt;
   * minimum: 0
   * @return totalAmount
  **/

  public BigDecimal getTotalAmount() {
    return totalAmount;
  }

  public void setTotalAmount(BigDecimal totalAmount) {
    this.totalAmount = totalAmount;
  }

  public MonthDonateInfoDTO monthDonateDate(DateTime monthDonateDate) {
    this.monthDonateDate = monthDonateDate;
    return this;
  }

   /**
   * &lt;p&gt;月捐日期&lt;/p&gt;
   * @return monthDonateDate
  **/

  public DateTime getMonthDonateDate() {
    return monthDonateDate;
  }

  public void setMonthDonateDate(DateTime monthDonateDate) {
    this.monthDonateDate = monthDonateDate;
  }

  public MonthDonateInfoDTO monthDonateCount(Long monthDonateCount) {
    this.monthDonateCount = monthDonateCount;
    return this;
  }

   /**
   * &lt;p&gt;月捐笔数&lt;/p&gt;
   * minimum: 0
   * @return monthDonateCount
  **/

  public Long getMonthDonateCount() {
    return monthDonateCount;
  }

  public void setMonthDonateCount(Long monthDonateCount) {
    this.monthDonateCount = monthDonateCount;
  }

  public MonthDonateInfoDTO projectName(String projectName) {
    this.projectName = projectName;
    return this;
  }

   /**
   * &lt;p&gt;项目名称&lt;/p&gt;
   * @return projectName
  **/

  public String getProjectName() {
    return projectName;
  }

  public void setProjectName(String projectName) {
    this.projectName = projectName;
  }

  public MonthDonateInfoDTO openDate(DateTime openDate) {
    this.openDate = openDate;
    return this;
  }

   /**
   * &lt;p&gt;开通日期&lt;/p&gt;
   * @return openDate
  **/

  public DateTime getOpenDate() {
    return openDate;
  }

  public void setOpenDate(DateTime openDate) {
    this.openDate = openDate;
  }

  public MonthDonateInfoDTO projectId(Long projectId) {
    this.projectId = projectId;
    return this;
  }

   /**
   * &lt;p&gt;项目ID&lt;/p&gt;
   * @return projectId
  **/

  public Long getProjectId() {
    return projectId;
  }

  public void setProjectId(Long projectId) {
    this.projectId = projectId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    MonthDonateInfoDTO monthDonateInfoDTO = (MonthDonateInfoDTO) o;
    return ObjectUtils.equals(this.monthStatus, monthDonateInfoDTO.monthStatus) &&
    ObjectUtils.equals(this.monthDonateAmount, monthDonateInfoDTO.monthDonateAmount) &&
    ObjectUtils.equals(this.totalAmount, monthDonateInfoDTO.totalAmount) &&
    ObjectUtils.equals(this.monthDonateDate, monthDonateInfoDTO.monthDonateDate) &&
    ObjectUtils.equals(this.monthDonateCount, monthDonateInfoDTO.monthDonateCount) &&
    ObjectUtils.equals(this.projectName, monthDonateInfoDTO.projectName) &&
    ObjectUtils.equals(this.openDate, monthDonateInfoDTO.openDate) &&
    ObjectUtils.equals(this.projectId, monthDonateInfoDTO.projectId);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(monthStatus, monthDonateAmount, totalAmount, monthDonateDate, monthDonateCount, projectName, openDate, projectId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MonthDonateInfoDTO {\n");
    
    sb.append("    monthStatus: ").append(toIndentedString(monthStatus)).append("\n");
    sb.append("    monthDonateAmount: ").append(toIndentedString(monthDonateAmount)).append("\n");
    sb.append("    totalAmount: ").append(toIndentedString(totalAmount)).append("\n");
    sb.append("    monthDonateDate: ").append(toIndentedString(monthDonateDate)).append("\n");
    sb.append("    monthDonateCount: ").append(toIndentedString(monthDonateCount)).append("\n");
    sb.append("    projectName: ").append(toIndentedString(projectName)).append("\n");
    sb.append("    openDate: ").append(toIndentedString(openDate)).append("\n");
    sb.append("    projectId: ").append(toIndentedString(projectId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

