/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单基本返回信息
 */
public class OrderBaseResponseDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;订单状态需要关注orderStatus&lt;/p&gt;
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * &lt;pre&gt;/_**&lt;br /&gt; * 订单状态:&lt;br /&gt; * 0:待支付&lt;br /&gt; * 1:待出货（已支付）&lt;br /&gt; * 2:已出货&lt;br /&gt; * 3:已取消&lt;br /&gt; * 4:交易完成&lt;br /&gt; * 部分退款/取消，对应已出货；全部退款，对应已取消&lt;br /&gt; *_/&lt;/pre&gt;
   */
  @JsonProperty("orderStatus")
  private String orderStatus = null;

  /**
   * &lt;pre&gt;供应商渠道&lt;/pre&gt;
   */
  @JsonProperty("supplierChannel")
  private String supplierChannel = null;

  /**
   * 
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * &lt;pre&gt;支付请求流水号&lt;/pre&gt;
   */
  @JsonProperty("paymentUniqueOrderNo")
  private String paymentUniqueOrderNo = null;

  /**
   * &lt;pre&gt;订单金额&lt;/pre&gt;
   */
  @JsonProperty("orderAmount")
  private BigDecimal orderAmount = null;

  /**
   * &lt;pre&gt;完成时间。订单出货成功时返回&lt;/pre&gt;
   */
  @JsonProperty("complateTime")
  private String complateTime = null;

  /**
   * &lt;p&gt;取消时间&lt;/p&gt;
   */
  @JsonProperty("cancelTime")
  private String cancelTime = null;

  /**
   * &lt;pre&gt;下单成功时间。下单成功时返回&lt;/pre&gt;
   */
  @JsonProperty("orderSuccessTime")
  private String orderSuccessTime = null;

  /**
   * &lt;pre&gt;系统返回唯一订单号&lt;/pre&gt;
   */
  @JsonProperty("systemOrderNo")
  private String systemOrderNo = null;

  /**
   * &lt;pre&gt;支付成功时间。支付成功时返回&lt;/pre&gt;
   */
  @JsonProperty("paySuccessTime")
  private String paySuccessTime = null;

  /**
   * &lt;p&gt;请求商编&lt;/p&gt;
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;p&gt;商户订单号&lt;/p&gt;
   */
  @JsonProperty("merchantRequestNo")
  private String merchantRequestNo = null;

  /**
   * &lt;pre&gt;请求方订单号&lt;/pre&gt;
   */
  @JsonProperty("parentMerchantRequestNo")
  private String parentMerchantRequestNo = null;

  /**
   * &lt;pre&gt;资源方订单号&lt;/pre&gt;
   */
  @JsonProperty("supplierOrderNo")
  private String supplierOrderNo = null;

  public OrderBaseResponseDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * &lt;p&gt;订单状态需要关注orderStatus&lt;/p&gt;
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public OrderBaseResponseDTO orderStatus(String orderStatus) {
    this.orderStatus = orderStatus;
    return this;
  }

   /**
   * &lt;pre&gt;/_**&lt;br /&gt; * 订单状态:&lt;br /&gt; * 0:待支付&lt;br /&gt; * 1:待出货（已支付）&lt;br /&gt; * 2:已出货&lt;br /&gt; * 3:已取消&lt;br /&gt; * 4:交易完成&lt;br /&gt; * 部分退款/取消，对应已出货；全部退款，对应已取消&lt;br /&gt; *_/&lt;/pre&gt;
   * @return orderStatus
  **/

  public String getOrderStatus() {
    return orderStatus;
  }

  public void setOrderStatus(String orderStatus) {
    this.orderStatus = orderStatus;
  }

  public OrderBaseResponseDTO supplierChannel(String supplierChannel) {
    this.supplierChannel = supplierChannel;
    return this;
  }

   /**
   * &lt;pre&gt;供应商渠道&lt;/pre&gt;
   * @return supplierChannel
  **/

  public String getSupplierChannel() {
    return supplierChannel;
  }

  public void setSupplierChannel(String supplierChannel) {
    this.supplierChannel = supplierChannel;
  }

  public OrderBaseResponseDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * Get message
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public OrderBaseResponseDTO paymentUniqueOrderNo(String paymentUniqueOrderNo) {
    this.paymentUniqueOrderNo = paymentUniqueOrderNo;
    return this;
  }

   /**
   * &lt;pre&gt;支付请求流水号&lt;/pre&gt;
   * @return paymentUniqueOrderNo
  **/

  public String getPaymentUniqueOrderNo() {
    return paymentUniqueOrderNo;
  }

  public void setPaymentUniqueOrderNo(String paymentUniqueOrderNo) {
    this.paymentUniqueOrderNo = paymentUniqueOrderNo;
  }

  public OrderBaseResponseDTO orderAmount(BigDecimal orderAmount) {
    this.orderAmount = orderAmount;
    return this;
  }

   /**
   * &lt;pre&gt;订单金额&lt;/pre&gt;
   * @return orderAmount
  **/

  public BigDecimal getOrderAmount() {
    return orderAmount;
  }

  public void setOrderAmount(BigDecimal orderAmount) {
    this.orderAmount = orderAmount;
  }

  public OrderBaseResponseDTO complateTime(String complateTime) {
    this.complateTime = complateTime;
    return this;
  }

   /**
   * &lt;pre&gt;完成时间。订单出货成功时返回&lt;/pre&gt;
   * @return complateTime
  **/

  public String getComplateTime() {
    return complateTime;
  }

  public void setComplateTime(String complateTime) {
    this.complateTime = complateTime;
  }

  public OrderBaseResponseDTO cancelTime(String cancelTime) {
    this.cancelTime = cancelTime;
    return this;
  }

   /**
   * &lt;p&gt;取消时间&lt;/p&gt;
   * @return cancelTime
  **/

  public String getCancelTime() {
    return cancelTime;
  }

  public void setCancelTime(String cancelTime) {
    this.cancelTime = cancelTime;
  }

  public OrderBaseResponseDTO orderSuccessTime(String orderSuccessTime) {
    this.orderSuccessTime = orderSuccessTime;
    return this;
  }

   /**
   * &lt;pre&gt;下单成功时间。下单成功时返回&lt;/pre&gt;
   * @return orderSuccessTime
  **/

  public String getOrderSuccessTime() {
    return orderSuccessTime;
  }

  public void setOrderSuccessTime(String orderSuccessTime) {
    this.orderSuccessTime = orderSuccessTime;
  }

  public OrderBaseResponseDTO systemOrderNo(String systemOrderNo) {
    this.systemOrderNo = systemOrderNo;
    return this;
  }

   /**
   * &lt;pre&gt;系统返回唯一订单号&lt;/pre&gt;
   * @return systemOrderNo
  **/

  public String getSystemOrderNo() {
    return systemOrderNo;
  }

  public void setSystemOrderNo(String systemOrderNo) {
    this.systemOrderNo = systemOrderNo;
  }

  public OrderBaseResponseDTO paySuccessTime(String paySuccessTime) {
    this.paySuccessTime = paySuccessTime;
    return this;
  }

   /**
   * &lt;pre&gt;支付成功时间。支付成功时返回&lt;/pre&gt;
   * @return paySuccessTime
  **/

  public String getPaySuccessTime() {
    return paySuccessTime;
  }

  public void setPaySuccessTime(String paySuccessTime) {
    this.paySuccessTime = paySuccessTime;
  }

  public OrderBaseResponseDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;请求商编&lt;/p&gt;
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public OrderBaseResponseDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public OrderBaseResponseDTO merchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;商户订单号&lt;/p&gt;
   * @return merchantRequestNo
  **/

  public String getMerchantRequestNo() {
    return merchantRequestNo;
  }

  public void setMerchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
  }

  public OrderBaseResponseDTO parentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
    return this;
  }

   /**
   * &lt;pre&gt;请求方订单号&lt;/pre&gt;
   * @return parentMerchantRequestNo
  **/

  public String getParentMerchantRequestNo() {
    return parentMerchantRequestNo;
  }

  public void setParentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
  }

  public OrderBaseResponseDTO supplierOrderNo(String supplierOrderNo) {
    this.supplierOrderNo = supplierOrderNo;
    return this;
  }

   /**
   * &lt;pre&gt;资源方订单号&lt;/pre&gt;
   * @return supplierOrderNo
  **/

  public String getSupplierOrderNo() {
    return supplierOrderNo;
  }

  public void setSupplierOrderNo(String supplierOrderNo) {
    this.supplierOrderNo = supplierOrderNo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    OrderBaseResponseDTO orderBaseResponseDTO = (OrderBaseResponseDTO) o;
    return ObjectUtils.equals(this.code, orderBaseResponseDTO.code) &&
    ObjectUtils.equals(this.orderStatus, orderBaseResponseDTO.orderStatus) &&
    ObjectUtils.equals(this.supplierChannel, orderBaseResponseDTO.supplierChannel) &&
    ObjectUtils.equals(this.message, orderBaseResponseDTO.message) &&
    ObjectUtils.equals(this.paymentUniqueOrderNo, orderBaseResponseDTO.paymentUniqueOrderNo) &&
    ObjectUtils.equals(this.orderAmount, orderBaseResponseDTO.orderAmount) &&
    ObjectUtils.equals(this.complateTime, orderBaseResponseDTO.complateTime) &&
    ObjectUtils.equals(this.cancelTime, orderBaseResponseDTO.cancelTime) &&
    ObjectUtils.equals(this.orderSuccessTime, orderBaseResponseDTO.orderSuccessTime) &&
    ObjectUtils.equals(this.systemOrderNo, orderBaseResponseDTO.systemOrderNo) &&
    ObjectUtils.equals(this.paySuccessTime, orderBaseResponseDTO.paySuccessTime) &&
    ObjectUtils.equals(this.parentMerchantNo, orderBaseResponseDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, orderBaseResponseDTO.merchantNo) &&
    ObjectUtils.equals(this.merchantRequestNo, orderBaseResponseDTO.merchantRequestNo) &&
    ObjectUtils.equals(this.parentMerchantRequestNo, orderBaseResponseDTO.parentMerchantRequestNo) &&
    ObjectUtils.equals(this.supplierOrderNo, orderBaseResponseDTO.supplierOrderNo);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, orderStatus, supplierChannel, message, paymentUniqueOrderNo, orderAmount, complateTime, cancelTime, orderSuccessTime, systemOrderNo, paySuccessTime, parentMerchantNo, merchantNo, merchantRequestNo, parentMerchantRequestNo, supplierOrderNo);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderBaseResponseDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    orderStatus: ").append(toIndentedString(orderStatus)).append("\n");
    sb.append("    supplierChannel: ").append(toIndentedString(supplierChannel)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    paymentUniqueOrderNo: ").append(toIndentedString(paymentUniqueOrderNo)).append("\n");
    sb.append("    orderAmount: ").append(toIndentedString(orderAmount)).append("\n");
    sb.append("    complateTime: ").append(toIndentedString(complateTime)).append("\n");
    sb.append("    cancelTime: ").append(toIndentedString(cancelTime)).append("\n");
    sb.append("    orderSuccessTime: ").append(toIndentedString(orderSuccessTime)).append("\n");
    sb.append("    systemOrderNo: ").append(toIndentedString(systemOrderNo)).append("\n");
    sb.append("    paySuccessTime: ").append(toIndentedString(paySuccessTime)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantRequestNo: ").append(toIndentedString(merchantRequestNo)).append("\n");
    sb.append("    parentMerchantRequestNo: ").append(toIndentedString(parentMerchantRequestNo)).append("\n");
    sb.append("    supplierOrderNo: ").append(toIndentedString(supplierOrderNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

