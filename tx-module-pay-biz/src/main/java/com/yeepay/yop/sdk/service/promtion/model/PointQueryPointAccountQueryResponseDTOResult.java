/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * PointQueryPointAccountQueryResponseDTOResult
 */
public class PointQueryPointAccountQueryResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 业务发起商编
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * 商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 商户用户ID
   */
  @JsonProperty("merchantUserNo")
  private String merchantUserNo = null;

  /**
   * 账户状态
   */
  @JsonProperty("pointAccountStatus")
  private String pointAccountStatus = null;

  /**
   * 易宝积分账户编号
   */
  @JsonProperty("pointAccountNo")
  private String pointAccountNo = null;

  /**
   * 账户类型
   */
  @JsonProperty("pointAccountType")
  private String pointAccountType = null;

  /**
   * 积分数
   */
  @JsonProperty("point")
  private BigDecimal point = null;

  public PointQueryPointAccountQueryResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public PointQueryPointAccountQueryResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public PointQueryPointAccountQueryResponseDTOResult parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * 业务发起商编
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public PointQueryPointAccountQueryResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public PointQueryPointAccountQueryResponseDTOResult merchantUserNo(String merchantUserNo) {
    this.merchantUserNo = merchantUserNo;
    return this;
  }

   /**
   * 商户用户ID
   * @return merchantUserNo
  **/

  public String getMerchantUserNo() {
    return merchantUserNo;
  }

  public void setMerchantUserNo(String merchantUserNo) {
    this.merchantUserNo = merchantUserNo;
  }

  public PointQueryPointAccountQueryResponseDTOResult pointAccountStatus(String pointAccountStatus) {
    this.pointAccountStatus = pointAccountStatus;
    return this;
  }

   /**
   * 账户状态
   * @return pointAccountStatus
  **/

  public String getPointAccountStatus() {
    return pointAccountStatus;
  }

  public void setPointAccountStatus(String pointAccountStatus) {
    this.pointAccountStatus = pointAccountStatus;
  }

  public PointQueryPointAccountQueryResponseDTOResult pointAccountNo(String pointAccountNo) {
    this.pointAccountNo = pointAccountNo;
    return this;
  }

   /**
   * 易宝积分账户编号
   * @return pointAccountNo
  **/

  public String getPointAccountNo() {
    return pointAccountNo;
  }

  public void setPointAccountNo(String pointAccountNo) {
    this.pointAccountNo = pointAccountNo;
  }

  public PointQueryPointAccountQueryResponseDTOResult pointAccountType(String pointAccountType) {
    this.pointAccountType = pointAccountType;
    return this;
  }

   /**
   * 账户类型
   * @return pointAccountType
  **/

  public String getPointAccountType() {
    return pointAccountType;
  }

  public void setPointAccountType(String pointAccountType) {
    this.pointAccountType = pointAccountType;
  }

  public PointQueryPointAccountQueryResponseDTOResult point(BigDecimal point) {
    this.point = point;
    return this;
  }

   /**
   * 积分数
   * @return point
  **/

  public BigDecimal getPoint() {
    return point;
  }

  public void setPoint(BigDecimal point) {
    this.point = point;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    PointQueryPointAccountQueryResponseDTOResult pointQueryPointAccountQueryResponseDTOResult = (PointQueryPointAccountQueryResponseDTOResult) o;
    return ObjectUtils.equals(this.code, pointQueryPointAccountQueryResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, pointQueryPointAccountQueryResponseDTOResult.message) &&
    ObjectUtils.equals(this.parentMerchantNo, pointQueryPointAccountQueryResponseDTOResult.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, pointQueryPointAccountQueryResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.merchantUserNo, pointQueryPointAccountQueryResponseDTOResult.merchantUserNo) &&
    ObjectUtils.equals(this.pointAccountStatus, pointQueryPointAccountQueryResponseDTOResult.pointAccountStatus) &&
    ObjectUtils.equals(this.pointAccountNo, pointQueryPointAccountQueryResponseDTOResult.pointAccountNo) &&
    ObjectUtils.equals(this.pointAccountType, pointQueryPointAccountQueryResponseDTOResult.pointAccountType) &&
    ObjectUtils.equals(this.point, pointQueryPointAccountQueryResponseDTOResult.point);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, parentMerchantNo, merchantNo, merchantUserNo, pointAccountStatus, pointAccountNo, pointAccountType, point);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PointQueryPointAccountQueryResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantUserNo: ").append(toIndentedString(merchantUserNo)).append("\n");
    sb.append("    pointAccountStatus: ").append(toIndentedString(pointAccountStatus)).append("\n");
    sb.append("    pointAccountNo: ").append(toIndentedString(pointAccountNo)).append("\n");
    sb.append("    pointAccountType: ").append(toIndentedString(pointAccountType)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

