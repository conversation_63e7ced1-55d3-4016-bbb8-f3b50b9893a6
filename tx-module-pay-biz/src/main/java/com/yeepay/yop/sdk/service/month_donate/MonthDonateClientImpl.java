/*
 * 公益-月捐
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.month_donate;

import com.yeepay.yop.sdk.client.*;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.HttpResponseAnalyzerSupport;
import com.yeepay.yop.sdk.http.HttpResponseHandler;
import com.yeepay.yop.sdk.http.handler.DefaultHttpResponseHandler;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.service.month_donate.request.*;
import com.yeepay.yop.sdk.service.month_donate.response.*;

public class MonthDonateClientImpl implements MonthDonateClient {

    private final ClientHandler clientHandler;

    MonthDonateClientImpl(ClientParams clientParams) {
        this.clientHandler = new ClientHandlerImpl(new ClientHandlerParams().withClientParams(clientParams));
    }

    @Override
    public ChangeResponse change(ChangeRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<ChangeRequest> requestMarshaller = ChangeRequestMarshaller.getInstance();
        HttpResponseHandler<ChangeResponse> responseHandler =
                new DefaultHttpResponseHandler<ChangeResponse>(ChangeResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<ChangeRequest, ChangeResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public CloseResponse close(CloseRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<CloseRequest> requestMarshaller = CloseRequestMarshaller.getInstance();
        HttpResponseHandler<CloseResponse> responseHandler =
                new DefaultHttpResponseHandler<CloseResponse>(CloseResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<CloseRequest, CloseResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public CreateUserResponse createUser(CreateUserRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<CreateUserRequest> requestMarshaller = CreateUserRequestMarshaller.getInstance();
        HttpResponseHandler<CreateUserResponse> responseHandler =
                new DefaultHttpResponseHandler<CreateUserResponse>(CreateUserResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<CreateUserRequest, CreateUserResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public OpenResponse open(OpenRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<OpenRequest> requestMarshaller = OpenRequestMarshaller.getInstance();
        HttpResponseHandler<OpenResponse> responseHandler =
                new DefaultHttpResponseHandler<OpenResponse>(OpenResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<OpenRequest, OpenResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public QueryOrderInfoResponse queryOrderInfo(QueryOrderInfoRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<QueryOrderInfoRequest> requestMarshaller = QueryOrderInfoRequestMarshaller.getInstance();
        HttpResponseHandler<QueryOrderInfoResponse> responseHandler =
                new DefaultHttpResponseHandler<QueryOrderInfoResponse>(QueryOrderInfoResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<QueryOrderInfoRequest, QueryOrderInfoResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public QuerySignInfoResponse querySignInfo(QuerySignInfoRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<QuerySignInfoRequest> requestMarshaller = QuerySignInfoRequestMarshaller.getInstance();
        HttpResponseHandler<QuerySignInfoResponse> responseHandler =
                new DefaultHttpResponseHandler<QuerySignInfoResponse>(QuerySignInfoResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<QuerySignInfoRequest, QuerySignInfoResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public QueryUserInfoResponse queryUserInfo(QueryUserInfoRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<QueryUserInfoRequest> requestMarshaller = QueryUserInfoRequestMarshaller.getInstance();
        HttpResponseHandler<QueryUserInfoResponse> responseHandler =
                new DefaultHttpResponseHandler<QueryUserInfoResponse>(QueryUserInfoResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<QueryUserInfoRequest, QueryUserInfoResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }

    @Override
    public void shutdown() {
        clientHandler.shutdown();
    }

}
