/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * SubsidyBackYopSubsidyBackResDTOResult
 */
public class SubsidyBackYopSubsidyBackResDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 易宝补贴订单号
   */
  @JsonProperty("subsidyOrderNo")
  private String subsidyOrderNo = null;

  /**
   * 易宝补贴退回订单号
   */
  @JsonProperty("subsidyBackOrderNo")
  private String subsidyBackOrderNo = null;

  /**
   * 处理状态
   */
  @JsonProperty("status")
  private String status = null;

  /**
   * 补贴退回金额
   */
  @JsonProperty("subsidyBackAmount")
  private String subsidyBackAmount = null;

  /**
   * 失败原因
   */
  @JsonProperty("failReason")
  private String failReason = null;

  public SubsidyBackYopSubsidyBackResDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public SubsidyBackYopSubsidyBackResDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回信息
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public SubsidyBackYopSubsidyBackResDTOResult subsidyOrderNo(String subsidyOrderNo) {
    this.subsidyOrderNo = subsidyOrderNo;
    return this;
  }

   /**
   * 易宝补贴订单号
   * @return subsidyOrderNo
  **/

  public String getSubsidyOrderNo() {
    return subsidyOrderNo;
  }

  public void setSubsidyOrderNo(String subsidyOrderNo) {
    this.subsidyOrderNo = subsidyOrderNo;
  }

  public SubsidyBackYopSubsidyBackResDTOResult subsidyBackOrderNo(String subsidyBackOrderNo) {
    this.subsidyBackOrderNo = subsidyBackOrderNo;
    return this;
  }

   /**
   * 易宝补贴退回订单号
   * @return subsidyBackOrderNo
  **/

  public String getSubsidyBackOrderNo() {
    return subsidyBackOrderNo;
  }

  public void setSubsidyBackOrderNo(String subsidyBackOrderNo) {
    this.subsidyBackOrderNo = subsidyBackOrderNo;
  }

  public SubsidyBackYopSubsidyBackResDTOResult status(String status) {
    this.status = status;
    return this;
  }

   /**
   * 处理状态
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public SubsidyBackYopSubsidyBackResDTOResult subsidyBackAmount(String subsidyBackAmount) {
    this.subsidyBackAmount = subsidyBackAmount;
    return this;
  }

   /**
   * 补贴退回金额
   * @return subsidyBackAmount
  **/

  public String getSubsidyBackAmount() {
    return subsidyBackAmount;
  }

  public void setSubsidyBackAmount(String subsidyBackAmount) {
    this.subsidyBackAmount = subsidyBackAmount;
  }

  public SubsidyBackYopSubsidyBackResDTOResult failReason(String failReason) {
    this.failReason = failReason;
    return this;
  }

   /**
   * 失败原因
   * @return failReason
  **/

  public String getFailReason() {
    return failReason;
  }

  public void setFailReason(String failReason) {
    this.failReason = failReason;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    SubsidyBackYopSubsidyBackResDTOResult subsidyBackYopSubsidyBackResDTOResult = (SubsidyBackYopSubsidyBackResDTOResult) o;
    return ObjectUtils.equals(this.code, subsidyBackYopSubsidyBackResDTOResult.code) &&
    ObjectUtils.equals(this.message, subsidyBackYopSubsidyBackResDTOResult.message) &&
    ObjectUtils.equals(this.subsidyOrderNo, subsidyBackYopSubsidyBackResDTOResult.subsidyOrderNo) &&
    ObjectUtils.equals(this.subsidyBackOrderNo, subsidyBackYopSubsidyBackResDTOResult.subsidyBackOrderNo) &&
    ObjectUtils.equals(this.status, subsidyBackYopSubsidyBackResDTOResult.status) &&
    ObjectUtils.equals(this.subsidyBackAmount, subsidyBackYopSubsidyBackResDTOResult.subsidyBackAmount) &&
    ObjectUtils.equals(this.failReason, subsidyBackYopSubsidyBackResDTOResult.failReason);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, subsidyOrderNo, subsidyBackOrderNo, status, subsidyBackAmount, failReason);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SubsidyBackYopSubsidyBackResDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    subsidyOrderNo: ").append(toIndentedString(subsidyOrderNo)).append("\n");
    sb.append("    subsidyBackOrderNo: ").append(toIndentedString(subsidyBackOrderNo)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    subsidyBackAmount: ").append(toIndentedString(subsidyBackAmount)).append("\n");
    sb.append("    failReason: ").append(toIndentedString(failReason)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

