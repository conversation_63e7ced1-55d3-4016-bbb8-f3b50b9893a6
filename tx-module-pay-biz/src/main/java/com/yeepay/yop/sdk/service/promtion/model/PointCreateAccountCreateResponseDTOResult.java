/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * PointCreateAccountCreateResponseDTOResult
 */
public class PointCreateAccountCreateResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 业务发起商编
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * 商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 商户用户ID
   */
  @JsonProperty("merchantUserNo")
  private String merchantUserNo = null;

  /**
   * 响应状态
   */
  @JsonProperty("status")
  private String status = null;

  /**
   * 易宝积分账户编号
   */
  @JsonProperty("pointAccountNo")
  private String pointAccountNo = null;

  /**
   * 账户类型
   */
  @JsonProperty("pointAccountType")
  private String pointAccountType = null;

  public PointCreateAccountCreateResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public PointCreateAccountCreateResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public PointCreateAccountCreateResponseDTOResult parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * 业务发起商编
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public PointCreateAccountCreateResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public PointCreateAccountCreateResponseDTOResult merchantUserNo(String merchantUserNo) {
    this.merchantUserNo = merchantUserNo;
    return this;
  }

   /**
   * 商户用户ID
   * @return merchantUserNo
  **/

  public String getMerchantUserNo() {
    return merchantUserNo;
  }

  public void setMerchantUserNo(String merchantUserNo) {
    this.merchantUserNo = merchantUserNo;
  }

  public PointCreateAccountCreateResponseDTOResult status(String status) {
    this.status = status;
    return this;
  }

   /**
   * 响应状态
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public PointCreateAccountCreateResponseDTOResult pointAccountNo(String pointAccountNo) {
    this.pointAccountNo = pointAccountNo;
    return this;
  }

   /**
   * 易宝积分账户编号
   * @return pointAccountNo
  **/

  public String getPointAccountNo() {
    return pointAccountNo;
  }

  public void setPointAccountNo(String pointAccountNo) {
    this.pointAccountNo = pointAccountNo;
  }

  public PointCreateAccountCreateResponseDTOResult pointAccountType(String pointAccountType) {
    this.pointAccountType = pointAccountType;
    return this;
  }

   /**
   * 账户类型
   * @return pointAccountType
  **/

  public String getPointAccountType() {
    return pointAccountType;
  }

  public void setPointAccountType(String pointAccountType) {
    this.pointAccountType = pointAccountType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    PointCreateAccountCreateResponseDTOResult pointCreateAccountCreateResponseDTOResult = (PointCreateAccountCreateResponseDTOResult) o;
    return ObjectUtils.equals(this.code, pointCreateAccountCreateResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, pointCreateAccountCreateResponseDTOResult.message) &&
    ObjectUtils.equals(this.parentMerchantNo, pointCreateAccountCreateResponseDTOResult.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, pointCreateAccountCreateResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.merchantUserNo, pointCreateAccountCreateResponseDTOResult.merchantUserNo) &&
    ObjectUtils.equals(this.status, pointCreateAccountCreateResponseDTOResult.status) &&
    ObjectUtils.equals(this.pointAccountNo, pointCreateAccountCreateResponseDTOResult.pointAccountNo) &&
    ObjectUtils.equals(this.pointAccountType, pointCreateAccountCreateResponseDTOResult.pointAccountType);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, parentMerchantNo, merchantNo, merchantUserNo, status, pointAccountNo, pointAccountType);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PointCreateAccountCreateResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantUserNo: ").append(toIndentedString(merchantUserNo)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    pointAccountNo: ").append(toIndentedString(pointAccountNo)).append("\n");
    sb.append("    pointAccountType: ").append(toIndentedString(pointAccountType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

