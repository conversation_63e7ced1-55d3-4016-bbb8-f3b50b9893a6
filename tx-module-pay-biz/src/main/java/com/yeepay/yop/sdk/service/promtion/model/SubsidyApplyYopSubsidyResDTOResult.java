/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * SubsidyApplyYopSubsidyResDTOResult
 */
public class SubsidyApplyYopSubsidyResDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 易宝补贴订单号
   */
  @JsonProperty("subsidyOrderNo")
  private String subsidyOrderNo = null;

  /**
   * 补贴状态
   */
  @JsonProperty("status")
  private String status = null;

  /**
   * 补贴金额
   */
  @JsonProperty("subsidyAmount")
  private String subsidyAmount = null;

  /**
   * 出资方商编
   */
  @JsonProperty("assumeMerchantNo")
  private String assumeMerchantNo = null;

  /**
   * 失败原因
   */
  @JsonProperty("failReason")
  private String failReason = null;

  public SubsidyApplyYopSubsidyResDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public SubsidyApplyYopSubsidyResDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回信息
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public SubsidyApplyYopSubsidyResDTOResult subsidyOrderNo(String subsidyOrderNo) {
    this.subsidyOrderNo = subsidyOrderNo;
    return this;
  }

   /**
   * 易宝补贴订单号
   * @return subsidyOrderNo
  **/

  public String getSubsidyOrderNo() {
    return subsidyOrderNo;
  }

  public void setSubsidyOrderNo(String subsidyOrderNo) {
    this.subsidyOrderNo = subsidyOrderNo;
  }

  public SubsidyApplyYopSubsidyResDTOResult status(String status) {
    this.status = status;
    return this;
  }

   /**
   * 补贴状态
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public SubsidyApplyYopSubsidyResDTOResult subsidyAmount(String subsidyAmount) {
    this.subsidyAmount = subsidyAmount;
    return this;
  }

   /**
   * 补贴金额
   * @return subsidyAmount
  **/

  public String getSubsidyAmount() {
    return subsidyAmount;
  }

  public void setSubsidyAmount(String subsidyAmount) {
    this.subsidyAmount = subsidyAmount;
  }

  public SubsidyApplyYopSubsidyResDTOResult assumeMerchantNo(String assumeMerchantNo) {
    this.assumeMerchantNo = assumeMerchantNo;
    return this;
  }

   /**
   * 出资方商编
   * @return assumeMerchantNo
  **/

  public String getAssumeMerchantNo() {
    return assumeMerchantNo;
  }

  public void setAssumeMerchantNo(String assumeMerchantNo) {
    this.assumeMerchantNo = assumeMerchantNo;
  }

  public SubsidyApplyYopSubsidyResDTOResult failReason(String failReason) {
    this.failReason = failReason;
    return this;
  }

   /**
   * 失败原因
   * @return failReason
  **/

  public String getFailReason() {
    return failReason;
  }

  public void setFailReason(String failReason) {
    this.failReason = failReason;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    SubsidyApplyYopSubsidyResDTOResult subsidyApplyYopSubsidyResDTOResult = (SubsidyApplyYopSubsidyResDTOResult) o;
    return ObjectUtils.equals(this.code, subsidyApplyYopSubsidyResDTOResult.code) &&
    ObjectUtils.equals(this.message, subsidyApplyYopSubsidyResDTOResult.message) &&
    ObjectUtils.equals(this.subsidyOrderNo, subsidyApplyYopSubsidyResDTOResult.subsidyOrderNo) &&
    ObjectUtils.equals(this.status, subsidyApplyYopSubsidyResDTOResult.status) &&
    ObjectUtils.equals(this.subsidyAmount, subsidyApplyYopSubsidyResDTOResult.subsidyAmount) &&
    ObjectUtils.equals(this.assumeMerchantNo, subsidyApplyYopSubsidyResDTOResult.assumeMerchantNo) &&
    ObjectUtils.equals(this.failReason, subsidyApplyYopSubsidyResDTOResult.failReason);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, subsidyOrderNo, status, subsidyAmount, assumeMerchantNo, failReason);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SubsidyApplyYopSubsidyResDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    subsidyOrderNo: ").append(toIndentedString(subsidyOrderNo)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    subsidyAmount: ").append(toIndentedString(subsidyAmount)).append("\n");
    sb.append("    assumeMerchantNo: ").append(toIndentedString(assumeMerchantNo)).append("\n");
    sb.append("    failReason: ").append(toIndentedString(failReason)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

