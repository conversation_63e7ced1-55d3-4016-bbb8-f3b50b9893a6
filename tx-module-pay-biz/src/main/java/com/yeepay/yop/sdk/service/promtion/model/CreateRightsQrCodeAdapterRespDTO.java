/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 请修改我
 */
public class CreateRightsQrCodeAdapterRespDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 
   */
  @JsonProperty("rightsQrCode")
  private String rightsQrCode = null;

  /**
   * 
   */
  @JsonProperty("expireTime")
  private String expireTime = null;

  /**
   * 
   */
  @JsonProperty("ruleDesc")
  private String ruleDesc = null;

  /**
   * 
   */
  @JsonProperty("rightsCode")
  private String rightsCode = null;

  /**
   * 
   */
  @JsonProperty("rightsTitle")
  private String rightsTitle = null;

  /**
   * 
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  public CreateRightsQrCodeAdapterRespDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * Get code
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public CreateRightsQrCodeAdapterRespDTO rightsQrCode(String rightsQrCode) {
    this.rightsQrCode = rightsQrCode;
    return this;
  }

   /**
   * Get rightsQrCode
   * @return rightsQrCode
  **/

  public String getRightsQrCode() {
    return rightsQrCode;
  }

  public void setRightsQrCode(String rightsQrCode) {
    this.rightsQrCode = rightsQrCode;
  }

  public CreateRightsQrCodeAdapterRespDTO expireTime(String expireTime) {
    this.expireTime = expireTime;
    return this;
  }

   /**
   * Get expireTime
   * @return expireTime
  **/

  public String getExpireTime() {
    return expireTime;
  }

  public void setExpireTime(String expireTime) {
    this.expireTime = expireTime;
  }

  public CreateRightsQrCodeAdapterRespDTO ruleDesc(String ruleDesc) {
    this.ruleDesc = ruleDesc;
    return this;
  }

   /**
   * Get ruleDesc
   * @return ruleDesc
  **/

  public String getRuleDesc() {
    return ruleDesc;
  }

  public void setRuleDesc(String ruleDesc) {
    this.ruleDesc = ruleDesc;
  }

  public CreateRightsQrCodeAdapterRespDTO rightsCode(String rightsCode) {
    this.rightsCode = rightsCode;
    return this;
  }

   /**
   * Get rightsCode
   * @return rightsCode
  **/

  public String getRightsCode() {
    return rightsCode;
  }

  public void setRightsCode(String rightsCode) {
    this.rightsCode = rightsCode;
  }

  public CreateRightsQrCodeAdapterRespDTO rightsTitle(String rightsTitle) {
    this.rightsTitle = rightsTitle;
    return this;
  }

   /**
   * Get rightsTitle
   * @return rightsTitle
  **/

  public String getRightsTitle() {
    return rightsTitle;
  }

  public void setRightsTitle(String rightsTitle) {
    this.rightsTitle = rightsTitle;
  }

  public CreateRightsQrCodeAdapterRespDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * Get message
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public CreateRightsQrCodeAdapterRespDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * Get merchantNo
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    CreateRightsQrCodeAdapterRespDTO createRightsQrCodeAdapterRespDTO = (CreateRightsQrCodeAdapterRespDTO) o;
    return ObjectUtils.equals(this.code, createRightsQrCodeAdapterRespDTO.code) &&
    ObjectUtils.equals(this.rightsQrCode, createRightsQrCodeAdapterRespDTO.rightsQrCode) &&
    ObjectUtils.equals(this.expireTime, createRightsQrCodeAdapterRespDTO.expireTime) &&
    ObjectUtils.equals(this.ruleDesc, createRightsQrCodeAdapterRespDTO.ruleDesc) &&
    ObjectUtils.equals(this.rightsCode, createRightsQrCodeAdapterRespDTO.rightsCode) &&
    ObjectUtils.equals(this.rightsTitle, createRightsQrCodeAdapterRespDTO.rightsTitle) &&
    ObjectUtils.equals(this.message, createRightsQrCodeAdapterRespDTO.message) &&
    ObjectUtils.equals(this.merchantNo, createRightsQrCodeAdapterRespDTO.merchantNo);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, rightsQrCode, expireTime, ruleDesc, rightsCode, rightsTitle, message, merchantNo);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CreateRightsQrCodeAdapterRespDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    rightsQrCode: ").append(toIndentedString(rightsQrCode)).append("\n");
    sb.append("    expireTime: ").append(toIndentedString(expireTime)).append("\n");
    sb.append("    ruleDesc: ").append(toIndentedString(ruleDesc)).append("\n");
    sb.append("    rightsCode: ").append(toIndentedString(rightsCode)).append("\n");
    sb.append("    rightsTitle: ").append(toIndentedString(rightsTitle)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

