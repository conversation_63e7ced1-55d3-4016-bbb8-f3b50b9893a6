/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.agency_operation.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 抽佣记录查询结果
 */
public class YopMultiChannelQueryWithholdRecordResDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;被抽佣方商编&lt;/p&gt;
   */
  @JsonProperty("contributeMerchantNo")
  private String contributeMerchantNo = null;

  /**
   * &lt;p&gt;抽佣接收方商编&lt;/p&gt;
   */
  @JsonProperty("receiveMerchantNo")
  private String receiveMerchantNo = null;

  /**
   * &lt;p&gt;请求号&lt;/p&gt;
   */
  @JsonProperty("requestNo")
  private String requestNo = null;

  /**
   * &lt;p&gt;抽佣状态&lt;/p&gt; &lt;p&gt;PROCESSING ： 处理中&lt;/p&gt; &lt;p&gt;FAIL ：失败&lt;/p&gt; &lt;p&gt;SUCCESS : 成功&lt;/p&gt;
   */
  @JsonProperty("status")
  private String status = null;

  /**
   * &lt;p&gt;手续费&lt;/p&gt;
   */
  @JsonProperty("fee")
  private BigDecimal fee = null;

  /**
   * &lt;p&gt;入账金额&lt;/p&gt;
   */
  @JsonProperty("ypSettleAmount")
  private BigDecimal ypSettleAmount = null;

  /**
   * &lt;p&gt;抽佣金额&lt;/p&gt;
   */
  @JsonProperty("withholdAmount")
  private BigDecimal withholdAmount = null;

  public YopMultiChannelQueryWithholdRecordResDTO contributeMerchantNo(String contributeMerchantNo) {
    this.contributeMerchantNo = contributeMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;被抽佣方商编&lt;/p&gt;
   * @return contributeMerchantNo
  **/

  public String getContributeMerchantNo() {
    return contributeMerchantNo;
  }

  public void setContributeMerchantNo(String contributeMerchantNo) {
    this.contributeMerchantNo = contributeMerchantNo;
  }

  public YopMultiChannelQueryWithholdRecordResDTO receiveMerchantNo(String receiveMerchantNo) {
    this.receiveMerchantNo = receiveMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;抽佣接收方商编&lt;/p&gt;
   * @return receiveMerchantNo
  **/

  public String getReceiveMerchantNo() {
    return receiveMerchantNo;
  }

  public void setReceiveMerchantNo(String receiveMerchantNo) {
    this.receiveMerchantNo = receiveMerchantNo;
  }

  public YopMultiChannelQueryWithholdRecordResDTO requestNo(String requestNo) {
    this.requestNo = requestNo;
    return this;
  }

   /**
   * &lt;p&gt;请求号&lt;/p&gt;
   * @return requestNo
  **/

  public String getRequestNo() {
    return requestNo;
  }

  public void setRequestNo(String requestNo) {
    this.requestNo = requestNo;
  }

  public YopMultiChannelQueryWithholdRecordResDTO status(String status) {
    this.status = status;
    return this;
  }

   /**
   * &lt;p&gt;抽佣状态&lt;/p&gt; &lt;p&gt;PROCESSING ： 处理中&lt;/p&gt; &lt;p&gt;FAIL ：失败&lt;/p&gt; &lt;p&gt;SUCCESS : 成功&lt;/p&gt;
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public YopMultiChannelQueryWithholdRecordResDTO fee(BigDecimal fee) {
    this.fee = fee;
    return this;
  }

   /**
   * &lt;p&gt;手续费&lt;/p&gt;
   * @return fee
  **/

  public BigDecimal getFee() {
    return fee;
  }

  public void setFee(BigDecimal fee) {
    this.fee = fee;
  }

  public YopMultiChannelQueryWithholdRecordResDTO ypSettleAmount(BigDecimal ypSettleAmount) {
    this.ypSettleAmount = ypSettleAmount;
    return this;
  }

   /**
   * &lt;p&gt;入账金额&lt;/p&gt;
   * @return ypSettleAmount
  **/

  public BigDecimal getYpSettleAmount() {
    return ypSettleAmount;
  }

  public void setYpSettleAmount(BigDecimal ypSettleAmount) {
    this.ypSettleAmount = ypSettleAmount;
  }

  public YopMultiChannelQueryWithholdRecordResDTO withholdAmount(BigDecimal withholdAmount) {
    this.withholdAmount = withholdAmount;
    return this;
  }

   /**
   * &lt;p&gt;抽佣金额&lt;/p&gt;
   * @return withholdAmount
  **/

  public BigDecimal getWithholdAmount() {
    return withholdAmount;
  }

  public void setWithholdAmount(BigDecimal withholdAmount) {
    this.withholdAmount = withholdAmount;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    YopMultiChannelQueryWithholdRecordResDTO yopMultiChannelQueryWithholdRecordResDTO = (YopMultiChannelQueryWithholdRecordResDTO) o;
    return ObjectUtils.equals(this.contributeMerchantNo, yopMultiChannelQueryWithholdRecordResDTO.contributeMerchantNo) &&
    ObjectUtils.equals(this.receiveMerchantNo, yopMultiChannelQueryWithholdRecordResDTO.receiveMerchantNo) &&
    ObjectUtils.equals(this.requestNo, yopMultiChannelQueryWithholdRecordResDTO.requestNo) &&
    ObjectUtils.equals(this.status, yopMultiChannelQueryWithholdRecordResDTO.status) &&
    ObjectUtils.equals(this.fee, yopMultiChannelQueryWithholdRecordResDTO.fee) &&
    ObjectUtils.equals(this.ypSettleAmount, yopMultiChannelQueryWithholdRecordResDTO.ypSettleAmount) &&
    ObjectUtils.equals(this.withholdAmount, yopMultiChannelQueryWithholdRecordResDTO.withholdAmount);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(contributeMerchantNo, receiveMerchantNo, requestNo, status, fee, ypSettleAmount, withholdAmount);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class YopMultiChannelQueryWithholdRecordResDTO {\n");
    
    sb.append("    contributeMerchantNo: ").append(toIndentedString(contributeMerchantNo)).append("\n");
    sb.append("    receiveMerchantNo: ").append(toIndentedString(receiveMerchantNo)).append("\n");
    sb.append("    requestNo: ").append(toIndentedString(requestNo)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    fee: ").append(toIndentedString(fee)).append("\n");
    sb.append("    ypSettleAmount: ").append(toIndentedString(ypSettleAmount)).append("\n");
    sb.append("    withholdAmount: ").append(toIndentedString(withholdAmount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

