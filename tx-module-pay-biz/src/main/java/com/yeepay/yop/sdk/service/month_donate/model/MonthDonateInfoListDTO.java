/*
 * 公益-月捐
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.month_donate.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.month_donate.model.MonthDonateInfoDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 月捐列表
 */
public class MonthDonateInfoListDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;总金额&lt;/p&gt;
   */
  @JsonProperty("totalAmount")
  private BigDecimal totalAmount = null;

  /**
   * &lt;p&gt;发起项目数&lt;/p&gt;
   */
  @JsonProperty("projectCount")
  private Integer projectCount = null;

  /**
   * 
   */
  @JsonProperty("monthDonateInfoDTOList")
  private List<MonthDonateInfoDTO> monthDonateInfoDTOList = null;

  /**
   * &lt;p&gt;响应code码&lt;/p&gt;
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * &lt;p&gt;月捐笔数&lt;/p&gt;
   */
  @JsonProperty("monthDonateCount")
  private Integer monthDonateCount = null;

  /**
   * &lt;p&gt;响应描述信息&lt;/p&gt;
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * &lt;p&gt;响应状态&lt;/p&gt;
   */
  @JsonProperty("status")
  private String status = null;

  public MonthDonateInfoListDTO totalAmount(BigDecimal totalAmount) {
    this.totalAmount = totalAmount;
    return this;
  }

   /**
   * &lt;p&gt;总金额&lt;/p&gt;
   * minimum: 0
   * @return totalAmount
  **/

  public BigDecimal getTotalAmount() {
    return totalAmount;
  }

  public void setTotalAmount(BigDecimal totalAmount) {
    this.totalAmount = totalAmount;
  }

  public MonthDonateInfoListDTO projectCount(Integer projectCount) {
    this.projectCount = projectCount;
    return this;
  }

   /**
   * &lt;p&gt;发起项目数&lt;/p&gt;
   * minimum: 0
   * @return projectCount
  **/

  public Integer getProjectCount() {
    return projectCount;
  }

  public void setProjectCount(Integer projectCount) {
    this.projectCount = projectCount;
  }

  public MonthDonateInfoListDTO monthDonateInfoDTOList(List<MonthDonateInfoDTO> monthDonateInfoDTOList) {
    this.monthDonateInfoDTOList = monthDonateInfoDTOList;
    return this;
  }

  public MonthDonateInfoListDTO addMonthDonateInfoDTOListItem(MonthDonateInfoDTO monthDonateInfoDTOListItem) {
    if (this.monthDonateInfoDTOList == null) {
      this.monthDonateInfoDTOList = new ArrayList<>();
    }
    this.monthDonateInfoDTOList.add(monthDonateInfoDTOListItem);
    return this;
  }

   /**
   * Get monthDonateInfoDTOList
   * @return monthDonateInfoDTOList
  **/

  public List<MonthDonateInfoDTO> getMonthDonateInfoDTOList() {
    return monthDonateInfoDTOList;
  }

  public void setMonthDonateInfoDTOList(List<MonthDonateInfoDTO> monthDonateInfoDTOList) {
    this.monthDonateInfoDTOList = monthDonateInfoDTOList;
  }

  public MonthDonateInfoListDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * &lt;p&gt;响应code码&lt;/p&gt;
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public MonthDonateInfoListDTO monthDonateCount(Integer monthDonateCount) {
    this.monthDonateCount = monthDonateCount;
    return this;
  }

   /**
   * &lt;p&gt;月捐笔数&lt;/p&gt;
   * minimum: 0
   * @return monthDonateCount
  **/

  public Integer getMonthDonateCount() {
    return monthDonateCount;
  }

  public void setMonthDonateCount(Integer monthDonateCount) {
    this.monthDonateCount = monthDonateCount;
  }

  public MonthDonateInfoListDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * &lt;p&gt;响应描述信息&lt;/p&gt;
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public MonthDonateInfoListDTO status(String status) {
    this.status = status;
    return this;
  }

   /**
   * &lt;p&gt;响应状态&lt;/p&gt;
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    MonthDonateInfoListDTO monthDonateInfoListDTO = (MonthDonateInfoListDTO) o;
    return ObjectUtils.equals(this.totalAmount, monthDonateInfoListDTO.totalAmount) &&
    ObjectUtils.equals(this.projectCount, monthDonateInfoListDTO.projectCount) &&
    ObjectUtils.equals(this.monthDonateInfoDTOList, monthDonateInfoListDTO.monthDonateInfoDTOList) &&
    ObjectUtils.equals(this.code, monthDonateInfoListDTO.code) &&
    ObjectUtils.equals(this.monthDonateCount, monthDonateInfoListDTO.monthDonateCount) &&
    ObjectUtils.equals(this.message, monthDonateInfoListDTO.message) &&
    ObjectUtils.equals(this.status, monthDonateInfoListDTO.status);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(totalAmount, projectCount, monthDonateInfoDTOList, code, monthDonateCount, message, status);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MonthDonateInfoListDTO {\n");
    
    sb.append("    totalAmount: ").append(toIndentedString(totalAmount)).append("\n");
    sb.append("    projectCount: ").append(toIndentedString(projectCount)).append("\n");
    sb.append("    monthDonateInfoDTOList: ").append(toIndentedString(monthDonateInfoDTOList)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    monthDonateCount: ").append(toIndentedString(monthDonateCount)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

