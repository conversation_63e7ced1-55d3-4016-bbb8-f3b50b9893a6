/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult
 */
public class YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 商编
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 订单token
   */
  @JsonProperty("token")
  private String token = null;

  /**
   * 支付记录流水号
   */
  @JsonProperty("recordId")
  private String recordId = null;

  /**
   * 验证码类型
   */
  @JsonProperty("verifyCodeType")
  private String verifyCodeType = null;

  /**
   * 补充银行密码场景
   */
  @JsonProperty("needItemScene")
  private String needItemScene = null;

  /**
   * 需补充项名称的集合
   */
  @JsonProperty("needItems")
  private String needItems = null;

  public YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回信息
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商编
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult token(String token) {
    this.token = token;
    return this;
  }

   /**
   * 订单token
   * @return token
  **/

  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  public YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult recordId(String recordId) {
    this.recordId = recordId;
    return this;
  }

   /**
   * 支付记录流水号
   * @return recordId
  **/

  public String getRecordId() {
    return recordId;
  }

  public void setRecordId(String recordId) {
    this.recordId = recordId;
  }

  public YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult verifyCodeType(String verifyCodeType) {
    this.verifyCodeType = verifyCodeType;
    return this;
  }

   /**
   * 验证码类型
   * @return verifyCodeType
  **/

  public String getVerifyCodeType() {
    return verifyCodeType;
  }

  public void setVerifyCodeType(String verifyCodeType) {
    this.verifyCodeType = verifyCodeType;
  }

  public YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult needItemScene(String needItemScene) {
    this.needItemScene = needItemScene;
    return this;
  }

   /**
   * 补充银行密码场景
   * @return needItemScene
  **/

  public String getNeedItemScene() {
    return needItemScene;
  }

  public void setNeedItemScene(String needItemScene) {
    this.needItemScene = needItemScene;
  }

  public YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult needItems(String needItems) {
    this.needItems = needItems;
    return this;
  }

   /**
   * 需补充项名称的集合
   * @return needItems
  **/

  public String getNeedItems() {
    return needItems;
  }

  public void setNeedItems(String needItems) {
    this.needItems = needItems;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult yjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult = (YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult) o;
    return ObjectUtils.equals(this.code, yjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, yjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult.message) &&
    ObjectUtils.equals(this.merchantNo, yjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.token, yjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult.token) &&
    ObjectUtils.equals(this.recordId, yjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult.recordId) &&
    ObjectUtils.equals(this.verifyCodeType, yjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult.verifyCodeType) &&
    ObjectUtils.equals(this.needItemScene, yjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult.needItemScene) &&
    ObjectUtils.equals(this.needItems, yjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult.needItems);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, merchantNo, token, recordId, verifyCodeType, needItemScene, needItems);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class YjzfBindpayrequestAPIYJZFBindPaymentResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    token: ").append(toIndentedString(token)).append("\n");
    sb.append("    recordId: ").append(toIndentedString(recordId)).append("\n");
    sb.append("    verifyCodeType: ").append(toIndentedString(verifyCodeType)).append("\n");
    sb.append("    needItemScene: ").append(toIndentedString(needItemScene)).append("\n");
    sb.append("    needItems: ").append(toIndentedString(needItems)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

