/*
 * 再处理
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.reprocess.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class MigrateBankOrderV10Request extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String migrateRequestId;

    private String orderId;

    private String uniqueOrderNo;

    private String migrateNotifyUrl;

    private String migrateAmount;

    private String accountLinkInfo;

    private String parentMerchantNo;

    private String merchantNo;


    /**
     * Get migrateRequestId
     * @return migrateRequestId
     **/
    
    public String getMigrateRequestId() {
        return migrateRequestId;
    }

    public void setMigrateRequestId(String migrateRequestId) {
        this.migrateRequestId = migrateRequestId;
    }

    /**
     * Get orderId
     * @return orderId
     **/
    
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * Get uniqueOrderNo
     * @return uniqueOrderNo
     **/
    
    public String getUniqueOrderNo() {
        return uniqueOrderNo;
    }

    public void setUniqueOrderNo(String uniqueOrderNo) {
        this.uniqueOrderNo = uniqueOrderNo;
    }

    /**
     * Get migrateNotifyUrl
     * @return migrateNotifyUrl
     **/
    
    public String getMigrateNotifyUrl() {
        return migrateNotifyUrl;
    }

    public void setMigrateNotifyUrl(String migrateNotifyUrl) {
        this.migrateNotifyUrl = migrateNotifyUrl;
    }

    /**
     * Get migrateAmount
     * @return migrateAmount
     **/
    
    public String getMigrateAmount() {
        return migrateAmount;
    }

    public void setMigrateAmount(String migrateAmount) {
        this.migrateAmount = migrateAmount;
    }

    /**
     * Get accountLinkInfo
     * @return accountLinkInfo
     **/
    
    public String getAccountLinkInfo() {
        return accountLinkInfo;
    }

    public void setAccountLinkInfo(String accountLinkInfo) {
        this.accountLinkInfo = accountLinkInfo;
    }

    /**
     * Get parentMerchantNo
     * @return parentMerchantNo
     **/
    
    public String getParentMerchantNo() {
        return parentMerchantNo;
    }

    public void setParentMerchantNo(String parentMerchantNo) {
        this.parentMerchantNo = parentMerchantNo;
    }

    /**
     * Get merchantNo
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    @Override
    public String getOperationId() {
        return "migrate_bank_order_v1_0";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
