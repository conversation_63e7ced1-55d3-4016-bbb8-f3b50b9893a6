/*
 * 公益-月捐
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.month_donate;


import com.yeepay.yop.sdk.auth.req.AuthorizationReqRegistry;
import com.yeepay.yop.sdk.auth.req.DefaultAuthorizationReqRegistry;
import com.yeepay.yop.sdk.client.ClientParams;
import com.yeepay.yop.sdk.client.AbstractServiceClientBuilder;

public class MonthDonateClientBuilder extends AbstractServiceClientBuilder<MonthDonateClientBuilder, MonthDonateClientImpl>{

    private static final AuthorizationReqRegistry REGISTRY;

    static {
        REGISTRY = new DefaultAuthorizationReqRegistry();
        REGISTRY.register("change", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("close", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("createUser", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("open", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("queryOrderInfo", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("querySignInfo", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("queryUserInfo", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
    }

    @Override
    protected AuthorizationReqRegistry authorizationReqRegistry() {
        return REGISTRY;
    }

    @Override
    protected MonthDonateClientImpl build(ClientParams params) {
        return new MonthDonateClientImpl(params);
    }

    /**
     * @return Create new instance of builder with all defaults set.
     */
    public static MonthDonateClientBuilder builder(){
        return new MonthDonateClientBuilder();
    }

}
