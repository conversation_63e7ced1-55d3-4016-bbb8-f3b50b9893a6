/*
 * 公益-月捐
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.month_donate.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 创建用户响应参数
 */
public class CreateUserResponseDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;响应code码&lt;/p&gt;
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * &lt;p&gt;用户编号：仅支持手机号&lt;/p&gt;
   */
  @JsonProperty("userNo")
  private String userNo = null;

  /**
   * &lt;p&gt;PHONE 手机号&lt;/p&gt;
   */
  @JsonProperty("userType")
  private String userType = null;

  /**
   * &lt;p&gt;响应描述信息&lt;/p&gt;
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * &lt;p&gt;用户ID&lt;/p&gt;
   */
  @JsonProperty("userId")
  private String userId = null;

  /**
   * &lt;p&gt;响应状态&lt;/p&gt; &lt;p&gt;SUCCESS&lt;/p&gt;
   */
  @JsonProperty("status")
  private String status = null;

  public CreateUserResponseDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * &lt;p&gt;响应code码&lt;/p&gt;
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public CreateUserResponseDTO userNo(String userNo) {
    this.userNo = userNo;
    return this;
  }

   /**
   * &lt;p&gt;用户编号：仅支持手机号&lt;/p&gt;
   * @return userNo
  **/

  public String getUserNo() {
    return userNo;
  }

  public void setUserNo(String userNo) {
    this.userNo = userNo;
  }

  public CreateUserResponseDTO userType(String userType) {
    this.userType = userType;
    return this;
  }

   /**
   * &lt;p&gt;PHONE 手机号&lt;/p&gt;
   * @return userType
  **/

  public String getUserType() {
    return userType;
  }

  public void setUserType(String userType) {
    this.userType = userType;
  }

  public CreateUserResponseDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * &lt;p&gt;响应描述信息&lt;/p&gt;
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public CreateUserResponseDTO userId(String userId) {
    this.userId = userId;
    return this;
  }

   /**
   * &lt;p&gt;用户ID&lt;/p&gt;
   * @return userId
  **/

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public CreateUserResponseDTO status(String status) {
    this.status = status;
    return this;
  }

   /**
   * &lt;p&gt;响应状态&lt;/p&gt; &lt;p&gt;SUCCESS&lt;/p&gt;
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    CreateUserResponseDTO createUserResponseDTO = (CreateUserResponseDTO) o;
    return ObjectUtils.equals(this.code, createUserResponseDTO.code) &&
    ObjectUtils.equals(this.userNo, createUserResponseDTO.userNo) &&
    ObjectUtils.equals(this.userType, createUserResponseDTO.userType) &&
    ObjectUtils.equals(this.message, createUserResponseDTO.message) &&
    ObjectUtils.equals(this.userId, createUserResponseDTO.userId) &&
    ObjectUtils.equals(this.status, createUserResponseDTO.status);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, userNo, userType, message, userId, status);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CreateUserResponseDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    userNo: ").append(toIndentedString(userNo)).append("\n");
    sb.append("    userType: ").append(toIndentedString(userType)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

