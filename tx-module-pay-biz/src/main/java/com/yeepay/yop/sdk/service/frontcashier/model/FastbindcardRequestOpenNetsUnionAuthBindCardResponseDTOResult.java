/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult
 */
public class FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 错误描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 商编
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 商户绑卡请求号
   */
  @JsonProperty("merchantFlowId")
  private String merchantFlowId = null;

  /**
   * 易宝绑卡唯一订单号
   */
  @JsonProperty("nopOrderId")
  private String nopOrderId = null;

  /**
   * 参数提交方式
   */
  @JsonProperty("submitMethod")
  private String submitMethod = null;

  /**
   * 鉴权请求地址
   */
  @JsonProperty("submitUrl")
  private String submitUrl = null;

  /**
   * url编码方式
   */
  @JsonProperty("encoding")
  private String encoding = null;

  public FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 错误描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商编
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult merchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
    return this;
  }

   /**
   * 商户绑卡请求号
   * @return merchantFlowId
  **/

  public String getMerchantFlowId() {
    return merchantFlowId;
  }

  public void setMerchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
  }

  public FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult nopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
    return this;
  }

   /**
   * 易宝绑卡唯一订单号
   * @return nopOrderId
  **/

  public String getNopOrderId() {
    return nopOrderId;
  }

  public void setNopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
  }

  public FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult submitMethod(String submitMethod) {
    this.submitMethod = submitMethod;
    return this;
  }

   /**
   * 参数提交方式
   * @return submitMethod
  **/

  public String getSubmitMethod() {
    return submitMethod;
  }

  public void setSubmitMethod(String submitMethod) {
    this.submitMethod = submitMethod;
  }

  public FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult submitUrl(String submitUrl) {
    this.submitUrl = submitUrl;
    return this;
  }

   /**
   * 鉴权请求地址
   * @return submitUrl
  **/

  public String getSubmitUrl() {
    return submitUrl;
  }

  public void setSubmitUrl(String submitUrl) {
    this.submitUrl = submitUrl;
  }

  public FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult encoding(String encoding) {
    this.encoding = encoding;
    return this;
  }

   /**
   * url编码方式
   * @return encoding
  **/

  public String getEncoding() {
    return encoding;
  }

  public void setEncoding(String encoding) {
    this.encoding = encoding;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult fastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult = (FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult) o;
    return ObjectUtils.equals(this.code, fastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, fastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult.message) &&
    ObjectUtils.equals(this.merchantNo, fastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.merchantFlowId, fastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult.merchantFlowId) &&
    ObjectUtils.equals(this.nopOrderId, fastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult.nopOrderId) &&
    ObjectUtils.equals(this.submitMethod, fastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult.submitMethod) &&
    ObjectUtils.equals(this.submitUrl, fastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult.submitUrl) &&
    ObjectUtils.equals(this.encoding, fastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult.encoding);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, merchantNo, merchantFlowId, nopOrderId, submitMethod, submitUrl, encoding);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FastbindcardRequestOpenNetsUnionAuthBindCardResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantFlowId: ").append(toIndentedString(merchantFlowId)).append("\n");
    sb.append("    nopOrderId: ").append(toIndentedString(nopOrderId)).append("\n");
    sb.append("    submitMethod: ").append(toIndentedString(submitMethod)).append("\n");
    sb.append("    submitUrl: ").append(toIndentedString(submitUrl)).append("\n");
    sb.append("    encoding: ").append(toIndentedString(encoding)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

