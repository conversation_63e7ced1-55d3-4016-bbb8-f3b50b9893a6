/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.agency_operation.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yeepay.yop.sdk.service.base.request.serializer.YopCustomSerializer;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 请修改我
 */
public class YopMultiChannelWithholdReqDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;抽佣接收方商编&lt;/p&gt;
   */
  @JsonProperty("receiveMerchantNo")
  private String receiveMerchantNo = null;

  /**
   * &lt;p&gt;抽佣比例,单位%&lt;br /&gt;&lt;br /&gt;示例：10，代表10%&lt;/p&gt;
   */
  @JsonProperty("withholdRatio")
  private BigDecimal withholdRatio = null;

  /**
   * &lt;p&gt;用户标识&lt;/p&gt;
   */
  @JsonProperty("userNo")
  private String userNo = null;

  /**
   * &lt;p&gt;被抽佣方商编&lt;/p&gt;
   */
  @JsonProperty("contributeMerchantNo")
  private String contributeMerchantNo = null;

  /**
   * &lt;p&gt;请求号&lt;/p&gt;
   */
  @JsonProperty("requestNo")
  private String requestNo = null;

  /**
   * &lt;p&gt;绑卡id&lt;/p&gt;
   */
  @JsonProperty("bindId")
  private Long bindId = null;

  /**
   * &lt;p&gt;提现id&lt;/p&gt;
   */
  @JsonProperty("withHoldId")
  private String withHoldId = null;

  /**
   * &lt;p&gt;资金处理类型&lt;/p&gt; &lt;p&gt;REAL_TIME(\&quot;实时订单,不需要分账(默认值)\&quot;)&lt;br /&gt;DELAY_SETTLE(\&quot;延迟分账\&quot;)&lt;/p&gt;
   */
  @JsonProperty("fundProcessType")
  private String fundProcessType = null;

  /**
   * &lt;p&gt;通知地址&lt;/p&gt;
   */
  @JsonProperty("notifyUrl")
  private String notifyUrl = null;

  /**
   * &lt;p&gt;用户标识类型&lt;/p&gt; &lt;p&gt;USER_ID:用户ID(默认值)&lt;/p&gt; &lt;p&gt;IMEI:imei MAC:网卡地址&lt;/p&gt; &lt;p&gt;EMAIL:用户注册email&lt;/p&gt; &lt;p&gt;PHONE:用户注册手机号&lt;/p&gt; &lt;p&gt;ID_CARD:用户身份证号&lt;/p&gt; &lt;p&gt;AGREEMENT_NO:用户纸质订单协议号&lt;/p&gt; &lt;p&gt;WECHAT:微信号&lt;/p&gt;
   */
  @JsonProperty("userType")
  private String userType = null;

  public YopMultiChannelWithholdReqDTO receiveMerchantNo(String receiveMerchantNo) {
    this.receiveMerchantNo = receiveMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;抽佣接收方商编&lt;/p&gt;
   * @return receiveMerchantNo
  **/

  public String getReceiveMerchantNo() {
    return receiveMerchantNo;
  }

  public void setReceiveMerchantNo(String receiveMerchantNo) {
    this.receiveMerchantNo = receiveMerchantNo;
  }

  public YopMultiChannelWithholdReqDTO withholdRatio(BigDecimal withholdRatio) {
    this.withholdRatio = withholdRatio;
    return this;
  }

   /**
   * &lt;p&gt;抽佣比例,单位%&lt;br /&gt;&lt;br /&gt;示例：10，代表10%&lt;/p&gt;
   * @return withholdRatio
  **/

  public BigDecimal getWithholdRatio() {
    return withholdRatio;
  }

  public void setWithholdRatio(BigDecimal withholdRatio) {
    this.withholdRatio = withholdRatio;
  }

  public YopMultiChannelWithholdReqDTO userNo(String userNo) {
    this.userNo = userNo;
    return this;
  }

   /**
   * &lt;p&gt;用户标识&lt;/p&gt;
   * @return userNo
  **/

  public String getUserNo() {
    return userNo;
  }

  public void setUserNo(String userNo) {
    this.userNo = userNo;
  }

  public YopMultiChannelWithholdReqDTO contributeMerchantNo(String contributeMerchantNo) {
    this.contributeMerchantNo = contributeMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;被抽佣方商编&lt;/p&gt;
   * @return contributeMerchantNo
  **/

  public String getContributeMerchantNo() {
    return contributeMerchantNo;
  }

  public void setContributeMerchantNo(String contributeMerchantNo) {
    this.contributeMerchantNo = contributeMerchantNo;
  }

  public YopMultiChannelWithholdReqDTO requestNo(String requestNo) {
    this.requestNo = requestNo;
    return this;
  }

   /**
   * &lt;p&gt;请求号&lt;/p&gt;
   * @return requestNo
  **/

  public String getRequestNo() {
    return requestNo;
  }

  public void setRequestNo(String requestNo) {
    this.requestNo = requestNo;
  }

  public YopMultiChannelWithholdReqDTO bindId(Long bindId) {
    this.bindId = bindId;
    return this;
  }

   /**
   * &lt;p&gt;绑卡id&lt;/p&gt;
   * @return bindId
  **/

  public Long getBindId() {
    return bindId;
  }

  public void setBindId(Long bindId) {
    this.bindId = bindId;
  }

  public YopMultiChannelWithholdReqDTO withHoldId(String withHoldId) {
    this.withHoldId = withHoldId;
    return this;
  }

   /**
   * &lt;p&gt;提现id&lt;/p&gt;
   * @return withHoldId
  **/

  public String getWithHoldId() {
    return withHoldId;
  }

  public void setWithHoldId(String withHoldId) {
    this.withHoldId = withHoldId;
  }

  public YopMultiChannelWithholdReqDTO fundProcessType(String fundProcessType) {
    this.fundProcessType = fundProcessType;
    return this;
  }

   /**
   * &lt;p&gt;资金处理类型&lt;/p&gt; &lt;p&gt;REAL_TIME(\&quot;实时订单,不需要分账(默认值)\&quot;)&lt;br /&gt;DELAY_SETTLE(\&quot;延迟分账\&quot;)&lt;/p&gt;
   * @return fundProcessType
  **/

  public String getFundProcessType() {
    return fundProcessType;
  }

  public void setFundProcessType(String fundProcessType) {
    this.fundProcessType = fundProcessType;
  }

  public YopMultiChannelWithholdReqDTO notifyUrl(String notifyUrl) {
    this.notifyUrl = notifyUrl;
    return this;
  }

   /**
   * &lt;p&gt;通知地址&lt;/p&gt;
   * @return notifyUrl
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getNotifyUrl() {
    return notifyUrl;
  }

  public void setNotifyUrl(String notifyUrl) {
    this.notifyUrl = notifyUrl;
  }

  public YopMultiChannelWithholdReqDTO userType(String userType) {
    this.userType = userType;
    return this;
  }

   /**
   * &lt;p&gt;用户标识类型&lt;/p&gt; &lt;p&gt;USER_ID:用户ID(默认值)&lt;/p&gt; &lt;p&gt;IMEI:imei MAC:网卡地址&lt;/p&gt; &lt;p&gt;EMAIL:用户注册email&lt;/p&gt; &lt;p&gt;PHONE:用户注册手机号&lt;/p&gt; &lt;p&gt;ID_CARD:用户身份证号&lt;/p&gt; &lt;p&gt;AGREEMENT_NO:用户纸质订单协议号&lt;/p&gt; &lt;p&gt;WECHAT:微信号&lt;/p&gt;
   * @return userType
  **/

  public String getUserType() {
    return userType;
  }

  public void setUserType(String userType) {
    this.userType = userType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    YopMultiChannelWithholdReqDTO yopMultiChannelWithholdReqDTO = (YopMultiChannelWithholdReqDTO) o;
    return ObjectUtils.equals(this.receiveMerchantNo, yopMultiChannelWithholdReqDTO.receiveMerchantNo) &&
    ObjectUtils.equals(this.withholdRatio, yopMultiChannelWithholdReqDTO.withholdRatio) &&
    ObjectUtils.equals(this.userNo, yopMultiChannelWithholdReqDTO.userNo) &&
    ObjectUtils.equals(this.contributeMerchantNo, yopMultiChannelWithholdReqDTO.contributeMerchantNo) &&
    ObjectUtils.equals(this.requestNo, yopMultiChannelWithholdReqDTO.requestNo) &&
    ObjectUtils.equals(this.bindId, yopMultiChannelWithholdReqDTO.bindId) &&
    ObjectUtils.equals(this.withHoldId, yopMultiChannelWithholdReqDTO.withHoldId) &&
    ObjectUtils.equals(this.fundProcessType, yopMultiChannelWithholdReqDTO.fundProcessType) &&
    ObjectUtils.equals(this.notifyUrl, yopMultiChannelWithholdReqDTO.notifyUrl) &&
    ObjectUtils.equals(this.userType, yopMultiChannelWithholdReqDTO.userType);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(receiveMerchantNo, withholdRatio, userNo, contributeMerchantNo, requestNo, bindId, withHoldId, fundProcessType, notifyUrl, userType);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class YopMultiChannelWithholdReqDTO {\n");
    
    sb.append("    receiveMerchantNo: ").append(toIndentedString(receiveMerchantNo)).append("\n");
    sb.append("    withholdRatio: ").append(toIndentedString(withholdRatio)).append("\n");
    sb.append("    userNo: ").append(toIndentedString(userNo)).append("\n");
    sb.append("    contributeMerchantNo: ").append(toIndentedString(contributeMerchantNo)).append("\n");
    sb.append("    requestNo: ").append(toIndentedString(requestNo)).append("\n");
    sb.append("    bindId: ").append(toIndentedString(bindId)).append("\n");
    sb.append("    withHoldId: ").append(toIndentedString(withHoldId)).append("\n");
    sb.append("    fundProcessType: ").append(toIndentedString(fundProcessType)).append("\n");
    sb.append("    notifyUrl: ").append(toIndentedString(notifyUrl)).append("\n");
    sb.append("    userType: ").append(toIndentedString(userType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

