/*
 * API收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.nccashierapi.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class ApiPayRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String payTool;

    private String payType;

    private String token;

    private String appId;

    private String openId;

    private String version;

    private String payEmpowerNo;

    private String merchantTerminalId;

    private String merchantStoreNo;

    private String userIp;

    private String extParamMap;

    private String userNo;

    private String userType;


    /**
     * Get payTool
     * @return payTool
     **/
    
    public String getPayTool() {
        return payTool;
    }

    public void setPayTool(String payTool) {
        this.payTool = payTool;
    }

    /**
     * Get payType
     * @return payType
     **/
    
    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    /**
     * Get token
     * @return token
     **/
    
    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    /**
     * Get appId
     * @return appId
     **/
    
    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    /**
     * Get openId
     * @return openId
     **/
    
    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    /**
     * Get version
     * @return version
     **/
    
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * Get payEmpowerNo
     * @return payEmpowerNo
     **/
    
    public String getPayEmpowerNo() {
        return payEmpowerNo;
    }

    public void setPayEmpowerNo(String payEmpowerNo) {
        this.payEmpowerNo = payEmpowerNo;
    }

    /**
     * Get merchantTerminalId
     * @return merchantTerminalId
     **/
    
    public String getMerchantTerminalId() {
        return merchantTerminalId;
    }

    public void setMerchantTerminalId(String merchantTerminalId) {
        this.merchantTerminalId = merchantTerminalId;
    }

    /**
     * Get merchantStoreNo
     * @return merchantStoreNo
     **/
    
    public String getMerchantStoreNo() {
        return merchantStoreNo;
    }

    public void setMerchantStoreNo(String merchantStoreNo) {
        this.merchantStoreNo = merchantStoreNo;
    }

    /**
     * Get userIp
     * @return userIp
     **/
    
    public String getUserIp() {
        return userIp;
    }

    public void setUserIp(String userIp) {
        this.userIp = userIp;
    }

    /**
     * Get extParamMap
     * @return extParamMap
     **/
    
    public String getExtParamMap() {
        return extParamMap;
    }

    public void setExtParamMap(String extParamMap) {
        this.extParamMap = extParamMap;
    }

    /**
     * Get userNo
     * @return userNo
     **/
    
    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    /**
     * Get userType
     * @return userType
     **/
    
    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    @Override
    public String getOperationId() {
        return "apiPay";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
