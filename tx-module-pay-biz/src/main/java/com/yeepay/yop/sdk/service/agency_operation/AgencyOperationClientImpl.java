/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.agency_operation;

import com.yeepay.yop.sdk.client.*;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.HttpResponseAnalyzerSupport;
import com.yeepay.yop.sdk.http.HttpResponseHandler;
import com.yeepay.yop.sdk.http.handler.DefaultHttpResponseHandler;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.service.agency_operation.request.*;
import com.yeepay.yop.sdk.service.agency_operation.response.*;

public class AgencyOperationClientImpl implements AgencyOperationClient {

    private final ClientHandler clientHandler;

    AgencyOperationClientImpl(ClientParams clientParams) {
        this.clientHandler = new ClientHandlerImpl(new ClientHandlerParams().withClientParams(clientParams));
    }

    @Override
    public ShopBindResponse shopBind(ShopBindRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<ShopBindRequest> requestMarshaller = ShopBindRequestMarshaller.getInstance();
        HttpResponseHandler<ShopBindResponse> responseHandler =
                new DefaultHttpResponseHandler<ShopBindResponse>(ShopBindResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<ShopBindRequest, ShopBindResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public ShopBindQueryResponse shopBindQuery(ShopBindQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<ShopBindQueryRequest> requestMarshaller = ShopBindQueryRequestMarshaller.getInstance();
        HttpResponseHandler<ShopBindQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<ShopBindQueryResponse>(ShopBindQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<ShopBindQueryRequest, ShopBindQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WithholdResponse withhold(WithholdRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WithholdRequest> requestMarshaller = WithholdRequestMarshaller.getInstance();
        HttpResponseHandler<WithholdResponse> responseHandler =
                new DefaultHttpResponseHandler<WithholdResponse>(WithholdResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WithholdRequest, WithholdResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WithholdQueryResponse withholdQuery(WithholdQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WithholdQueryRequest> requestMarshaller = WithholdQueryRequestMarshaller.getInstance();
        HttpResponseHandler<WithholdQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<WithholdQueryResponse>(WithholdQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WithholdQueryRequest, WithholdQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WithholdRecordQueryV10Response withhold_record_query_v1_0(WithholdRecordQueryV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WithholdRecordQueryV10Request> requestMarshaller = WithholdRecordQueryV10RequestMarshaller.getInstance();
        HttpResponseHandler<WithholdRecordQueryV10Response> responseHandler =
                new DefaultHttpResponseHandler<WithholdRecordQueryV10Response>(WithholdRecordQueryV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WithholdRecordQueryV10Request, WithholdRecordQueryV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WithholdShopBindQueryV10Response withhold_shop_bind_query_v1_0(WithholdShopBindQueryV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WithholdShopBindQueryV10Request> requestMarshaller = WithholdShopBindQueryV10RequestMarshaller.getInstance();
        HttpResponseHandler<WithholdShopBindQueryV10Response> responseHandler =
                new DefaultHttpResponseHandler<WithholdShopBindQueryV10Response>(WithholdShopBindQueryV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WithholdShopBindQueryV10Request, WithholdShopBindQueryV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WithholdShopBindV10Response withhold_shop_bind_v1_0(WithholdShopBindV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WithholdShopBindV10Request> requestMarshaller = WithholdShopBindV10RequestMarshaller.getInstance();
        HttpResponseHandler<WithholdShopBindV10Response> responseHandler =
                new DefaultHttpResponseHandler<WithholdShopBindV10Response>(WithholdShopBindV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WithholdShopBindV10Request, WithholdShopBindV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WithholdV10Response withhold_v1_0(WithholdV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WithholdV10Request> requestMarshaller = WithholdV10RequestMarshaller.getInstance();
        HttpResponseHandler<WithholdV10Response> responseHandler =
                new DefaultHttpResponseHandler<WithholdV10Response>(WithholdV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WithholdV10Request, WithholdV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }

    @Override
    public void shutdown() {
        clientHandler.shutdown();
    }

}
