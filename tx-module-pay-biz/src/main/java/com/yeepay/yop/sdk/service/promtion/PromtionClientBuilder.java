/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.promtion;


import com.yeepay.yop.sdk.auth.req.AuthorizationReqRegistry;
import com.yeepay.yop.sdk.auth.req.DefaultAuthorizationReqRegistry;
import com.yeepay.yop.sdk.client.ClientParams;
import com.yeepay.yop.sdk.client.AbstractServiceClientBuilder;

public class PromtionClientBuilder extends AbstractServiceClientBuilder<PromtionClientBuilder, PromtionClientImpl>{

    private static final AuthorizationReqRegistry REGISTRY;

    static {
        REGISTRY = new DefaultAuthorizationReqRegistry();
        REGISTRY.register("activityListQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("addRights", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("add_rights", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("couponApply", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("couponListQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("create_rights_qrcode_adapter", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("frozen_rights", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("pointCreate", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("pointOperate", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("pointQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("query_consume_record_adapter", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("query_rights", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("rightsCreateQrcode", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("rightsFrozenRights", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("rightsQueryConsumeRecords", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("rightsQueryRights", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("rightsTransfer", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("rights_transfer", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("subsidyApply", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("subsidyBack", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("subsidyBackQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("subsidyQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
    }

    @Override
    protected AuthorizationReqRegistry authorizationReqRegistry() {
        return REGISTRY;
    }

    @Override
    protected PromtionClientImpl build(ClientParams params) {
        return new PromtionClientImpl(params);
    }

    /**
     * @return Create new instance of builder with all defaults set.
     */
    public static PromtionClientBuilder builder(){
        return new PromtionClientBuilder();
    }

}
