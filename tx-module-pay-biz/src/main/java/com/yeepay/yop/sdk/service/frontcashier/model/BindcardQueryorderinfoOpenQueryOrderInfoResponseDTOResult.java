/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult
 */
public class BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 响应码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 响应描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 商户签约请求号
   */
  @JsonProperty("merchantFlowId")
  private String merchantFlowId = null;

  /**
   * 易宝订单号
   */
  @JsonProperty("nopOrderId")
  private String nopOrderId = null;

  /**
   * 订单状态
   */
  @JsonProperty("orderStatus")
  private String orderStatus = null;

  /**
   * 用户标识
   */
  @JsonProperty("userNo")
  private String userNo = null;

  /**
   * 用户标识类型
   */
  @JsonProperty("userType")
  private String userType = null;

  /**
   * 绑卡ID
   */
  @JsonProperty("bindId")
  private String bindId = null;

  public BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 响应码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 响应描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult merchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
    return this;
  }

   /**
   * 商户签约请求号
   * @return merchantFlowId
  **/

  public String getMerchantFlowId() {
    return merchantFlowId;
  }

  public void setMerchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
  }

  public BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult nopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
    return this;
  }

   /**
   * 易宝订单号
   * @return nopOrderId
  **/

  public String getNopOrderId() {
    return nopOrderId;
  }

  public void setNopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
  }

  public BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult orderStatus(String orderStatus) {
    this.orderStatus = orderStatus;
    return this;
  }

   /**
   * 订单状态
   * @return orderStatus
  **/

  public String getOrderStatus() {
    return orderStatus;
  }

  public void setOrderStatus(String orderStatus) {
    this.orderStatus = orderStatus;
  }

  public BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult userNo(String userNo) {
    this.userNo = userNo;
    return this;
  }

   /**
   * 用户标识
   * @return userNo
  **/

  public String getUserNo() {
    return userNo;
  }

  public void setUserNo(String userNo) {
    this.userNo = userNo;
  }

  public BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult userType(String userType) {
    this.userType = userType;
    return this;
  }

   /**
   * 用户标识类型
   * @return userType
  **/

  public String getUserType() {
    return userType;
  }

  public void setUserType(String userType) {
    this.userType = userType;
  }

  public BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult bindId(String bindId) {
    this.bindId = bindId;
    return this;
  }

   /**
   * 绑卡ID
   * @return bindId
  **/

  public String getBindId() {
    return bindId;
  }

  public void setBindId(String bindId) {
    this.bindId = bindId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult bindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult = (BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult) o;
    return ObjectUtils.equals(this.code, bindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, bindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult.message) &&
    ObjectUtils.equals(this.merchantNo, bindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.merchantFlowId, bindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult.merchantFlowId) &&
    ObjectUtils.equals(this.nopOrderId, bindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult.nopOrderId) &&
    ObjectUtils.equals(this.orderStatus, bindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult.orderStatus) &&
    ObjectUtils.equals(this.userNo, bindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult.userNo) &&
    ObjectUtils.equals(this.userType, bindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult.userType) &&
    ObjectUtils.equals(this.bindId, bindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult.bindId);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, merchantNo, merchantFlowId, nopOrderId, orderStatus, userNo, userType, bindId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindcardQueryorderinfoOpenQueryOrderInfoResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantFlowId: ").append(toIndentedString(merchantFlowId)).append("\n");
    sb.append("    nopOrderId: ").append(toIndentedString(nopOrderId)).append("\n");
    sb.append("    orderStatus: ").append(toIndentedString(orderStatus)).append("\n");
    sb.append("    userNo: ").append(toIndentedString(userNo)).append("\n");
    sb.append("    userType: ").append(toIndentedString(userType)).append("\n");
    sb.append("    bindId: ").append(toIndentedString(bindId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

