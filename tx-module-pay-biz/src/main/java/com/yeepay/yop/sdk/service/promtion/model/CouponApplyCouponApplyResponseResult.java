/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * CouponApplyCouponApplyResponseResult
 */
public class CouponApplyCouponApplyResponseResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 响应码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 响应描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 优惠券码
   */
  @JsonProperty("couponNo")
  private String couponNo = null;

  public CouponApplyCouponApplyResponseResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 响应码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public CouponApplyCouponApplyResponseResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 响应描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public CouponApplyCouponApplyResponseResult couponNo(String couponNo) {
    this.couponNo = couponNo;
    return this;
  }

   /**
   * 优惠券码
   * @return couponNo
  **/

  public String getCouponNo() {
    return couponNo;
  }

  public void setCouponNo(String couponNo) {
    this.couponNo = couponNo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    CouponApplyCouponApplyResponseResult couponApplyCouponApplyResponseResult = (CouponApplyCouponApplyResponseResult) o;
    return ObjectUtils.equals(this.code, couponApplyCouponApplyResponseResult.code) &&
    ObjectUtils.equals(this.message, couponApplyCouponApplyResponseResult.message) &&
    ObjectUtils.equals(this.couponNo, couponApplyCouponApplyResponseResult.couponNo);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, couponNo);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CouponApplyCouponApplyResponseResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    couponNo: ").append(toIndentedString(couponNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

