/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.agency_operation.request;

import com.yeepay.yop.sdk.service.agency_operation.model.YopQueryMerchantShopBindReqDTO;
public class WithholdShopBindQueryV10Request extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private YopQueryMerchantShopBindReqDTO body;


    /**
     * Get body
     * @return body
     **/
    
    public YopQueryMerchantShopBindReqDTO getBody() {
        return body;
    }

    public void setBody(YopQueryMerchantShopBindReqDTO body) {
        this.body = body;
    }

    @Override
    public String getOperationId() {
        return "withhold_shop_bind_query_v1_0";
    }
}
