/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.agency_operation;


import com.yeepay.yop.sdk.auth.req.AuthorizationReqRegistry;
import com.yeepay.yop.sdk.auth.req.DefaultAuthorizationReqRegistry;
import com.yeepay.yop.sdk.client.ClientParams;
import com.yeepay.yop.sdk.client.AbstractServiceClientBuilder;

public class AgencyOperationClientBuilder extends AbstractServiceClientBuilder<AgencyOperationClientBuilder, AgencyOperationClientImpl>{

    private static final AuthorizationReqRegistry REGISTRY;

    static {
        REGISTRY = new DefaultAuthorizationReqRegistry();
        REGISTRY.register("shopBind", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("shopBindQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("withhold", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("withholdQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("withhold_record_query_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("withhold_shop_bind_query_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("withhold_shop_bind_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("withhold_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
    }

    @Override
    protected AuthorizationReqRegistry authorizationReqRegistry() {
        return REGISTRY;
    }

    @Override
    protected AgencyOperationClientImpl build(ClientParams params) {
        return new AgencyOperationClientImpl(params);
    }

    /**
     * @return Create new instance of builder with all defaults set.
     */
    public static AgencyOperationClientBuilder builder(){
        return new AgencyOperationClientBuilder();
    }

}
