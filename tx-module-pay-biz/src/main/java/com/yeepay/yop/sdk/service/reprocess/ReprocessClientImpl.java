/*
 * 再处理
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.reprocess;

import com.yeepay.yop.sdk.client.*;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.HttpResponseAnalyzerSupport;
import com.yeepay.yop.sdk.http.HttpResponseHandler;
import com.yeepay.yop.sdk.http.handler.DefaultHttpResponseHandler;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.service.reprocess.request.*;
import com.yeepay.yop.sdk.service.reprocess.response.*;

public class ReprocessClientImpl implements ReprocessClient {

    private final ClientHandler clientHandler;

    ReprocessClientImpl(ClientParams clientParams) {
        this.clientHandler = new ClientHandlerImpl(new ClientHandlerParams().withClientParams(clientParams));
    }

    @Override
    public MigrateBankOrderResponse migrateBankOrder(MigrateBankOrderRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<MigrateBankOrderRequest> requestMarshaller = MigrateBankOrderRequestMarshaller.getInstance();
        HttpResponseHandler<MigrateBankOrderResponse> responseHandler =
                new DefaultHttpResponseHandler<MigrateBankOrderResponse>(MigrateBankOrderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<MigrateBankOrderRequest, MigrateBankOrderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public MigrateBankQueryResponse migrateBankQuery(MigrateBankQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<MigrateBankQueryRequest> requestMarshaller = MigrateBankQueryRequestMarshaller.getInstance();
        HttpResponseHandler<MigrateBankQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<MigrateBankQueryResponse>(MigrateBankQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<MigrateBankQueryRequest, MigrateBankQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public MigrateBankOrderV10Response migrate_bank_order_v1_0(MigrateBankOrderV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<MigrateBankOrderV10Request> requestMarshaller = MigrateBankOrderV10RequestMarshaller.getInstance();
        HttpResponseHandler<MigrateBankOrderV10Response> responseHandler =
                new DefaultHttpResponseHandler<MigrateBankOrderV10Response>(MigrateBankOrderV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<MigrateBankOrderV10Request, MigrateBankOrderV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public MigrateBankQueryV10Response migrate_bank_query_v1_0(MigrateBankQueryV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<MigrateBankQueryV10Request> requestMarshaller = MigrateBankQueryV10RequestMarshaller.getInstance();
        HttpResponseHandler<MigrateBankQueryV10Response> responseHandler =
                new DefaultHttpResponseHandler<MigrateBankQueryV10Response>(MigrateBankQueryV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<MigrateBankQueryV10Request, MigrateBankQueryV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }

    @Override
    public void shutdown() {
        clientHandler.shutdown();
    }

}
