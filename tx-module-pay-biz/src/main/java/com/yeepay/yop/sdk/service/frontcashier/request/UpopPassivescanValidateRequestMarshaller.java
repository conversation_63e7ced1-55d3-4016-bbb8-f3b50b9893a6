/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class UpopPassivescanValidateRequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<UpopPassivescanValidateRequest> {
    private final String serviceName = "Frontcashier";

    private final String resourcePath = "/rest/v1.0/frontcashier/upop/passivescan/validate";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.POST;


    @Override
    public Request<UpopPassivescanValidateRequest> marshall(UpopPassivescanValidateRequest request) {
        Request<UpopPassivescanValidateRequest> internalRequest = new DefaultRequest<UpopPassivescanValidateRequest>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getMerchantFlowId() != null) {
            internalRequest.addParameter("merchantFlowId", PrimitiveMarshallerUtils.marshalling(request.getMerchantFlowId(), "String"));
        }
        if (request.getPayOrderNo() != null) {
            internalRequest.addParameter("payOrderNo", PrimitiveMarshallerUtils.marshalling(request.getPayOrderNo(), "String"));
        }
        if (request.getCouponInfo() != null) {
            internalRequest.addParameter("couponInfo", PrimitiveMarshallerUtils.marshalling(request.getCouponInfo(), "String"));
        }
        if (request.getRealTradeAmount() != null) {
            internalRequest.addParameter("realTradeAmount", PrimitiveMarshallerUtils.marshalling(request.getRealTradeAmount(), "BigDecimal"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static UpopPassivescanValidateRequestMarshaller INSTANCE = new UpopPassivescanValidateRequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static UpopPassivescanValidateRequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
