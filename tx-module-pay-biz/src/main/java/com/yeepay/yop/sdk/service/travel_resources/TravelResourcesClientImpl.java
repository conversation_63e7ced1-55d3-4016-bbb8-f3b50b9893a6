/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.travel_resources;

import com.yeepay.yop.sdk.client.*;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.HttpResponseAnalyzerSupport;
import com.yeepay.yop.sdk.http.HttpResponseHandler;
import com.yeepay.yop.sdk.http.handler.DefaultHttpResponseHandler;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.service.travel_resources.request.*;
import com.yeepay.yop.sdk.service.travel_resources.response.*;

public class TravelResourcesClientImpl implements TravelResourcesClient {

    private final ClientHandler clientHandler;

    TravelResourcesClientImpl(ClientParams clientParams) {
        this.clientHandler = new ClientHandlerImpl(new ClientHandlerParams().withClientParams(clientParams));
    }

    @Override
    public CreateOrderResponse createOrder(CreateOrderRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<CreateOrderRequest> requestMarshaller = CreateOrderRequestMarshaller.getInstance();
        HttpResponseHandler<CreateOrderResponse> responseHandler =
                new DefaultHttpResponseHandler<CreateOrderResponse>(CreateOrderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<CreateOrderRequest, CreateOrderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public CreateRefundOrderResponse createRefundOrder(CreateRefundOrderRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<CreateRefundOrderRequest> requestMarshaller = CreateRefundOrderRequestMarshaller.getInstance();
        HttpResponseHandler<CreateRefundOrderResponse> responseHandler =
                new DefaultHttpResponseHandler<CreateRefundOrderResponse>(CreateRefundOrderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<CreateRefundOrderRequest, CreateRefundOrderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public KfcOrderResponse kfcOrder(KfcOrderRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<KfcOrderRequest> requestMarshaller = KfcOrderRequestMarshaller.getInstance();
        HttpResponseHandler<KfcOrderResponse> responseHandler =
                new DefaultHttpResponseHandler<KfcOrderResponse>(KfcOrderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<KfcOrderRequest, KfcOrderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public KfcOrder0Response kfcOrder_0(KfcOrder0Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<KfcOrder0Request> requestMarshaller = KfcOrder0RequestMarshaller.getInstance();
        HttpResponseHandler<KfcOrder0Response> responseHandler =
                new DefaultHttpResponseHandler<KfcOrder0Response>(KfcOrder0Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<KfcOrder0Request, KfcOrder0Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public QueryCinemaOrderResponse queryCinemaOrder(QueryCinemaOrderRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<QueryCinemaOrderRequest> requestMarshaller = QueryCinemaOrderRequestMarshaller.getInstance();
        HttpResponseHandler<QueryCinemaOrderResponse> responseHandler =
                new DefaultHttpResponseHandler<QueryCinemaOrderResponse>(QueryCinemaOrderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<QueryCinemaOrderRequest, QueryCinemaOrderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public QueryKfcOrderResponse queryKfcOrder(QueryKfcOrderRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<QueryKfcOrderRequest> requestMarshaller = QueryKfcOrderRequestMarshaller.getInstance();
        HttpResponseHandler<QueryKfcOrderResponse> responseHandler =
                new DefaultHttpResponseHandler<QueryKfcOrderResponse>(QueryKfcOrderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<QueryKfcOrderRequest, QueryKfcOrderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public QueryOrderResponse queryOrder(QueryOrderRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<QueryOrderRequest> requestMarshaller = QueryOrderRequestMarshaller.getInstance();
        HttpResponseHandler<QueryOrderResponse> responseHandler =
                new DefaultHttpResponseHandler<QueryOrderResponse>(QueryOrderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<QueryOrderRequest, QueryOrderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public QueryPayOrderResponse queryPayOrder(QueryPayOrderRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<QueryPayOrderRequest> requestMarshaller = QueryPayOrderRequestMarshaller.getInstance();
        HttpResponseHandler<QueryPayOrderResponse> responseHandler =
                new DefaultHttpResponseHandler<QueryPayOrderResponse>(QueryPayOrderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<QueryPayOrderRequest, QueryPayOrderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public QueryRefundOrderResponse queryRefundOrder(QueryRefundOrderRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<QueryRefundOrderRequest> requestMarshaller = QueryRefundOrderRequestMarshaller.getInstance();
        HttpResponseHandler<QueryRefundOrderResponse> responseHandler =
                new DefaultHttpResponseHandler<QueryRefundOrderResponse>(QueryRefundOrderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<QueryRefundOrderRequest, QueryRefundOrderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }

    @Override
    public void shutdown() {
        clientHandler.shutdown();
    }

}
