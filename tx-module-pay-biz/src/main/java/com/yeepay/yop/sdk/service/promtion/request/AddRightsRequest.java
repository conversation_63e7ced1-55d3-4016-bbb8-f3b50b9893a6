/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.promtion.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class AddRightsRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String merchantNo;

    private String rightsCode;

    private String startEffectDate;

    private String endEffectDate;

    private String brandNo;

    private String title;

    private String ruleDesc;

    private String periodType;

    private Integer period;

    private Integer frequency;

    private String businessType;

    private String userNo;

    private String productNo;

    private String productName;


    /**
     * Get merchantNo
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    /**
     * Get rightsCode
     * @return rightsCode
     **/
    
    public String getRightsCode() {
        return rightsCode;
    }

    public void setRightsCode(String rightsCode) {
        this.rightsCode = rightsCode;
    }

    /**
     * Get startEffectDate
     * @return startEffectDate
     **/
    
    public String getStartEffectDate() {
        return startEffectDate;
    }

    public void setStartEffectDate(String startEffectDate) {
        this.startEffectDate = startEffectDate;
    }

    /**
     * Get endEffectDate
     * @return endEffectDate
     **/
    
    public String getEndEffectDate() {
        return endEffectDate;
    }

    public void setEndEffectDate(String endEffectDate) {
        this.endEffectDate = endEffectDate;
    }

    /**
     * Get brandNo
     * @return brandNo
     **/
    
    public String getBrandNo() {
        return brandNo;
    }

    public void setBrandNo(String brandNo) {
        this.brandNo = brandNo;
    }

    /**
     * Get title
     * @return title
     **/
    
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * Get ruleDesc
     * @return ruleDesc
     **/
    
    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    /**
     * Get periodType
     * @return periodType
     **/
    
    public String getPeriodType() {
        return periodType;
    }

    public void setPeriodType(String periodType) {
        this.periodType = periodType;
    }

    /**
     * Get period
     * @return period
     **/
    
    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    /**
     * Get frequency
     * @return frequency
     **/
    
    public Integer getFrequency() {
        return frequency;
    }

    public void setFrequency(Integer frequency) {
        this.frequency = frequency;
    }

    /**
     * Get businessType
     * @return businessType
     **/
    
    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * Get userNo
     * @return userNo
     **/
    
    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    /**
     * Get productNo
     * @return productNo
     **/
    
    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    /**
     * Get productName
     * @return productName
     **/
    
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Override
    public String getOperationId() {
        return "add_rights";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
