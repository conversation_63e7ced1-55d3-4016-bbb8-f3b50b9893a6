/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.agency_operation.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 服务商抽佣结果
 */
public class YopMultiChannelWithholdResDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;抽佣状态&lt;br /&gt;PROCESSING ：处理中&lt;/p&gt; &lt;p&gt;FAIL : 失败&lt;/p&gt; &lt;p&gt;SUCCESS ：成功&lt;/p&gt;
   */
  @JsonProperty("status")
  private String status = null;

  public YopMultiChannelWithholdResDTO status(String status) {
    this.status = status;
    return this;
  }

   /**
   * &lt;p&gt;抽佣状态&lt;br /&gt;PROCESSING ：处理中&lt;/p&gt; &lt;p&gt;FAIL : 失败&lt;/p&gt; &lt;p&gt;SUCCESS ：成功&lt;/p&gt;
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    YopMultiChannelWithholdResDTO yopMultiChannelWithholdResDTO = (YopMultiChannelWithholdResDTO) o;
    return ObjectUtils.equals(this.status, yopMultiChannelWithholdResDTO.status);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(status);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class YopMultiChannelWithholdResDTO {\n");
    
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

