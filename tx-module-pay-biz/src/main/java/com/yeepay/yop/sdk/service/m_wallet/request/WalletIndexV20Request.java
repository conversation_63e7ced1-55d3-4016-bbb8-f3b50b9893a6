/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class WalletIndexV20Request extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String parentMerchantNo;

    private String merchantNo;

    private String merchantUserNo;

    private String userMerchantNo;

    private String name;

    private String certificateType;

    private String certificateNo;

    private String mobile;

    private String returnUrl;

    private String notifyUrl;

    private String requestNo;

    private Boolean needNavBar;


    /**
     * Get parentMerchantNo
     * @return parentMerchantNo
     **/
    
    public String getParentMerchantNo() {
        return parentMerchantNo;
    }

    public void setParentMerchantNo(String parentMerchantNo) {
        this.parentMerchantNo = parentMerchantNo;
    }

    /**
     * Get merchantNo
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    /**
     * Get merchantUserNo
     * @return merchantUserNo
     **/
    
    public String getMerchantUserNo() {
        return merchantUserNo;
    }

    public void setMerchantUserNo(String merchantUserNo) {
        this.merchantUserNo = merchantUserNo;
    }

    /**
     * Get userMerchantNo
     * @return userMerchantNo
     **/
    
    public String getUserMerchantNo() {
        return userMerchantNo;
    }

    public void setUserMerchantNo(String userMerchantNo) {
        this.userMerchantNo = userMerchantNo;
    }

    /**
     * Get name
     * @return name
     **/
    
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * Get certificateType
     * @return certificateType
     **/
    
    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    /**
     * Get certificateNo
     * @return certificateNo
     **/
    
    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    /**
     * Get mobile
     * @return mobile
     **/
    
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * Get returnUrl
     * @return returnUrl
     **/
    
    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    /**
     * Get notifyUrl
     * @return notifyUrl
     **/
    
    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    /**
     * Get requestNo
     * @return requestNo
     **/
    
    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    /**
     * Get needNavBar
     * @return needNavBar
     **/
    
    public Boolean getNeedNavBar() {
        return needNavBar;
    }

    public void setNeedNavBar(Boolean needNavBar) {
        this.needNavBar = needNavBar;
    }

    @Override
    public String getOperationId() {
        return "wallet_index_v2_0";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
