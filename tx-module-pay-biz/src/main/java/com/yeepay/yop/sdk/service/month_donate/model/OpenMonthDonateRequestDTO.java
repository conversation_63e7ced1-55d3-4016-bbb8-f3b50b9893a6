/*
 * 公益-月捐
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.month_donate.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 开通月捐请求参数
 */
public class OpenMonthDonateRequestDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;月捐金额&lt;/p&gt;
   */
  @JsonProperty("amount")
  private BigDecimal amount = null;

  /**
   * &lt;p&gt;签约渠道&lt;/p&gt; &lt;div&gt; &lt;pre&gt;WECHAT_ACCOUNT 微信公众号&lt;/pre&gt; &lt;div&gt; &lt;pre&gt;WECHAT_MINI_PROGRAM 微信小程序&lt;/pre&gt; &lt;/div&gt; &lt;/div&gt;
   */
  @JsonProperty("channel")
  private String channel = null;

  /**
   * &lt;p&gt;回调地址&lt;/p&gt;
   */
  @JsonProperty("successUrl")
  private String successUrl = null;

  /**
   * &lt;p&gt;跳转商户页面地址&lt;/p&gt;
   */
  @JsonProperty("callbackUrl")
  private String callbackUrl = null;

  /**
   * &lt;p&gt;项目ID&lt;/p&gt;
   */
  @JsonProperty("projectId")
  private Long projectId = null;

  /**
   * &lt;p&gt;用户ID&lt;/p&gt;
   */
  @JsonProperty("userId")
  private Long userId = null;

  /**
   * &lt;p&gt;商户编号&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;div data-lark-html-role&#x3D;\&quot;root\&quot;&gt;&lt;span class&#x3D;\&quot;text-only\&quot; data-eleid&#x3D;\&quot;10\&quot;&gt;业务请求流水号&lt;/span&gt;&lt;/div&gt;
   */
  @JsonProperty("businessId")
  private String businessId = null;

  public OpenMonthDonateRequestDTO amount(BigDecimal amount) {
    this.amount = amount;
    return this;
  }

   /**
   * &lt;p&gt;月捐金额&lt;/p&gt;
   * minimum: 0
   * @return amount
  **/

  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public OpenMonthDonateRequestDTO channel(String channel) {
    this.channel = channel;
    return this;
  }

   /**
   * &lt;p&gt;签约渠道&lt;/p&gt; &lt;div&gt; &lt;pre&gt;WECHAT_ACCOUNT 微信公众号&lt;/pre&gt; &lt;div&gt; &lt;pre&gt;WECHAT_MINI_PROGRAM 微信小程序&lt;/pre&gt; &lt;/div&gt; &lt;/div&gt;
   * @return channel
  **/

  public String getChannel() {
    return channel;
  }

  public void setChannel(String channel) {
    this.channel = channel;
  }

  public OpenMonthDonateRequestDTO successUrl(String successUrl) {
    this.successUrl = successUrl;
    return this;
  }

   /**
   * &lt;p&gt;回调地址&lt;/p&gt;
   * @return successUrl
  **/

  public String getSuccessUrl() {
    return successUrl;
  }

  public void setSuccessUrl(String successUrl) {
    this.successUrl = successUrl;
  }

  public OpenMonthDonateRequestDTO callbackUrl(String callbackUrl) {
    this.callbackUrl = callbackUrl;
    return this;
  }

   /**
   * &lt;p&gt;跳转商户页面地址&lt;/p&gt;
   * @return callbackUrl
  **/

  public String getCallbackUrl() {
    return callbackUrl;
  }

  public void setCallbackUrl(String callbackUrl) {
    this.callbackUrl = callbackUrl;
  }

  public OpenMonthDonateRequestDTO projectId(Long projectId) {
    this.projectId = projectId;
    return this;
  }

   /**
   * &lt;p&gt;项目ID&lt;/p&gt;
   * @return projectId
  **/

  public Long getProjectId() {
    return projectId;
  }

  public void setProjectId(Long projectId) {
    this.projectId = projectId;
  }

  public OpenMonthDonateRequestDTO userId(Long userId) {
    this.userId = userId;
    return this;
  }

   /**
   * &lt;p&gt;用户ID&lt;/p&gt;
   * @return userId
  **/

  public Long getUserId() {
    return userId;
  }

  public void setUserId(Long userId) {
    this.userId = userId;
  }

  public OpenMonthDonateRequestDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;商户编号&lt;/p&gt;
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public OpenMonthDonateRequestDTO businessId(String businessId) {
    this.businessId = businessId;
    return this;
  }

   /**
   * &lt;div data-lark-html-role&#x3D;\&quot;root\&quot;&gt;&lt;span class&#x3D;\&quot;text-only\&quot; data-eleid&#x3D;\&quot;10\&quot;&gt;业务请求流水号&lt;/span&gt;&lt;/div&gt;
   * @return businessId
  **/

  public String getBusinessId() {
    return businessId;
  }

  public void setBusinessId(String businessId) {
    this.businessId = businessId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    OpenMonthDonateRequestDTO openMonthDonateRequestDTO = (OpenMonthDonateRequestDTO) o;
    return ObjectUtils.equals(this.amount, openMonthDonateRequestDTO.amount) &&
    ObjectUtils.equals(this.channel, openMonthDonateRequestDTO.channel) &&
    ObjectUtils.equals(this.successUrl, openMonthDonateRequestDTO.successUrl) &&
    ObjectUtils.equals(this.callbackUrl, openMonthDonateRequestDTO.callbackUrl) &&
    ObjectUtils.equals(this.projectId, openMonthDonateRequestDTO.projectId) &&
    ObjectUtils.equals(this.userId, openMonthDonateRequestDTO.userId) &&
    ObjectUtils.equals(this.merchantNo, openMonthDonateRequestDTO.merchantNo) &&
    ObjectUtils.equals(this.businessId, openMonthDonateRequestDTO.businessId);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(amount, channel, successUrl, callbackUrl, projectId, userId, merchantNo, businessId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OpenMonthDonateRequestDTO {\n");
    
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("    channel: ").append(toIndentedString(channel)).append("\n");
    sb.append("    successUrl: ").append(toIndentedString(successUrl)).append("\n");
    sb.append("    callbackUrl: ").append(toIndentedString(callbackUrl)).append("\n");
    sb.append("    projectId: ").append(toIndentedString(projectId)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    businessId: ").append(toIndentedString(businessId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

