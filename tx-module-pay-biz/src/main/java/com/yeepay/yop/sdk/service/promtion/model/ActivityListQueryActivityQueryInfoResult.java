/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 */
public class ActivityListQueryActivityQueryInfoResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 商户活动编号
   */
  @JsonProperty("merchantActivityNo")
  private String merchantActivityNo = null;

  /**
   * 易宝活动编号
   */
  @JsonProperty("marketingNo")
  private String marketingNo = null;

  /**
   * 活动名称
   */
  @JsonProperty("activityName")
  private String activityName = null;

  /**
   * 活动状态
   */
  @JsonProperty("activityStatus")
  private String activityStatus = null;

  /**
   * 活动开始时间
   */
  @JsonProperty("activityStartTime")
  private String activityStartTime = null;

  /**
   * 活动结束时间
   */
  @JsonProperty("activityEndTime")
  private String activityEndTime = null;

  /**
   * 活动说明
   */
  @JsonProperty("activityRemark")
  private String activityRemark = null;

  /**
   * 活动工具类型
   */
  @JsonProperty("activityToolType")
  private String activityToolType = null;

  /**
   * 优惠券名称
   */
  @JsonProperty("couponName")
  private String couponName = null;

  /**
   * 优惠券类型
   */
  @JsonProperty("couponType")
  private String couponType = null;

  /**
   * 最小使用金额
   */
  @JsonProperty("minUseAmount")
  private String minUseAmount = null;

  /**
   * 优惠金额
   */
  @JsonProperty("preferentialAmount")
  private String preferentialAmount = null;

  /**
   * 折扣比例
   */
  @JsonProperty("discountRatio")
  private String discountRatio = null;

  /**
   * 最大优惠金额
   */
  @JsonProperty("maxPreferentialAmount")
  private String maxPreferentialAmount = null;

  /**
   * 适用产品类型
   */
  @JsonProperty("applyProductType")
  private String applyProductType = null;

  /**
   * 产品id
   */
  @JsonProperty("applyProductId")
  private String applyProductId = null;

  /**
   * 可用时间类型
   */
  @JsonProperty("validityType")
  private String validityType = null;

  /**
   * 动态生效类型
   */
  @JsonProperty("dynamicValidityType")
  private String dynamicValidityType = null;

  /**
   * 有效期天数
   */
  @JsonProperty("validityDays")
  private Integer validityDays = null;

  /**
   * 生效日期
   */
  @JsonProperty("effectiveTime")
  private String effectiveTime = null;

  /**
   * 失效日期
   */
  @JsonProperty("expirationTime")
  private String expirationTime = null;

  /**
   * 优惠券说明
   */
  @JsonProperty("couponRemark")
  private String couponRemark = null;

  /**
   * 申请数量
   */
  @JsonProperty("applyGrantNumber")
  private Integer applyGrantNumber = null;

  /**
   * 实际发放数量
   */
  @JsonProperty("actualGrantNumber")
  private Integer actualGrantNumber = null;

  /**
   * 同一用户最大参与次数
   */
  @JsonProperty("sameUserMaxJoinNumber")
  private Integer sameUserMaxJoinNumber = null;

  public ActivityListQueryActivityQueryInfoResult merchantActivityNo(String merchantActivityNo) {
    this.merchantActivityNo = merchantActivityNo;
    return this;
  }

   /**
   * 商户活动编号
   * @return merchantActivityNo
  **/

  public String getMerchantActivityNo() {
    return merchantActivityNo;
  }

  public void setMerchantActivityNo(String merchantActivityNo) {
    this.merchantActivityNo = merchantActivityNo;
  }

  public ActivityListQueryActivityQueryInfoResult marketingNo(String marketingNo) {
    this.marketingNo = marketingNo;
    return this;
  }

   /**
   * 易宝活动编号
   * @return marketingNo
  **/

  public String getMarketingNo() {
    return marketingNo;
  }

  public void setMarketingNo(String marketingNo) {
    this.marketingNo = marketingNo;
  }

  public ActivityListQueryActivityQueryInfoResult activityName(String activityName) {
    this.activityName = activityName;
    return this;
  }

   /**
   * 活动名称
   * @return activityName
  **/

  public String getActivityName() {
    return activityName;
  }

  public void setActivityName(String activityName) {
    this.activityName = activityName;
  }

  public ActivityListQueryActivityQueryInfoResult activityStatus(String activityStatus) {
    this.activityStatus = activityStatus;
    return this;
  }

   /**
   * 活动状态
   * @return activityStatus
  **/

  public String getActivityStatus() {
    return activityStatus;
  }

  public void setActivityStatus(String activityStatus) {
    this.activityStatus = activityStatus;
  }

  public ActivityListQueryActivityQueryInfoResult activityStartTime(String activityStartTime) {
    this.activityStartTime = activityStartTime;
    return this;
  }

   /**
   * 活动开始时间
   * @return activityStartTime
  **/

  public String getActivityStartTime() {
    return activityStartTime;
  }

  public void setActivityStartTime(String activityStartTime) {
    this.activityStartTime = activityStartTime;
  }

  public ActivityListQueryActivityQueryInfoResult activityEndTime(String activityEndTime) {
    this.activityEndTime = activityEndTime;
    return this;
  }

   /**
   * 活动结束时间
   * @return activityEndTime
  **/

  public String getActivityEndTime() {
    return activityEndTime;
  }

  public void setActivityEndTime(String activityEndTime) {
    this.activityEndTime = activityEndTime;
  }

  public ActivityListQueryActivityQueryInfoResult activityRemark(String activityRemark) {
    this.activityRemark = activityRemark;
    return this;
  }

   /**
   * 活动说明
   * @return activityRemark
  **/

  public String getActivityRemark() {
    return activityRemark;
  }

  public void setActivityRemark(String activityRemark) {
    this.activityRemark = activityRemark;
  }

  public ActivityListQueryActivityQueryInfoResult activityToolType(String activityToolType) {
    this.activityToolType = activityToolType;
    return this;
  }

   /**
   * 活动工具类型
   * @return activityToolType
  **/

  public String getActivityToolType() {
    return activityToolType;
  }

  public void setActivityToolType(String activityToolType) {
    this.activityToolType = activityToolType;
  }

  public ActivityListQueryActivityQueryInfoResult couponName(String couponName) {
    this.couponName = couponName;
    return this;
  }

   /**
   * 优惠券名称
   * @return couponName
  **/

  public String getCouponName() {
    return couponName;
  }

  public void setCouponName(String couponName) {
    this.couponName = couponName;
  }

  public ActivityListQueryActivityQueryInfoResult couponType(String couponType) {
    this.couponType = couponType;
    return this;
  }

   /**
   * 优惠券类型
   * @return couponType
  **/

  public String getCouponType() {
    return couponType;
  }

  public void setCouponType(String couponType) {
    this.couponType = couponType;
  }

  public ActivityListQueryActivityQueryInfoResult minUseAmount(String minUseAmount) {
    this.minUseAmount = minUseAmount;
    return this;
  }

   /**
   * 最小使用金额
   * @return minUseAmount
  **/

  public String getMinUseAmount() {
    return minUseAmount;
  }

  public void setMinUseAmount(String minUseAmount) {
    this.minUseAmount = minUseAmount;
  }

  public ActivityListQueryActivityQueryInfoResult preferentialAmount(String preferentialAmount) {
    this.preferentialAmount = preferentialAmount;
    return this;
  }

   /**
   * 优惠金额
   * @return preferentialAmount
  **/

  public String getPreferentialAmount() {
    return preferentialAmount;
  }

  public void setPreferentialAmount(String preferentialAmount) {
    this.preferentialAmount = preferentialAmount;
  }

  public ActivityListQueryActivityQueryInfoResult discountRatio(String discountRatio) {
    this.discountRatio = discountRatio;
    return this;
  }

   /**
   * 折扣比例
   * @return discountRatio
  **/

  public String getDiscountRatio() {
    return discountRatio;
  }

  public void setDiscountRatio(String discountRatio) {
    this.discountRatio = discountRatio;
  }

  public ActivityListQueryActivityQueryInfoResult maxPreferentialAmount(String maxPreferentialAmount) {
    this.maxPreferentialAmount = maxPreferentialAmount;
    return this;
  }

   /**
   * 最大优惠金额
   * @return maxPreferentialAmount
  **/

  public String getMaxPreferentialAmount() {
    return maxPreferentialAmount;
  }

  public void setMaxPreferentialAmount(String maxPreferentialAmount) {
    this.maxPreferentialAmount = maxPreferentialAmount;
  }

  public ActivityListQueryActivityQueryInfoResult applyProductType(String applyProductType) {
    this.applyProductType = applyProductType;
    return this;
  }

   /**
   * 适用产品类型
   * @return applyProductType
  **/

  public String getApplyProductType() {
    return applyProductType;
  }

  public void setApplyProductType(String applyProductType) {
    this.applyProductType = applyProductType;
  }

  public ActivityListQueryActivityQueryInfoResult applyProductId(String applyProductId) {
    this.applyProductId = applyProductId;
    return this;
  }

   /**
   * 产品id
   * @return applyProductId
  **/

  public String getApplyProductId() {
    return applyProductId;
  }

  public void setApplyProductId(String applyProductId) {
    this.applyProductId = applyProductId;
  }

  public ActivityListQueryActivityQueryInfoResult validityType(String validityType) {
    this.validityType = validityType;
    return this;
  }

   /**
   * 可用时间类型
   * @return validityType
  **/

  public String getValidityType() {
    return validityType;
  }

  public void setValidityType(String validityType) {
    this.validityType = validityType;
  }

  public ActivityListQueryActivityQueryInfoResult dynamicValidityType(String dynamicValidityType) {
    this.dynamicValidityType = dynamicValidityType;
    return this;
  }

   /**
   * 动态生效类型
   * @return dynamicValidityType
  **/

  public String getDynamicValidityType() {
    return dynamicValidityType;
  }

  public void setDynamicValidityType(String dynamicValidityType) {
    this.dynamicValidityType = dynamicValidityType;
  }

  public ActivityListQueryActivityQueryInfoResult validityDays(Integer validityDays) {
    this.validityDays = validityDays;
    return this;
  }

   /**
   * 有效期天数
   * @return validityDays
  **/

  public Integer getValidityDays() {
    return validityDays;
  }

  public void setValidityDays(Integer validityDays) {
    this.validityDays = validityDays;
  }

  public ActivityListQueryActivityQueryInfoResult effectiveTime(String effectiveTime) {
    this.effectiveTime = effectiveTime;
    return this;
  }

   /**
   * 生效日期
   * @return effectiveTime
  **/

  public String getEffectiveTime() {
    return effectiveTime;
  }

  public void setEffectiveTime(String effectiveTime) {
    this.effectiveTime = effectiveTime;
  }

  public ActivityListQueryActivityQueryInfoResult expirationTime(String expirationTime) {
    this.expirationTime = expirationTime;
    return this;
  }

   /**
   * 失效日期
   * @return expirationTime
  **/

  public String getExpirationTime() {
    return expirationTime;
  }

  public void setExpirationTime(String expirationTime) {
    this.expirationTime = expirationTime;
  }

  public ActivityListQueryActivityQueryInfoResult couponRemark(String couponRemark) {
    this.couponRemark = couponRemark;
    return this;
  }

   /**
   * 优惠券说明
   * @return couponRemark
  **/

  public String getCouponRemark() {
    return couponRemark;
  }

  public void setCouponRemark(String couponRemark) {
    this.couponRemark = couponRemark;
  }

  public ActivityListQueryActivityQueryInfoResult applyGrantNumber(Integer applyGrantNumber) {
    this.applyGrantNumber = applyGrantNumber;
    return this;
  }

   /**
   * 申请数量
   * @return applyGrantNumber
  **/

  public Integer getApplyGrantNumber() {
    return applyGrantNumber;
  }

  public void setApplyGrantNumber(Integer applyGrantNumber) {
    this.applyGrantNumber = applyGrantNumber;
  }

  public ActivityListQueryActivityQueryInfoResult actualGrantNumber(Integer actualGrantNumber) {
    this.actualGrantNumber = actualGrantNumber;
    return this;
  }

   /**
   * 实际发放数量
   * @return actualGrantNumber
  **/

  public Integer getActualGrantNumber() {
    return actualGrantNumber;
  }

  public void setActualGrantNumber(Integer actualGrantNumber) {
    this.actualGrantNumber = actualGrantNumber;
  }

  public ActivityListQueryActivityQueryInfoResult sameUserMaxJoinNumber(Integer sameUserMaxJoinNumber) {
    this.sameUserMaxJoinNumber = sameUserMaxJoinNumber;
    return this;
  }

   /**
   * 同一用户最大参与次数
   * @return sameUserMaxJoinNumber
  **/

  public Integer getSameUserMaxJoinNumber() {
    return sameUserMaxJoinNumber;
  }

  public void setSameUserMaxJoinNumber(Integer sameUserMaxJoinNumber) {
    this.sameUserMaxJoinNumber = sameUserMaxJoinNumber;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    ActivityListQueryActivityQueryInfoResult activityListQueryActivityQueryInfoResult = (ActivityListQueryActivityQueryInfoResult) o;
    return ObjectUtils.equals(this.merchantActivityNo, activityListQueryActivityQueryInfoResult.merchantActivityNo) &&
    ObjectUtils.equals(this.marketingNo, activityListQueryActivityQueryInfoResult.marketingNo) &&
    ObjectUtils.equals(this.activityName, activityListQueryActivityQueryInfoResult.activityName) &&
    ObjectUtils.equals(this.activityStatus, activityListQueryActivityQueryInfoResult.activityStatus) &&
    ObjectUtils.equals(this.activityStartTime, activityListQueryActivityQueryInfoResult.activityStartTime) &&
    ObjectUtils.equals(this.activityEndTime, activityListQueryActivityQueryInfoResult.activityEndTime) &&
    ObjectUtils.equals(this.activityRemark, activityListQueryActivityQueryInfoResult.activityRemark) &&
    ObjectUtils.equals(this.activityToolType, activityListQueryActivityQueryInfoResult.activityToolType) &&
    ObjectUtils.equals(this.couponName, activityListQueryActivityQueryInfoResult.couponName) &&
    ObjectUtils.equals(this.couponType, activityListQueryActivityQueryInfoResult.couponType) &&
    ObjectUtils.equals(this.minUseAmount, activityListQueryActivityQueryInfoResult.minUseAmount) &&
    ObjectUtils.equals(this.preferentialAmount, activityListQueryActivityQueryInfoResult.preferentialAmount) &&
    ObjectUtils.equals(this.discountRatio, activityListQueryActivityQueryInfoResult.discountRatio) &&
    ObjectUtils.equals(this.maxPreferentialAmount, activityListQueryActivityQueryInfoResult.maxPreferentialAmount) &&
    ObjectUtils.equals(this.applyProductType, activityListQueryActivityQueryInfoResult.applyProductType) &&
    ObjectUtils.equals(this.applyProductId, activityListQueryActivityQueryInfoResult.applyProductId) &&
    ObjectUtils.equals(this.validityType, activityListQueryActivityQueryInfoResult.validityType) &&
    ObjectUtils.equals(this.dynamicValidityType, activityListQueryActivityQueryInfoResult.dynamicValidityType) &&
    ObjectUtils.equals(this.validityDays, activityListQueryActivityQueryInfoResult.validityDays) &&
    ObjectUtils.equals(this.effectiveTime, activityListQueryActivityQueryInfoResult.effectiveTime) &&
    ObjectUtils.equals(this.expirationTime, activityListQueryActivityQueryInfoResult.expirationTime) &&
    ObjectUtils.equals(this.couponRemark, activityListQueryActivityQueryInfoResult.couponRemark) &&
    ObjectUtils.equals(this.applyGrantNumber, activityListQueryActivityQueryInfoResult.applyGrantNumber) &&
    ObjectUtils.equals(this.actualGrantNumber, activityListQueryActivityQueryInfoResult.actualGrantNumber) &&
    ObjectUtils.equals(this.sameUserMaxJoinNumber, activityListQueryActivityQueryInfoResult.sameUserMaxJoinNumber);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(merchantActivityNo, marketingNo, activityName, activityStatus, activityStartTime, activityEndTime, activityRemark, activityToolType, couponName, couponType, minUseAmount, preferentialAmount, discountRatio, maxPreferentialAmount, applyProductType, applyProductId, validityType, dynamicValidityType, validityDays, effectiveTime, expirationTime, couponRemark, applyGrantNumber, actualGrantNumber, sameUserMaxJoinNumber);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ActivityListQueryActivityQueryInfoResult {\n");
    
    sb.append("    merchantActivityNo: ").append(toIndentedString(merchantActivityNo)).append("\n");
    sb.append("    marketingNo: ").append(toIndentedString(marketingNo)).append("\n");
    sb.append("    activityName: ").append(toIndentedString(activityName)).append("\n");
    sb.append("    activityStatus: ").append(toIndentedString(activityStatus)).append("\n");
    sb.append("    activityStartTime: ").append(toIndentedString(activityStartTime)).append("\n");
    sb.append("    activityEndTime: ").append(toIndentedString(activityEndTime)).append("\n");
    sb.append("    activityRemark: ").append(toIndentedString(activityRemark)).append("\n");
    sb.append("    activityToolType: ").append(toIndentedString(activityToolType)).append("\n");
    sb.append("    couponName: ").append(toIndentedString(couponName)).append("\n");
    sb.append("    couponType: ").append(toIndentedString(couponType)).append("\n");
    sb.append("    minUseAmount: ").append(toIndentedString(minUseAmount)).append("\n");
    sb.append("    preferentialAmount: ").append(toIndentedString(preferentialAmount)).append("\n");
    sb.append("    discountRatio: ").append(toIndentedString(discountRatio)).append("\n");
    sb.append("    maxPreferentialAmount: ").append(toIndentedString(maxPreferentialAmount)).append("\n");
    sb.append("    applyProductType: ").append(toIndentedString(applyProductType)).append("\n");
    sb.append("    applyProductId: ").append(toIndentedString(applyProductId)).append("\n");
    sb.append("    validityType: ").append(toIndentedString(validityType)).append("\n");
    sb.append("    dynamicValidityType: ").append(toIndentedString(dynamicValidityType)).append("\n");
    sb.append("    validityDays: ").append(toIndentedString(validityDays)).append("\n");
    sb.append("    effectiveTime: ").append(toIndentedString(effectiveTime)).append("\n");
    sb.append("    expirationTime: ").append(toIndentedString(expirationTime)).append("\n");
    sb.append("    couponRemark: ").append(toIndentedString(couponRemark)).append("\n");
    sb.append("    applyGrantNumber: ").append(toIndentedString(applyGrantNumber)).append("\n");
    sb.append("    actualGrantNumber: ").append(toIndentedString(actualGrantNumber)).append("\n");
    sb.append("    sameUserMaxJoinNumber: ").append(toIndentedString(sameUserMaxJoinNumber)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

