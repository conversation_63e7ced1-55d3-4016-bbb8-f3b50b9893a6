/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.promtion;

import com.yeepay.yop.sdk.client.*;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.HttpResponseAnalyzerSupport;
import com.yeepay.yop.sdk.http.HttpResponseHandler;
import com.yeepay.yop.sdk.http.handler.DefaultHttpResponseHandler;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.service.promtion.request.*;
import com.yeepay.yop.sdk.service.promtion.response.*;

public class PromtionClientImpl implements PromtionClient {

    private final ClientHandler clientHandler;

    PromtionClientImpl(ClientParams clientParams) {
        this.clientHandler = new ClientHandlerImpl(new ClientHandlerParams().withClientParams(clientParams));
    }

    @Override
    public ActivityListQueryResponse activityListQuery(ActivityListQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<ActivityListQueryRequest> requestMarshaller = ActivityListQueryRequestMarshaller.getInstance();
        HttpResponseHandler<ActivityListQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<ActivityListQueryResponse>(ActivityListQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<ActivityListQueryRequest, ActivityListQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AddRightsResponse addRights(AddRightsRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AddRightsRequest> requestMarshaller = AddRightsRequestMarshaller.getInstance();
        HttpResponseHandler<AddRightsResponse> responseHandler =
                new DefaultHttpResponseHandler<AddRightsResponse>(AddRightsResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AddRightsRequest, AddRightsResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AddRightsResponse add_rights(AddRightsRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AddRightsRequest> requestMarshaller = AddRightsRequestMarshaller.getInstance();
        HttpResponseHandler<AddRightsResponse> responseHandler =
                new DefaultHttpResponseHandler<AddRightsResponse>(AddRightsResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AddRightsRequest, AddRightsResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public CouponApplyResponse couponApply(CouponApplyRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<CouponApplyRequest> requestMarshaller = CouponApplyRequestMarshaller.getInstance();
        HttpResponseHandler<CouponApplyResponse> responseHandler =
                new DefaultHttpResponseHandler<CouponApplyResponse>(CouponApplyResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<CouponApplyRequest, CouponApplyResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public CouponListQueryResponse couponListQuery(CouponListQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<CouponListQueryRequest> requestMarshaller = CouponListQueryRequestMarshaller.getInstance();
        HttpResponseHandler<CouponListQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<CouponListQueryResponse>(CouponListQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<CouponListQueryRequest, CouponListQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public CreateRightsQrcodeAdapterResponse create_rights_qrcode_adapter(CreateRightsQrcodeAdapterRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<CreateRightsQrcodeAdapterRequest> requestMarshaller = CreateRightsQrcodeAdapterRequestMarshaller.getInstance();
        HttpResponseHandler<CreateRightsQrcodeAdapterResponse> responseHandler =
                new DefaultHttpResponseHandler<CreateRightsQrcodeAdapterResponse>(CreateRightsQrcodeAdapterResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<CreateRightsQrcodeAdapterRequest, CreateRightsQrcodeAdapterResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public FrozenRightsResponse frozen_rights(FrozenRightsRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<FrozenRightsRequest> requestMarshaller = FrozenRightsRequestMarshaller.getInstance();
        HttpResponseHandler<FrozenRightsResponse> responseHandler =
                new DefaultHttpResponseHandler<FrozenRightsResponse>(FrozenRightsResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<FrozenRightsRequest, FrozenRightsResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public PointCreateResponse pointCreate(PointCreateRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<PointCreateRequest> requestMarshaller = PointCreateRequestMarshaller.getInstance();
        HttpResponseHandler<PointCreateResponse> responseHandler =
                new DefaultHttpResponseHandler<PointCreateResponse>(PointCreateResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<PointCreateRequest, PointCreateResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public PointOperateResponse pointOperate(PointOperateRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<PointOperateRequest> requestMarshaller = PointOperateRequestMarshaller.getInstance();
        HttpResponseHandler<PointOperateResponse> responseHandler =
                new DefaultHttpResponseHandler<PointOperateResponse>(PointOperateResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<PointOperateRequest, PointOperateResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public PointQueryResponse pointQuery(PointQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<PointQueryRequest> requestMarshaller = PointQueryRequestMarshaller.getInstance();
        HttpResponseHandler<PointQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<PointQueryResponse>(PointQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<PointQueryRequest, PointQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public QueryConsumeRecordAdapterResponse query_consume_record_adapter(QueryConsumeRecordAdapterRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<QueryConsumeRecordAdapterRequest> requestMarshaller = QueryConsumeRecordAdapterRequestMarshaller.getInstance();
        HttpResponseHandler<QueryConsumeRecordAdapterResponse> responseHandler =
                new DefaultHttpResponseHandler<QueryConsumeRecordAdapterResponse>(QueryConsumeRecordAdapterResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<QueryConsumeRecordAdapterRequest, QueryConsumeRecordAdapterResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public QueryRightsResponse query_rights(QueryRightsRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<QueryRightsRequest> requestMarshaller = QueryRightsRequestMarshaller.getInstance();
        HttpResponseHandler<QueryRightsResponse> responseHandler =
                new DefaultHttpResponseHandler<QueryRightsResponse>(QueryRightsResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<QueryRightsRequest, QueryRightsResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public RightsCreateQrcodeResponse rightsCreateQrcode(RightsCreateQrcodeRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<RightsCreateQrcodeRequest> requestMarshaller = RightsCreateQrcodeRequestMarshaller.getInstance();
        HttpResponseHandler<RightsCreateQrcodeResponse> responseHandler =
                new DefaultHttpResponseHandler<RightsCreateQrcodeResponse>(RightsCreateQrcodeResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<RightsCreateQrcodeRequest, RightsCreateQrcodeResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public RightsFrozenRightsResponse rightsFrozenRights(RightsFrozenRightsRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<RightsFrozenRightsRequest> requestMarshaller = RightsFrozenRightsRequestMarshaller.getInstance();
        HttpResponseHandler<RightsFrozenRightsResponse> responseHandler =
                new DefaultHttpResponseHandler<RightsFrozenRightsResponse>(RightsFrozenRightsResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<RightsFrozenRightsRequest, RightsFrozenRightsResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public RightsQueryConsumeRecordsResponse rightsQueryConsumeRecords(RightsQueryConsumeRecordsRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<RightsQueryConsumeRecordsRequest> requestMarshaller = RightsQueryConsumeRecordsRequestMarshaller.getInstance();
        HttpResponseHandler<RightsQueryConsumeRecordsResponse> responseHandler =
                new DefaultHttpResponseHandler<RightsQueryConsumeRecordsResponse>(RightsQueryConsumeRecordsResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<RightsQueryConsumeRecordsRequest, RightsQueryConsumeRecordsResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public RightsQueryRightsResponse rightsQueryRights(RightsQueryRightsRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<RightsQueryRightsRequest> requestMarshaller = RightsQueryRightsRequestMarshaller.getInstance();
        HttpResponseHandler<RightsQueryRightsResponse> responseHandler =
                new DefaultHttpResponseHandler<RightsQueryRightsResponse>(RightsQueryRightsResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<RightsQueryRightsRequest, RightsQueryRightsResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public RightsTransferResponse rightsTransfer(RightsTransferRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<RightsTransferRequest> requestMarshaller = RightsTransferRequestMarshaller.getInstance();
        HttpResponseHandler<RightsTransferResponse> responseHandler =
                new DefaultHttpResponseHandler<RightsTransferResponse>(RightsTransferResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<RightsTransferRequest, RightsTransferResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public RightsTransferResponse rights_transfer(RightsTransferRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<RightsTransferRequest> requestMarshaller = RightsTransferRequestMarshaller.getInstance();
        HttpResponseHandler<RightsTransferResponse> responseHandler =
                new DefaultHttpResponseHandler<RightsTransferResponse>(RightsTransferResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<RightsTransferRequest, RightsTransferResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public SubsidyApplyResponse subsidyApply(SubsidyApplyRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<SubsidyApplyRequest> requestMarshaller = SubsidyApplyRequestMarshaller.getInstance();
        HttpResponseHandler<SubsidyApplyResponse> responseHandler =
                new DefaultHttpResponseHandler<SubsidyApplyResponse>(SubsidyApplyResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<SubsidyApplyRequest, SubsidyApplyResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public SubsidyBackResponse subsidyBack(SubsidyBackRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<SubsidyBackRequest> requestMarshaller = SubsidyBackRequestMarshaller.getInstance();
        HttpResponseHandler<SubsidyBackResponse> responseHandler =
                new DefaultHttpResponseHandler<SubsidyBackResponse>(SubsidyBackResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<SubsidyBackRequest, SubsidyBackResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public SubsidyBackQueryResponse subsidyBackQuery(SubsidyBackQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<SubsidyBackQueryRequest> requestMarshaller = SubsidyBackQueryRequestMarshaller.getInstance();
        HttpResponseHandler<SubsidyBackQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<SubsidyBackQueryResponse>(SubsidyBackQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<SubsidyBackQueryRequest, SubsidyBackQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public SubsidyQueryResponse subsidyQuery(SubsidyQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<SubsidyQueryRequest> requestMarshaller = SubsidyQueryRequestMarshaller.getInstance();
        HttpResponseHandler<SubsidyQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<SubsidyQueryResponse>(SubsidyQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<SubsidyQueryRequest, SubsidyQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }

    @Override
    public void shutdown() {
        clientHandler.shutdown();
    }

}
