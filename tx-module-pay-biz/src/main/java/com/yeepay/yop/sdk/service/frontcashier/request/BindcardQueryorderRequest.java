/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class BindcardQueryorderRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String merchantNo;

    private String merchantFlowId;

    private String nopOrderId;

    private String phone;


    /**
     * Get merchantNo
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    /**
     * Get merchantFlowId
     * @return merchantFlowId
     **/
    
    public String getMerchantFlowId() {
        return merchantFlowId;
    }

    public void setMerchantFlowId(String merchantFlowId) {
        this.merchantFlowId = merchantFlowId;
    }

    /**
     * Get nopOrderId
     * @return nopOrderId
     **/
    
    public String getNopOrderId() {
        return nopOrderId;
    }

    public void setNopOrderId(String nopOrderId) {
        this.nopOrderId = nopOrderId;
    }

    /**
     * Get phone
     * @return phone
     **/
    
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Override
    public String getOperationId() {
        return "bindcardQueryorder";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
