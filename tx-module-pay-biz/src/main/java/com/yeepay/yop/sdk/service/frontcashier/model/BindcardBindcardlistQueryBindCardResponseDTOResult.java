/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * BindcardBindcardlistQueryBindCardResponseDTOResult
 */
public class BindcardBindcardlistQueryBindCardResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 用户标识
   */
  @JsonProperty("userNo")
  private String userNo = null;

  /**
   * 用户标识类型
   */
  @JsonProperty("userType")
  private String userType = null;

  /**
   * 绑卡信息列表
   */
  @JsonProperty("bindCardList")
  private String bindCardList = null;

  public BindcardBindcardlistQueryBindCardResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BindcardBindcardlistQueryBindCardResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public BindcardBindcardlistQueryBindCardResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public BindcardBindcardlistQueryBindCardResponseDTOResult userNo(String userNo) {
    this.userNo = userNo;
    return this;
  }

   /**
   * 用户标识
   * @return userNo
  **/

  public String getUserNo() {
    return userNo;
  }

  public void setUserNo(String userNo) {
    this.userNo = userNo;
  }

  public BindcardBindcardlistQueryBindCardResponseDTOResult userType(String userType) {
    this.userType = userType;
    return this;
  }

   /**
   * 用户标识类型
   * @return userType
  **/

  public String getUserType() {
    return userType;
  }

  public void setUserType(String userType) {
    this.userType = userType;
  }

  public BindcardBindcardlistQueryBindCardResponseDTOResult bindCardList(String bindCardList) {
    this.bindCardList = bindCardList;
    return this;
  }

   /**
   * 绑卡信息列表
   * @return bindCardList
  **/

  public String getBindCardList() {
    return bindCardList;
  }

  public void setBindCardList(String bindCardList) {
    this.bindCardList = bindCardList;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BindcardBindcardlistQueryBindCardResponseDTOResult bindcardBindcardlistQueryBindCardResponseDTOResult = (BindcardBindcardlistQueryBindCardResponseDTOResult) o;
    return ObjectUtils.equals(this.code, bindcardBindcardlistQueryBindCardResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, bindcardBindcardlistQueryBindCardResponseDTOResult.message) &&
    ObjectUtils.equals(this.merchantNo, bindcardBindcardlistQueryBindCardResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.userNo, bindcardBindcardlistQueryBindCardResponseDTOResult.userNo) &&
    ObjectUtils.equals(this.userType, bindcardBindcardlistQueryBindCardResponseDTOResult.userType) &&
    ObjectUtils.equals(this.bindCardList, bindcardBindcardlistQueryBindCardResponseDTOResult.bindCardList);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, merchantNo, userNo, userType, bindCardList);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindcardBindcardlistQueryBindCardResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    userNo: ").append(toIndentedString(userNo)).append("\n");
    sb.append("    userType: ").append(toIndentedString(userType)).append("\n");
    sb.append("    bindCardList: ").append(toIndentedString(bindCardList)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

