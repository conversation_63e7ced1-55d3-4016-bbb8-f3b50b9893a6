/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import com.yeepay.yop.sdk.service.frontcashier.model.APIOfflineTransferAccountInfoConfirmRequestDTO;
public class AccountConfirmRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private APIOfflineTransferAccountInfoConfirmRequestDTO body;


    /**
     * Get body
     * @return body
     **/
    
    public APIOfflineTransferAccountInfoConfirmRequestDTO getBody() {
        return body;
    }

    public void setBody(APIOfflineTransferAccountInfoConfirmRequestDTO body) {
        this.body = body;
    }

    @Override
    public String getOperationId() {
        return "accountConfirm";
    }
}
