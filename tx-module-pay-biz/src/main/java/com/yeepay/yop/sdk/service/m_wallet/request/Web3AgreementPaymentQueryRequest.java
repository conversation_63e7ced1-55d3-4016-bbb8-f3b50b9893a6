/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class Web3AgreementPaymentQueryRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String merchantUserNo;

    private String parentMerchantNo;

    private String merchantNo;

    private String operatedMerchantNo;


    /**
     * &lt;p&gt;商户用户id&lt;/p&gt;
     * @return merchantUserNo
     **/
    
    public String getMerchantUserNo() {
        return merchantUserNo;
    }

    public void setMerchantUserNo(String merchantUserNo) {
        this.merchantUserNo = merchantUserNo;
    }

    /**
     * &lt;p&gt;业务发起方商编（标准商户收付款方案中此参数与商编一致，平台商户收付款方案中此参数为平台商商户编号&lt;/p&gt;
     * @return parentMerchantNo
     **/
    
    public String getParentMerchantNo() {
        return parentMerchantNo;
    }

    public void setParentMerchantNo(String parentMerchantNo) {
        this.parentMerchantNo = parentMerchantNo;
    }

    /**
     * &lt;p&gt;商户编号&amp;nbsp;易宝支付分配的的商户唯一标识&lt;/p&gt;
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    /**
     * &lt;p&gt;代托管商编&lt;/p&gt;
     * @return operatedMerchantNo
     **/
    
    public String getOperatedMerchantNo() {
        return operatedMerchantNo;
    }

    public void setOperatedMerchantNo(String operatedMerchantNo) {
        this.operatedMerchantNo = operatedMerchantNo;
    }

    @Override
    public String getOperationId() {
        return "web3AgreementPaymentQuery";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
