/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * YjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult
 */
public class YjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 商编
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 订单token
   */
  @JsonProperty("token")
  private String token = null;

  /**
   * 支付记录流水号
   */
  @JsonProperty("recordId")
  private String recordId = null;

  /**
   * 绑卡ID
   */
  @JsonProperty("bindId")
  private String bindId = null;

  public YjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public YjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回信息
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public YjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商编
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public YjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult token(String token) {
    this.token = token;
    return this;
  }

   /**
   * 订单token
   * @return token
  **/

  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  public YjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult recordId(String recordId) {
    this.recordId = recordId;
    return this;
  }

   /**
   * 支付记录流水号
   * @return recordId
  **/

  public String getRecordId() {
    return recordId;
  }

  public void setRecordId(String recordId) {
    this.recordId = recordId;
  }

  public YjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult bindId(String bindId) {
    this.bindId = bindId;
    return this;
  }

   /**
   * 绑卡ID
   * @return bindId
  **/

  public String getBindId() {
    return bindId;
  }

  public void setBindId(String bindId) {
    this.bindId = bindId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    YjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult yjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult = (YjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult) o;
    return ObjectUtils.equals(this.code, yjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, yjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult.message) &&
    ObjectUtils.equals(this.merchantNo, yjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.token, yjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult.token) &&
    ObjectUtils.equals(this.recordId, yjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult.recordId) &&
    ObjectUtils.equals(this.bindId, yjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult.bindId);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, merchantNo, token, recordId, bindId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class YjzfPaymentconfirmAPIYJZFConfirmPayResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    token: ").append(toIndentedString(token)).append("\n");
    sb.append("    recordId: ").append(toIndentedString(recordId)).append("\n");
    sb.append("    bindId: ").append(toIndentedString(bindId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

