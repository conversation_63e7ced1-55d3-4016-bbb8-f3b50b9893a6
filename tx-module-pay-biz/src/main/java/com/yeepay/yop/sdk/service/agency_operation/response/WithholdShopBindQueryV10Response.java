/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.agency_operation.response;

import com.yeepay.yop.sdk.service.agency_operation.model.BaseResultYopQueryMerchantShopBindResDTO;

public class WithholdShopBindQueryV10Response extends com.yeepay.yop.sdk.model.BaseResponse {
    private static final long serialVersionUID = 1L;

    private BaseResultYopQueryMerchantShopBindResDTO result;

    public BaseResultYopQueryMerchantShopBindResDTO getResult() {
        return result;
    }

    public void setResult(BaseResultYopQueryMerchantShopBindResDTO result) {
        this.result = result;
    }

}
