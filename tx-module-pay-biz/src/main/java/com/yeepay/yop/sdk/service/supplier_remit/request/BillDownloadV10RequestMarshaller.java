/*
 * 供应商付款系统
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.supplier_remit.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class BillDownloadV10RequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<BillDownloadV10Request> {
    private final String serviceName = "SupplierRemit";

    private final String resourcePath = "/yos/v1.0/supplier-remit/bill/download";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.GET;


    @Override
    public Request<BillDownloadV10Request> marshall(BillDownloadV10Request request) {
        Request<BillDownloadV10Request> internalRequest = new DefaultRequest<BillDownloadV10Request>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        internalRequest.assignYos();
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getRemitDate() != null) {
            internalRequest.addParameter("remitDate", PrimitiveMarshallerUtils.marshalling(request.getRemitDate(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static BillDownloadV10RequestMarshaller INSTANCE = new BillDownloadV10RequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static BillDownloadV10RequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
