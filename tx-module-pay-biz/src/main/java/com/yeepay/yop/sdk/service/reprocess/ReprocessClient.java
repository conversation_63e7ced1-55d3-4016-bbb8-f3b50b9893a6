/*
 * 再处理
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.reprocess;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.reprocess.request.*;
import com.yeepay.yop.sdk.service.reprocess.response.*;

public interface ReprocessClient {

    /**
     * 申请分账余额存管至银行
     * 申请分账余额存管至银行
     * @return MigrateBankOrderResponse
     * @throws YopClientException if fails to make API call
     */
    MigrateBankOrderResponse migrateBankOrder(MigrateBankOrderRequest request) throws YopClientException;

    /**
     * 查询分账余额存管至银行结果
     * 查询分账余额存管至银行结果
     * @return MigrateBankQueryResponse
     * @throws YopClientException if fails to make API call
     */
    MigrateBankQueryResponse migrateBankQuery(MigrateBankQueryRequest request) throws YopClientException;

    /**
     * 申请分账余额存管至银行
     * 申请分账余额存管至银行
     * @return MigrateBankOrderV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    MigrateBankOrderV10Response migrate_bank_order_v1_0(MigrateBankOrderV10Request request) throws YopClientException;

    /**
     * 查询分账余额存管至银行结果
     * 查询分账余额存管至银行结果
     * @return MigrateBankQueryV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    MigrateBankQueryV10Response migrate_bank_query_v1_0(MigrateBankQueryV10Request request) throws YopClientException;


    /**
     * Shuts down this client object, releasing any resources that might be held open. This is an optional method, and
     * callers are not expected to call it, but can if they want to explicitly release any open resources. Once a client
     * has been shutdown, it should not be used to make any more requests.
     */
    void shutdown();
}
