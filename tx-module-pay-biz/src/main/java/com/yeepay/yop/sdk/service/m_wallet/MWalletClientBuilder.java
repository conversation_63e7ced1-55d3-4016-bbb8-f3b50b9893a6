/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet;


import com.yeepay.yop.sdk.auth.req.AuthorizationReqRegistry;
import com.yeepay.yop.sdk.auth.req.DefaultAuthorizationReqRegistry;
import com.yeepay.yop.sdk.client.ClientParams;
import com.yeepay.yop.sdk.client.AbstractServiceClientBuilder;

public class MWalletClientBuilder extends AbstractServiceClientBuilder<MWalletClientBuilder, MWalletClientImpl>{

    private static final AuthorizationReqRegistry REGISTRY;

    static {
        REGISTRY = new DefaultAuthorizationReqRegistry();
        REGISTRY.register("accountFaceCertifyOpen", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("accountOpen", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("accountOpenNotify", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("accountQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("accountQueryBalance", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("accountQueryQuota", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("account_face_certify_open", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("account_open_notify", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("account_open_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("account_query_balance_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("account_query_quota", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("account_query_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("agreementPaymentCancel", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("agreementPaymentQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("agreementPaymentRequest", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("agreementPaymentSign", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("agreement_payment_cancel_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("agreement_payment_cancel_web3_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("agreement_payment_notify_web3", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("agreement_payment_query", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("agreement_payment_query_web3_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("agreement_payment_request_v1", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("agreement_payment_sign_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("agreement_payment_sign_web3_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("autoWithdraw", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("autoWithdrawQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("auto_deduction_create", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("auto_deduction_query", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("auto_withdraw", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("auto_withdraw_query", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bankAccountConfirm", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bankAccountOpen", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bankAccountQueryActivation", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bankAccountQueryComplaint", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bankAccountQueryOpenResult", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bankAccountQueryTrade", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bankAccountQueryWithdraw", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bankAccountSendMsg", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bankAccountUpdateKeyWords", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bankAccountWithdraw", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bank_account_confirm_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bank_account_open_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bank_account_query_activation_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bank_account_query_complaint_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bank_account_query_open_result_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bank_account_query_trade_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bank_account_query_withdraw_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bank_account_send_msg_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bank_account_update_key_words_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bank_account_withdraw_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("billQueryDetail", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("billQueryList", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("billQueryListV2", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("billQueryOverview", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bill_query_detail", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bill_query_list", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bill_query_overview", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("cardQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("card_query_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("coupon_list_query_web3", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("manageFeeQueryDeduct", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("manage_fee_query_deduct", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("memberCardList", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("memberQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("member_card_list", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("member_query_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("passwordManage", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("password_manage_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("payment_manage_web3_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("rechargeInitiate", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("rechargeQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("recharge_initiate_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("recharge_query_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("subscribeExpireNotify", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("subscribe_expire_notify", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("tradeAutoDeductionCreate", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("tradeAutoDeductionQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("tradeOrder", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("tradeOrderV2", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("trade_order_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("trade_order_v2_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("transferB2cInitiate", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("transferB2cMarket", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("transferB2cQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("transfer_b2c_initiate_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("transfer_b2c_market_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("transfer_b2c_query_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("walletCancel", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("walletIndexV2", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("wallet_cancel_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("wallet_index_v2_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("web3AgreementNotify", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("web3AgreementPaymentCancel", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("web3AgreementPaymentQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("web3AgreementPaymentSign", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("web3CouponListQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("web3PaymentManage", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("withdrawInitiate", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("withdrawQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("withdraw_initiate_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("withdraw_query_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
    }

    @Override
    protected AuthorizationReqRegistry authorizationReqRegistry() {
        return REGISTRY;
    }

    @Override
    protected MWalletClientImpl build(ClientParams params) {
        return new MWalletClientImpl(params);
    }

    /**
     * @return Create new instance of builder with all defaults set.
     */
    public static MWalletClientBuilder builder(){
        return new MWalletClientBuilder();
    }

}
