/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class AgreementPaymentQueryWeb3V10RequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<AgreementPaymentQueryWeb3V10Request> {
    private final String serviceName = "MWallet";

    private final String resourcePath = "/rest/v1.0/m-wallet/web3/agreement/payment-query";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.GET;


    @Override
    public Request<AgreementPaymentQueryWeb3V10Request> marshall(AgreementPaymentQueryWeb3V10Request request) {
        Request<AgreementPaymentQueryWeb3V10Request> internalRequest = new DefaultRequest<AgreementPaymentQueryWeb3V10Request>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getMerchantUserNo() != null) {
            internalRequest.addParameter("merchantUserNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantUserNo(), "String"));
        }
        if (request.getParentMerchantNo() != null) {
            internalRequest.addParameter("parentMerchantNo", PrimitiveMarshallerUtils.marshalling(request.getParentMerchantNo(), "String"));
        }
        if (request.getMerchantNo() != null) {
            internalRequest.addParameter("merchantNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantNo(), "String"));
        }
        if (request.getOperatedMerchantNo() != null) {
            internalRequest.addParameter("operatedMerchantNo", PrimitiveMarshallerUtils.marshalling(request.getOperatedMerchantNo(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static AgreementPaymentQueryWeb3V10RequestMarshaller INSTANCE = new AgreementPaymentQueryWeb3V10RequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static AgreementPaymentQueryWeb3V10RequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
