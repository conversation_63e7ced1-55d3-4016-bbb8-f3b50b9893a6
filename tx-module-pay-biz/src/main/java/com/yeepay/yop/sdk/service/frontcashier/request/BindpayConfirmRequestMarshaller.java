/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class BindpayConfirmRequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<BindpayConfirmRequest> {
    private final String serviceName = "Frontcashier";

    private final String resourcePath = "/rest/v1.0/frontcashier/bindpay/confirm";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.POST;


    @Override
    public Request<BindpayConfirmRequest> marshall(BindpayConfirmRequest request) {
        Request<BindpayConfirmRequest> internalRequest = new DefaultRequest<BindpayConfirmRequest>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getToken() != null) {
            internalRequest.addParameter("token", PrimitiveMarshallerUtils.marshalling(request.getToken(), "String"));
        }
        if (request.getVersion() != null) {
            internalRequest.addParameter("version", PrimitiveMarshallerUtils.marshalling(request.getVersion(), "String"));
        }
        if (request.getVerifyCode() != null) {
            internalRequest.addParameter("verifyCode", PrimitiveMarshallerUtils.marshalling(request.getVerifyCode(), "String"));
        }
        if (request.getCardno() != null) {
            internalRequest.addParameter("cardno", PrimitiveMarshallerUtils.marshalling(request.getCardno(), "String"));
        }
        if (request.getOwner() != null) {
            internalRequest.addParameter("owner", PrimitiveMarshallerUtils.marshalling(request.getOwner(), "String"));
        }
        if (request.getIdno() != null) {
            internalRequest.addParameter("idno", PrimitiveMarshallerUtils.marshalling(request.getIdno(), "String"));
        }
        if (request.getPhoneNo() != null) {
            internalRequest.addParameter("phoneNo", PrimitiveMarshallerUtils.marshalling(request.getPhoneNo(), "String"));
        }
        if (request.getYpMobile() != null) {
            internalRequest.addParameter("ypMobile", PrimitiveMarshallerUtils.marshalling(request.getYpMobile(), "String"));
        }
        if (request.getAvlidDate() != null) {
            internalRequest.addParameter("avlidDate", PrimitiveMarshallerUtils.marshalling(request.getAvlidDate(), "String"));
        }
        if (request.getCvv2() != null) {
            internalRequest.addParameter("cvv2", PrimitiveMarshallerUtils.marshalling(request.getCvv2(), "String"));
        }
        if (request.getIdCardType() != null) {
            internalRequest.addParameter("idCardType", PrimitiveMarshallerUtils.marshalling(request.getIdCardType(), "String"));
        }
        if (request.getBankPWD() != null) {
            internalRequest.addParameter("bankPWD", PrimitiveMarshallerUtils.marshalling(request.getBankPWD(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static BindpayConfirmRequestMarshaller INSTANCE = new BindpayConfirmRequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static BindpayConfirmRequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
