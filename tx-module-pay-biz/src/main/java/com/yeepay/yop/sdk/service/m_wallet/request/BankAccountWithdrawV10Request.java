/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet.request;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class BankAccountWithdrawV10Request extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String merchantMemberNo;

    private String requestNo;

    private String accountNo;

    private String accountType;

    private String oneAccountBankNo;

    private String bindBankPhone;

    private BigDecimal withdrawPrice;

    private String remark;

    private String parentMerchantNo;

    private String merchantNo;


    /**
     * Get merchantMemberNo
     * @return merchantMemberNo
     **/
    
    public String getMerchantMemberNo() {
        return merchantMemberNo;
    }

    public void setMerchantMemberNo(String merchantMemberNo) {
        this.merchantMemberNo = merchantMemberNo;
    }

    /**
     * Get requestNo
     * @return requestNo
     **/
    
    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    /**
     * Get accountNo
     * @return accountNo
     **/
    
    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    /**
     * Get accountType
     * @return accountType
     **/
    
    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    /**
     * Get oneAccountBankNo
     * @return oneAccountBankNo
     **/
    
    public String getOneAccountBankNo() {
        return oneAccountBankNo;
    }

    public void setOneAccountBankNo(String oneAccountBankNo) {
        this.oneAccountBankNo = oneAccountBankNo;
    }

    /**
     * Get bindBankPhone
     * @return bindBankPhone
     **/
    
    public String getBindBankPhone() {
        return bindBankPhone;
    }

    public void setBindBankPhone(String bindBankPhone) {
        this.bindBankPhone = bindBankPhone;
    }

    /**
     * Get withdrawPrice
     * @return withdrawPrice
     **/
    
    public BigDecimal getWithdrawPrice() {
        return withdrawPrice;
    }

    public void setWithdrawPrice(BigDecimal withdrawPrice) {
        this.withdrawPrice = withdrawPrice;
    }

    /**
     * Get remark
     * @return remark
     **/
    
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * Get parentMerchantNo
     * @return parentMerchantNo
     **/
    
    public String getParentMerchantNo() {
        return parentMerchantNo;
    }

    public void setParentMerchantNo(String parentMerchantNo) {
        this.parentMerchantNo = parentMerchantNo;
    }

    /**
     * Get merchantNo
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    @Override
    public String getOperationId() {
        return "bank_account_withdraw_v1_0";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
