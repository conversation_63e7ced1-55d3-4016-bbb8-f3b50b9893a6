/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * UpopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult
 */
public class UpopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 错误码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 错误码描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 付款序列号
   */
  @JsonProperty("paySerialNo")
  private String paySerialNo = null;

  /**
   * 收款方商户名称
   */
  @JsonProperty("payeeMerchantName")
  private String payeeMerchantName = null;

  /**
   * 收款方附言
   */
  @JsonProperty("payeeComments")
  private String payeeComments = null;

  /**
   * 交易金额
   */
  @JsonProperty("tradeAmount")
  private BigDecimal tradeAmount = null;

  public UpopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 错误码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public UpopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 错误码描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public UpopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult paySerialNo(String paySerialNo) {
    this.paySerialNo = paySerialNo;
    return this;
  }

   /**
   * 付款序列号
   * @return paySerialNo
  **/

  public String getPaySerialNo() {
    return paySerialNo;
  }

  public void setPaySerialNo(String paySerialNo) {
    this.paySerialNo = paySerialNo;
  }

  public UpopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult payeeMerchantName(String payeeMerchantName) {
    this.payeeMerchantName = payeeMerchantName;
    return this;
  }

   /**
   * 收款方商户名称
   * @return payeeMerchantName
  **/

  public String getPayeeMerchantName() {
    return payeeMerchantName;
  }

  public void setPayeeMerchantName(String payeeMerchantName) {
    this.payeeMerchantName = payeeMerchantName;
  }

  public UpopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult payeeComments(String payeeComments) {
    this.payeeComments = payeeComments;
    return this;
  }

   /**
   * 收款方附言
   * @return payeeComments
  **/

  public String getPayeeComments() {
    return payeeComments;
  }

  public void setPayeeComments(String payeeComments) {
    this.payeeComments = payeeComments;
  }

  public UpopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult tradeAmount(BigDecimal tradeAmount) {
    this.tradeAmount = tradeAmount;
    return this;
  }

   /**
   * 交易金额
   * @return tradeAmount
  **/

  public BigDecimal getTradeAmount() {
    return tradeAmount;
  }

  public void setTradeAmount(BigDecimal tradeAmount) {
    this.tradeAmount = tradeAmount;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    UpopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult upopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult = (UpopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult) o;
    return ObjectUtils.equals(this.code, upopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, upopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult.message) &&
    ObjectUtils.equals(this.paySerialNo, upopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult.paySerialNo) &&
    ObjectUtils.equals(this.payeeMerchantName, upopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult.payeeMerchantName) &&
    ObjectUtils.equals(this.payeeComments, upopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult.payeeComments) &&
    ObjectUtils.equals(this.tradeAmount, upopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult.tradeAmount);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, paySerialNo, payeeMerchantName, payeeComments, tradeAmount);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UpopActivescanQuerypayeeorderOpenQueryPayeeOrderInfoResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    paySerialNo: ").append(toIndentedString(paySerialNo)).append("\n");
    sb.append("    payeeMerchantName: ").append(toIndentedString(payeeMerchantName)).append("\n");
    sb.append("    payeeComments: ").append(toIndentedString(payeeComments)).append("\n");
    sb.append("    tradeAmount: ").append(toIndentedString(tradeAmount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

