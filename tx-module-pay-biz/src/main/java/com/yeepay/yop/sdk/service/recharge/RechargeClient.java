/*
 * 商户充值
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.recharge;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.recharge.request.*;
import com.yeepay.yop.sdk.service.recharge.response.*;

public interface RechargeClient {

    /**
     * 银行账户查询余额
     * 
     * @return BankAccountQueryResponse
     * @throws YopClientException if fails to make API call
     */
    BankAccountQueryResponse bankAccountQuery(BankAccountQueryRequest request) throws YopClientException;


    /**
     * Shuts down this client object, releasing any resources that might be held open. This is an optional method, and
     * callers are not expected to call it, but can if they want to explicitly release any open resources. Once a client
     * has been shutdown, it should not be used to make any more requests.
     */
    void shutdown();
}
