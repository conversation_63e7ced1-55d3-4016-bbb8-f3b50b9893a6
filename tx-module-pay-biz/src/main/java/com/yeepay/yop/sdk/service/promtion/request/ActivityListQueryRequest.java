/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.promtion.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class ActivityListQueryRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String merchantNo;

    private String merchantActivityNo;

    private String marketingNo;

    private String activityStatus;

    private Integer pageNo;

    private Integer pageSize;


    /**
     * Get merchantNo
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    /**
     * Get merchantActivityNo
     * @return merchantActivityNo
     **/
    
    public String getMerchantActivityNo() {
        return merchantActivityNo;
    }

    public void setMerchantActivityNo(String merchantActivityNo) {
        this.merchantActivityNo = merchantActivityNo;
    }

    /**
     * Get marketingNo
     * @return marketingNo
     **/
    
    public String getMarketingNo() {
        return marketingNo;
    }

    public void setMarketingNo(String marketingNo) {
        this.marketingNo = marketingNo;
    }

    /**
     * Get activityStatus
     * @return activityStatus
     **/
    
    public String getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(String activityStatus) {
        this.activityStatus = activityStatus;
    }

    /**
     * Get pageNo
     * @return pageNo
     **/
    
    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    /**
     * Get pageSize
     * @return pageSize
     **/
    
    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String getOperationId() {
        return "activityListQuery";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
