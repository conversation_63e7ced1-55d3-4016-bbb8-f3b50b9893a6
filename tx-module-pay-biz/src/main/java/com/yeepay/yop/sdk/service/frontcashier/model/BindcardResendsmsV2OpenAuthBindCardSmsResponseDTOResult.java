/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * BindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult
 */
public class BindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 主商户编号
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * 商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 原商户绑卡流水号
   */
  @JsonProperty("merchantFlowId")
  private String merchantFlowId = null;

  /**
   * 易宝绑卡订单号
   */
  @JsonProperty("nopOrderId")
  private String nopOrderId = null;

  public BindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回信息
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public BindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * 主商户编号
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public BindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public BindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult merchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
    return this;
  }

   /**
   * 原商户绑卡流水号
   * @return merchantFlowId
  **/

  public String getMerchantFlowId() {
    return merchantFlowId;
  }

  public void setMerchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
  }

  public BindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult nopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
    return this;
  }

   /**
   * 易宝绑卡订单号
   * @return nopOrderId
  **/

  public String getNopOrderId() {
    return nopOrderId;
  }

  public void setNopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult bindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult = (BindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult) o;
    return ObjectUtils.equals(this.code, bindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, bindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult.message) &&
    ObjectUtils.equals(this.parentMerchantNo, bindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, bindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.merchantFlowId, bindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult.merchantFlowId) &&
    ObjectUtils.equals(this.nopOrderId, bindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult.nopOrderId);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, parentMerchantNo, merchantNo, merchantFlowId, nopOrderId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindcardResendsmsV2OpenAuthBindCardSmsResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantFlowId: ").append(toIndentedString(merchantFlowId)).append("\n");
    sb.append("    nopOrderId: ").append(toIndentedString(nopOrderId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

