/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.promtion.model.RightsConsumeDto;
import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 请修改我
 */
public class QueryRightsConsumeAdapterRespDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 
   */
  @JsonProperty("rightsConsumeList")
  private List<RightsConsumeDto> rightsConsumeList = null;

  /**
   * 
   */
  @JsonProperty("message")
  private String message = null;

  public QueryRightsConsumeAdapterRespDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * Get code
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public QueryRightsConsumeAdapterRespDTO rightsConsumeList(List<RightsConsumeDto> rightsConsumeList) {
    this.rightsConsumeList = rightsConsumeList;
    return this;
  }

  public QueryRightsConsumeAdapterRespDTO addRightsConsumeListItem(RightsConsumeDto rightsConsumeListItem) {
    if (this.rightsConsumeList == null) {
      this.rightsConsumeList = new ArrayList<>();
    }
    this.rightsConsumeList.add(rightsConsumeListItem);
    return this;
  }

   /**
   * Get rightsConsumeList
   * @return rightsConsumeList
  **/

  public List<RightsConsumeDto> getRightsConsumeList() {
    return rightsConsumeList;
  }

  public void setRightsConsumeList(List<RightsConsumeDto> rightsConsumeList) {
    this.rightsConsumeList = rightsConsumeList;
  }

  public QueryRightsConsumeAdapterRespDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * Get message
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QueryRightsConsumeAdapterRespDTO queryRightsConsumeAdapterRespDTO = (QueryRightsConsumeAdapterRespDTO) o;
    return ObjectUtils.equals(this.code, queryRightsConsumeAdapterRespDTO.code) &&
    ObjectUtils.equals(this.rightsConsumeList, queryRightsConsumeAdapterRespDTO.rightsConsumeList) &&
    ObjectUtils.equals(this.message, queryRightsConsumeAdapterRespDTO.message);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, rightsConsumeList, message);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryRightsConsumeAdapterRespDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    rightsConsumeList: ").append(toIndentedString(rightsConsumeList)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

