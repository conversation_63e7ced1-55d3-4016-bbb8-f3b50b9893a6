/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.travel_resources.request;

import com.yeepay.yop.sdk.service.travel_resources.model.KfcOrderRequestDTO;
public class KfcOrder0Request extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private KfcOrderRequestDTO body;


    /**
     * Get body
     * @return body
     **/
    
    public KfcOrderRequestDTO getBody() {
        return body;
    }

    public void setBody(KfcOrderRequestDTO body) {
        this.body = body;
    }

    @Override
    public String getOperationId() {
        return "kfcOrder_0";
    }
}
