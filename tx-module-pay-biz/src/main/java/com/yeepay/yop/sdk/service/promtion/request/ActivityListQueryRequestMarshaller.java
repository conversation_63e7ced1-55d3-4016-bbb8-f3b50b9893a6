/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.promtion.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class ActivityListQueryRequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<ActivityListQueryRequest> {
    private final String serviceName = "Promtion";

    private final String resourcePath = "/rest/v1.0/promtion/activity/list-query";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.POST;


    @Override
    public Request<ActivityListQueryRequest> marshall(ActivityListQueryRequest request) {
        Request<ActivityListQueryRequest> internalRequest = new DefaultRequest<ActivityListQueryRequest>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getMerchantNo() != null) {
            internalRequest.addParameter("merchantNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantNo(), "String"));
        }
        if (request.getMerchantActivityNo() != null) {
            internalRequest.addParameter("merchantActivityNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantActivityNo(), "String"));
        }
        if (request.getMarketingNo() != null) {
            internalRequest.addParameter("marketingNo", PrimitiveMarshallerUtils.marshalling(request.getMarketingNo(), "String"));
        }
        if (request.getActivityStatus() != null) {
            internalRequest.addParameter("activityStatus", PrimitiveMarshallerUtils.marshalling(request.getActivityStatus(), "String"));
        }
        if (request.getPageNo() != null) {
            internalRequest.addParameter("pageNo", PrimitiveMarshallerUtils.marshalling(request.getPageNo(), "Integer"));
        }
        if (request.getPageSize() != null) {
            internalRequest.addParameter("pageSize", PrimitiveMarshallerUtils.marshalling(request.getPageSize(), "Integer"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static ActivityListQueryRequestMarshaller INSTANCE = new ActivityListQueryRequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static ActivityListQueryRequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
