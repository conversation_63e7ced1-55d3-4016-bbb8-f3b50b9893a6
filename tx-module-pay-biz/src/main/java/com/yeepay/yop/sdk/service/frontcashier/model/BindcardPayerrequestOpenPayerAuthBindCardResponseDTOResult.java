/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult
 */
public class BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 主商户编号
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * 商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 商户订单号
   */
  @JsonProperty("merchantFlowId")
  private String merchantFlowId = null;

  /**
   * 易宝订单号
   */
  @JsonProperty("nopOrderId")
  private String nopOrderId = null;

  /**
   * 绑卡ID
   */
  @JsonProperty("bindId")
  private String bindId = null;

  /**
   * 跳页签约，页面提交方式
   */
  @JsonProperty("submitMethod")
  private String submitMethod = null;

  /**
   * 跳页签约请求地址
   */
  @JsonProperty("submitUrl")
  private String submitUrl = null;

  /**
   * 页面编码
   */
  @JsonProperty("encoding")
  private String encoding = null;

  /**
   * 短验发送方
   */
  @JsonProperty("smsSender")
  private String smsSender = null;

  /**
   * 短验类型
   */
  @JsonProperty("smsType")
  private String smsType = null;

  public BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回信息
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * 主商户编号
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult merchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
    return this;
  }

   /**
   * 商户订单号
   * @return merchantFlowId
  **/

  public String getMerchantFlowId() {
    return merchantFlowId;
  }

  public void setMerchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
  }

  public BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult nopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
    return this;
  }

   /**
   * 易宝订单号
   * @return nopOrderId
  **/

  public String getNopOrderId() {
    return nopOrderId;
  }

  public void setNopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
  }

  public BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult bindId(String bindId) {
    this.bindId = bindId;
    return this;
  }

   /**
   * 绑卡ID
   * @return bindId
  **/

  public String getBindId() {
    return bindId;
  }

  public void setBindId(String bindId) {
    this.bindId = bindId;
  }

  public BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult submitMethod(String submitMethod) {
    this.submitMethod = submitMethod;
    return this;
  }

   /**
   * 跳页签约，页面提交方式
   * @return submitMethod
  **/

  public String getSubmitMethod() {
    return submitMethod;
  }

  public void setSubmitMethod(String submitMethod) {
    this.submitMethod = submitMethod;
  }

  public BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult submitUrl(String submitUrl) {
    this.submitUrl = submitUrl;
    return this;
  }

   /**
   * 跳页签约请求地址
   * @return submitUrl
  **/

  public String getSubmitUrl() {
    return submitUrl;
  }

  public void setSubmitUrl(String submitUrl) {
    this.submitUrl = submitUrl;
  }

  public BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult encoding(String encoding) {
    this.encoding = encoding;
    return this;
  }

   /**
   * 页面编码
   * @return encoding
  **/

  public String getEncoding() {
    return encoding;
  }

  public void setEncoding(String encoding) {
    this.encoding = encoding;
  }

  public BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult smsSender(String smsSender) {
    this.smsSender = smsSender;
    return this;
  }

   /**
   * 短验发送方
   * @return smsSender
  **/

  public String getSmsSender() {
    return smsSender;
  }

  public void setSmsSender(String smsSender) {
    this.smsSender = smsSender;
  }

  public BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult smsType(String smsType) {
    this.smsType = smsType;
    return this;
  }

   /**
   * 短验类型
   * @return smsType
  **/

  public String getSmsType() {
    return smsType;
  }

  public void setSmsType(String smsType) {
    this.smsType = smsType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult = (BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult) o;
    return ObjectUtils.equals(this.code, bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult.message) &&
    ObjectUtils.equals(this.parentMerchantNo, bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.merchantFlowId, bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult.merchantFlowId) &&
    ObjectUtils.equals(this.nopOrderId, bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult.nopOrderId) &&
    ObjectUtils.equals(this.bindId, bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult.bindId) &&
    ObjectUtils.equals(this.submitMethod, bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult.submitMethod) &&
    ObjectUtils.equals(this.submitUrl, bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult.submitUrl) &&
    ObjectUtils.equals(this.encoding, bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult.encoding) &&
    ObjectUtils.equals(this.smsSender, bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult.smsSender) &&
    ObjectUtils.equals(this.smsType, bindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult.smsType);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, parentMerchantNo, merchantNo, merchantFlowId, nopOrderId, bindId, submitMethod, submitUrl, encoding, smsSender, smsType);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindcardPayerrequestOpenPayerAuthBindCardResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantFlowId: ").append(toIndentedString(merchantFlowId)).append("\n");
    sb.append("    nopOrderId: ").append(toIndentedString(nopOrderId)).append("\n");
    sb.append("    bindId: ").append(toIndentedString(bindId)).append("\n");
    sb.append("    submitMethod: ").append(toIndentedString(submitMethod)).append("\n");
    sb.append("    submitUrl: ").append(toIndentedString(submitUrl)).append("\n");
    sb.append("    encoding: ").append(toIndentedString(encoding)).append("\n");
    sb.append("    smsSender: ").append(toIndentedString(smsSender)).append("\n");
    sb.append("    smsType: ").append(toIndentedString(smsType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

