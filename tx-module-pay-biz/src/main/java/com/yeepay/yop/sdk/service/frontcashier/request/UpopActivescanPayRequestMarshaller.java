/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class UpopActivescanPayRequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<UpopActivescanPayRequest> {
    private final String serviceName = "Frontcashier";

    private final String resourcePath = "/rest/v1.0/frontcashier/upop/activescan/pay";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.POST;


    @Override
    public Request<UpopActivescanPayRequest> marshall(UpopActivescanPayRequest request) {
        Request<UpopActivescanPayRequest> internalRequest = new DefaultRequest<UpopActivescanPayRequest>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getMerchantNo() != null) {
            internalRequest.addParameter("merchantNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantNo(), "String"));
        }
        if (request.getMerchantFlowId() != null) {
            internalRequest.addParameter("merchantFlowId", PrimitiveMarshallerUtils.marshalling(request.getMerchantFlowId(), "String"));
        }
        if (request.getPaySerialNo() != null) {
            internalRequest.addParameter("paySerialNo", PrimitiveMarshallerUtils.marshalling(request.getPaySerialNo(), "String"));
        }
        if (request.getCouponSerialNo() != null) {
            internalRequest.addParameter("couponSerialNo", PrimitiveMarshallerUtils.marshalling(request.getCouponSerialNo(), "String"));
        }
        if (request.getTradeAmount() != null) {
            internalRequest.addParameter("tradeAmount", PrimitiveMarshallerUtils.marshalling(request.getTradeAmount(), "BigDecimal"));
        }
        if (request.getBindId() != null) {
            internalRequest.addParameter("bindId", PrimitiveMarshallerUtils.marshalling(request.getBindId(), "Long"));
        }
        if (request.getUserNo() != null) {
            internalRequest.addParameter("userNo", PrimitiveMarshallerUtils.marshalling(request.getUserNo(), "String"));
        }
        if (request.getUserType() != null) {
            internalRequest.addParameter("userType", PrimitiveMarshallerUtils.marshalling(request.getUserType(), "String"));
        }
        if (request.getCallBackUrl() != null) {
            internalRequest.addParameter("callBackUrl", PrimitiveMarshallerUtils.marshalling(request.getCallBackUrl(), "String"));
        }
        if (request.getPayComments() != null) {
            internalRequest.addParameter("payComments", PrimitiveMarshallerUtils.marshalling(request.getPayComments(), "String"));
        }
        if (request.getDeviceID() != null) {
            internalRequest.addParameter("deviceID", PrimitiveMarshallerUtils.marshalling(request.getDeviceID(), "String"));
        }
        if (request.getDeviceType() != null) {
            internalRequest.addParameter("deviceType", PrimitiveMarshallerUtils.marshalling(request.getDeviceType(), "String"));
        }
        if (request.getAccountIDHash() != null) {
            internalRequest.addParameter("accountIDHash", PrimitiveMarshallerUtils.marshalling(request.getAccountIDHash(), "String"));
        }
        if (request.getSourceIP() != null) {
            internalRequest.addParameter("sourceIP", PrimitiveMarshallerUtils.marshalling(request.getSourceIP(), "String"));
        }
        if (request.getUsrRgstrDt() != null) {
            internalRequest.addParameter("usrRgstrDt", PrimitiveMarshallerUtils.marshalling(request.getUsrRgstrDt(), "String"));
        }
        if (request.getAccountEmailLife() != null) {
            internalRequest.addParameter("accountEmailLife", PrimitiveMarshallerUtils.marshalling(request.getAccountEmailLife(), "String"));
        }
        if (request.getDeviceLocation() != null) {
            internalRequest.addParameter("deviceLocation", PrimitiveMarshallerUtils.marshalling(request.getDeviceLocation(), "String"));
        }
        if (request.getFullDeviceNumber() != null) {
            internalRequest.addParameter("fullDeviceNumber", PrimitiveMarshallerUtils.marshalling(request.getFullDeviceNumber(), "String"));
        }
        if (request.getCaptureMethod() != null) {
            internalRequest.addParameter("captureMethod", PrimitiveMarshallerUtils.marshalling(request.getCaptureMethod(), "String"));
        }
        if (request.getDeviceSimNumber() != null) {
            internalRequest.addParameter("deviceSimNumber", PrimitiveMarshallerUtils.marshalling(request.getDeviceSimNumber(), "String"));
        }
        if (request.getDeviceName() != null) {
            internalRequest.addParameter("deviceName", PrimitiveMarshallerUtils.marshalling(request.getDeviceName(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static UpopActivescanPayRequestMarshaller INSTANCE = new UpopActivescanPayRequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static UpopActivescanPayRequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
