/*
 * API收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.nccashierapi.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.internal.RestartableInputStream;
import com.yeepay.yop.sdk.utils.JsonUtils;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;


public class BindCardQueryRequestMarshaller  implements RequestMarshaller<BindCardQueryRequest> {
    private final String serviceName = "Nccashierapi";

    private final String resourcePath = "/rest/v1.0/nccashierapi/bindcard/query-request";

    private final String contentType = "application/json";

    private final HttpMethodName httpMethodName = HttpMethodName.POST;


    @Override
    public Request<BindCardQueryRequest> marshall(BindCardQueryRequest request) {
        Request<BindCardQueryRequest> internalRequest = new DefaultRequest<BindCardQueryRequest>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        String contentStr = JsonUtils.toJsonString(request.getBody());
        byte[] content = contentStr.getBytes(YopConstants.DEFAULT_CHARSET);
        internalRequest.addHeader(Headers.CONTENT_LENGTH, String.valueOf(content.length));
        internalRequest.setContent(RestartableInputStream.wrap(content));
        internalRequest.setContentType(YopContentType.JSON);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static BindCardQueryRequestMarshaller INSTANCE = new BindCardQueryRequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static BindCardQueryRequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
