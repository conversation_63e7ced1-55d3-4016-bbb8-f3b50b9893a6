/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yeepay.yop.sdk.service.base.request.serializer.YopCustomSerializer;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 退款订单请求参数
 */
public class RefundOrderRequestDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;请求商编&lt;/p&gt;
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;p&gt;商户请求单号&lt;/p&gt;
   */
  @JsonProperty("merchantRequestNo")
  private String merchantRequestNo = null;

  /**
   * &lt;p&gt;商户退款订单号&lt;/p&gt;
   */
  @JsonProperty("merchantRefundRequestNo")
  private String merchantRefundRequestNo = null;

  /**
   * &lt;p&gt;请求商户订单号&lt;/p&gt;
   */
  @JsonProperty("parentMerchantRequestNo")
  private String parentMerchantRequestNo = null;

  /**
   * &lt;p&gt;请求商户退款订单号&lt;/p&gt;
   */
  @JsonProperty("parentMerchantRefundRequestNo")
  private String parentMerchantRefundRequestNo = null;

  /**
   * &lt;p&gt;退款金额&lt;/p&gt;
   */
  @JsonProperty("refundAmount")
  private BigDecimal refundAmount = null;

  /**
   * &lt;p&gt;通知地址&lt;/p&gt;
   */
  @JsonProperty("complateNotifyUrl")
  private String complateNotifyUrl = null;

  public RefundOrderRequestDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;请求商编&lt;/p&gt;
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public RefundOrderRequestDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public RefundOrderRequestDTO merchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;商户请求单号&lt;/p&gt;
   * @return merchantRequestNo
  **/

  public String getMerchantRequestNo() {
    return merchantRequestNo;
  }

  public void setMerchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
  }

  public RefundOrderRequestDTO merchantRefundRequestNo(String merchantRefundRequestNo) {
    this.merchantRefundRequestNo = merchantRefundRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;商户退款订单号&lt;/p&gt;
   * @return merchantRefundRequestNo
  **/

  public String getMerchantRefundRequestNo() {
    return merchantRefundRequestNo;
  }

  public void setMerchantRefundRequestNo(String merchantRefundRequestNo) {
    this.merchantRefundRequestNo = merchantRefundRequestNo;
  }

  public RefundOrderRequestDTO parentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;请求商户订单号&lt;/p&gt;
   * @return parentMerchantRequestNo
  **/

  public String getParentMerchantRequestNo() {
    return parentMerchantRequestNo;
  }

  public void setParentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
  }

  public RefundOrderRequestDTO parentMerchantRefundRequestNo(String parentMerchantRefundRequestNo) {
    this.parentMerchantRefundRequestNo = parentMerchantRefundRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;请求商户退款订单号&lt;/p&gt;
   * @return parentMerchantRefundRequestNo
  **/

  public String getParentMerchantRefundRequestNo() {
    return parentMerchantRefundRequestNo;
  }

  public void setParentMerchantRefundRequestNo(String parentMerchantRefundRequestNo) {
    this.parentMerchantRefundRequestNo = parentMerchantRefundRequestNo;
  }

  public RefundOrderRequestDTO refundAmount(BigDecimal refundAmount) {
    this.refundAmount = refundAmount;
    return this;
  }

   /**
   * &lt;p&gt;退款金额&lt;/p&gt;
   * @return refundAmount
  **/

  public BigDecimal getRefundAmount() {
    return refundAmount;
  }

  public void setRefundAmount(BigDecimal refundAmount) {
    this.refundAmount = refundAmount;
  }

  public RefundOrderRequestDTO complateNotifyUrl(String complateNotifyUrl) {
    this.complateNotifyUrl = complateNotifyUrl;
    return this;
  }

   /**
   * &lt;p&gt;通知地址&lt;/p&gt;
   * @return complateNotifyUrl
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getComplateNotifyUrl() {
    return complateNotifyUrl;
  }

  public void setComplateNotifyUrl(String complateNotifyUrl) {
    this.complateNotifyUrl = complateNotifyUrl;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    RefundOrderRequestDTO refundOrderRequestDTO = (RefundOrderRequestDTO) o;
    return ObjectUtils.equals(this.parentMerchantNo, refundOrderRequestDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, refundOrderRequestDTO.merchantNo) &&
    ObjectUtils.equals(this.merchantRequestNo, refundOrderRequestDTO.merchantRequestNo) &&
    ObjectUtils.equals(this.merchantRefundRequestNo, refundOrderRequestDTO.merchantRefundRequestNo) &&
    ObjectUtils.equals(this.parentMerchantRequestNo, refundOrderRequestDTO.parentMerchantRequestNo) &&
    ObjectUtils.equals(this.parentMerchantRefundRequestNo, refundOrderRequestDTO.parentMerchantRefundRequestNo) &&
    ObjectUtils.equals(this.refundAmount, refundOrderRequestDTO.refundAmount) &&
    ObjectUtils.equals(this.complateNotifyUrl, refundOrderRequestDTO.complateNotifyUrl);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(parentMerchantNo, merchantNo, merchantRequestNo, merchantRefundRequestNo, parentMerchantRequestNo, parentMerchantRefundRequestNo, refundAmount, complateNotifyUrl);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RefundOrderRequestDTO {\n");
    
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantRequestNo: ").append(toIndentedString(merchantRequestNo)).append("\n");
    sb.append("    merchantRefundRequestNo: ").append(toIndentedString(merchantRefundRequestNo)).append("\n");
    sb.append("    parentMerchantRequestNo: ").append(toIndentedString(parentMerchantRequestNo)).append("\n");
    sb.append("    parentMerchantRefundRequestNo: ").append(toIndentedString(parentMerchantRefundRequestNo)).append("\n");
    sb.append("    refundAmount: ").append(toIndentedString(refundAmount)).append("\n");
    sb.append("    complateNotifyUrl: ").append(toIndentedString(complateNotifyUrl)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

