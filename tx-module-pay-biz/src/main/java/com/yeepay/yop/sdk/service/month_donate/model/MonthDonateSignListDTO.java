/*
 * 公益-月捐
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.month_donate.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.month_donate.model.MonthDonateSignDTO;
import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 签约列表
 */
public class MonthDonateSignListDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;响应code码&lt;/p&gt;
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * &lt;p&gt;用户签约信息列表&lt;/p&gt;
   */
  @JsonProperty("monthDonateSignRecordList")
  private List<MonthDonateSignDTO> monthDonateSignRecordList = null;

  /**
   * &lt;p&gt;响应描述信息&lt;/p&gt;
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * &lt;p&gt;响应状态&lt;/p&gt;
   */
  @JsonProperty("status")
  private String status = null;

  public MonthDonateSignListDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * &lt;p&gt;响应code码&lt;/p&gt;
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public MonthDonateSignListDTO monthDonateSignRecordList(List<MonthDonateSignDTO> monthDonateSignRecordList) {
    this.monthDonateSignRecordList = monthDonateSignRecordList;
    return this;
  }

  public MonthDonateSignListDTO addMonthDonateSignRecordListItem(MonthDonateSignDTO monthDonateSignRecordListItem) {
    if (this.monthDonateSignRecordList == null) {
      this.monthDonateSignRecordList = new ArrayList<>();
    }
    this.monthDonateSignRecordList.add(monthDonateSignRecordListItem);
    return this;
  }

   /**
   * &lt;p&gt;用户签约信息列表&lt;/p&gt;
   * @return monthDonateSignRecordList
  **/

  public List<MonthDonateSignDTO> getMonthDonateSignRecordList() {
    return monthDonateSignRecordList;
  }

  public void setMonthDonateSignRecordList(List<MonthDonateSignDTO> monthDonateSignRecordList) {
    this.monthDonateSignRecordList = monthDonateSignRecordList;
  }

  public MonthDonateSignListDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * &lt;p&gt;响应描述信息&lt;/p&gt;
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public MonthDonateSignListDTO status(String status) {
    this.status = status;
    return this;
  }

   /**
   * &lt;p&gt;响应状态&lt;/p&gt;
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    MonthDonateSignListDTO monthDonateSignListDTO = (MonthDonateSignListDTO) o;
    return ObjectUtils.equals(this.code, monthDonateSignListDTO.code) &&
    ObjectUtils.equals(this.monthDonateSignRecordList, monthDonateSignListDTO.monthDonateSignRecordList) &&
    ObjectUtils.equals(this.message, monthDonateSignListDTO.message) &&
    ObjectUtils.equals(this.status, monthDonateSignListDTO.status);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, monthDonateSignRecordList, message, status);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MonthDonateSignListDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    monthDonateSignRecordList: ").append(toIndentedString(monthDonateSignRecordList)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

