/*
 * 供应商付款系统
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.supplier_remit;


import com.yeepay.yop.sdk.auth.req.AuthorizationReqRegistry;
import com.yeepay.yop.sdk.auth.req.DefaultAuthorizationReqRegistry;
import com.yeepay.yop.sdk.client.ClientParams;
import com.yeepay.yop.sdk.client.AbstractServiceClientBuilder;

public class SupplierRemitClientBuilder extends AbstractServiceClientBuilder<SupplierRemitClientBuilder, SupplierRemitClientImpl>{

    private static final AuthorizationReqRegistry REGISTRY;

    static {
        REGISTRY = new DefaultAuthorizationReqRegistry();
        REGISTRY.register("billDownload", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bill_download_v1_0", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
    }

    @Override
    protected AuthorizationReqRegistry authorizationReqRegistry() {
        return REGISTRY;
    }

    @Override
    protected SupplierRemitClientImpl build(ClientParams params) {
        return new SupplierRemitClientImpl(params);
    }

    /**
     * @return Create new instance of builder with all defaults set.
     */
    public static SupplierRemitClientBuilder builder(){
        return new SupplierRemitClientBuilder();
    }

}
