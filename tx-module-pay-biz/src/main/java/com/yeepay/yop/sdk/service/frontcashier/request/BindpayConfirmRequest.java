/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class BindpayConfirmRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String token;

    private String version;

    private String verifyCode;

    private String cardno;

    private String owner;

    private String idno;

    private String phoneNo;

    private String ypMobile;

    private String avlidDate;

    private String cvv2;

    private String idCardType;

    private String bankPWD;


    /**
     * Get token
     * @return token
     **/
    
    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    /**
     * Get version
     * @return version
     **/
    
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * Get verifyCode
     * @return verifyCode
     **/
    
    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    /**
     * Get cardno
     * @return cardno
     **/
    
    public String getCardno() {
        return cardno;
    }

    public void setCardno(String cardno) {
        this.cardno = cardno;
    }

    /**
     * Get owner
     * @return owner
     **/
    
    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    /**
     * Get idno
     * @return idno
     **/
    
    public String getIdno() {
        return idno;
    }

    public void setIdno(String idno) {
        this.idno = idno;
    }

    /**
     * Get phoneNo
     * @return phoneNo
     **/
    
    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    /**
     * Get ypMobile
     * @return ypMobile
     **/
    
    public String getYpMobile() {
        return ypMobile;
    }

    public void setYpMobile(String ypMobile) {
        this.ypMobile = ypMobile;
    }

    /**
     * Get avlidDate
     * @return avlidDate
     **/
    
    public String getAvlidDate() {
        return avlidDate;
    }

    public void setAvlidDate(String avlidDate) {
        this.avlidDate = avlidDate;
    }

    /**
     * Get cvv2
     * @return cvv2
     **/
    
    public String getCvv2() {
        return cvv2;
    }

    public void setCvv2(String cvv2) {
        this.cvv2 = cvv2;
    }

    /**
     * Get idCardType
     * @return idCardType
     **/
    
    public String getIdCardType() {
        return idCardType;
    }

    public void setIdCardType(String idCardType) {
        this.idCardType = idCardType;
    }

    /**
     * Get bankPWD
     * @return bankPWD
     **/
    
    public String getBankPWD() {
        return bankPWD;
    }

    public void setBankPWD(String bankPWD) {
        this.bankPWD = bankPWD;
    }

    @Override
    public String getOperationId() {
        return "bindpayConfirm";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
