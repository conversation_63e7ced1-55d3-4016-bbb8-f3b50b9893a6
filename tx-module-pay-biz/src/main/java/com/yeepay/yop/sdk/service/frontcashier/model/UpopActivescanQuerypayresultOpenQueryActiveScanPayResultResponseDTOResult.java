/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.frontcashier.model.UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult
 */
public class UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 错误码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 错误码描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 失败错误码
   */
  @JsonProperty("origCode")
  private String origCode = null;

  /**
   * 失败错误描述
   */
  @JsonProperty("origMessage")
  private String origMessage = null;

  /**
   * 付款订单号
   */
  @JsonProperty("payOrderNo")
  private String payOrderNo = null;

  /**
   * 实际付款金额
   */
  @JsonProperty("realTradeAmount")
  private BigDecimal realTradeAmount = null;

  /**
   * 总退款金额
   */
  @JsonProperty("totalRefundAmount")
  private BigDecimal totalRefundAmount = null;

  /**
   * 收款商户号
   */
  @JsonProperty("payeeMerchantId")
  private String payeeMerchantId = null;

  /**
   * 收款商户名称
   */
  @JsonProperty("payeeMerchantName")
  private String payeeMerchantName = null;

  /**
   * 收款方MCC
   */
  @JsonProperty("payeeMCC")
  private String payeeMCC = null;

  /**
   * 订单状态
   */
  @JsonProperty("status")
  private String status = null;

  /**
   * 营销信息
   */
  @JsonProperty("couponInfo")
  private String couponInfo = null;

  /**
   * 初始交易金额（原订单金额）
   */
  @JsonProperty("origTxnAmt")
  private BigDecimal origTxnAmt = null;

  /**
   * 申请退款总金额
   */
  @JsonProperty("totalRefundOrigTxnAmt")
  private BigDecimal totalRefundOrigTxnAmt = null;

  /**
   * 退款明细列表
   */
  @JsonProperty("refundTradeDetailList")
  private List<UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult> refundTradeDetailList = null;

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 错误码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 错误码描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult origCode(String origCode) {
    this.origCode = origCode;
    return this;
  }

   /**
   * 失败错误码
   * @return origCode
  **/

  public String getOrigCode() {
    return origCode;
  }

  public void setOrigCode(String origCode) {
    this.origCode = origCode;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult origMessage(String origMessage) {
    this.origMessage = origMessage;
    return this;
  }

   /**
   * 失败错误描述
   * @return origMessage
  **/

  public String getOrigMessage() {
    return origMessage;
  }

  public void setOrigMessage(String origMessage) {
    this.origMessage = origMessage;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult payOrderNo(String payOrderNo) {
    this.payOrderNo = payOrderNo;
    return this;
  }

   /**
   * 付款订单号
   * @return payOrderNo
  **/

  public String getPayOrderNo() {
    return payOrderNo;
  }

  public void setPayOrderNo(String payOrderNo) {
    this.payOrderNo = payOrderNo;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult realTradeAmount(BigDecimal realTradeAmount) {
    this.realTradeAmount = realTradeAmount;
    return this;
  }

   /**
   * 实际付款金额
   * @return realTradeAmount
  **/

  public BigDecimal getRealTradeAmount() {
    return realTradeAmount;
  }

  public void setRealTradeAmount(BigDecimal realTradeAmount) {
    this.realTradeAmount = realTradeAmount;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult totalRefundAmount(BigDecimal totalRefundAmount) {
    this.totalRefundAmount = totalRefundAmount;
    return this;
  }

   /**
   * 总退款金额
   * @return totalRefundAmount
  **/

  public BigDecimal getTotalRefundAmount() {
    return totalRefundAmount;
  }

  public void setTotalRefundAmount(BigDecimal totalRefundAmount) {
    this.totalRefundAmount = totalRefundAmount;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult payeeMerchantId(String payeeMerchantId) {
    this.payeeMerchantId = payeeMerchantId;
    return this;
  }

   /**
   * 收款商户号
   * @return payeeMerchantId
  **/

  public String getPayeeMerchantId() {
    return payeeMerchantId;
  }

  public void setPayeeMerchantId(String payeeMerchantId) {
    this.payeeMerchantId = payeeMerchantId;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult payeeMerchantName(String payeeMerchantName) {
    this.payeeMerchantName = payeeMerchantName;
    return this;
  }

   /**
   * 收款商户名称
   * @return payeeMerchantName
  **/

  public String getPayeeMerchantName() {
    return payeeMerchantName;
  }

  public void setPayeeMerchantName(String payeeMerchantName) {
    this.payeeMerchantName = payeeMerchantName;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult payeeMCC(String payeeMCC) {
    this.payeeMCC = payeeMCC;
    return this;
  }

   /**
   * 收款方MCC
   * @return payeeMCC
  **/

  public String getPayeeMCC() {
    return payeeMCC;
  }

  public void setPayeeMCC(String payeeMCC) {
    this.payeeMCC = payeeMCC;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult status(String status) {
    this.status = status;
    return this;
  }

   /**
   * 订单状态
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult couponInfo(String couponInfo) {
    this.couponInfo = couponInfo;
    return this;
  }

   /**
   * 营销信息
   * @return couponInfo
  **/

  public String getCouponInfo() {
    return couponInfo;
  }

  public void setCouponInfo(String couponInfo) {
    this.couponInfo = couponInfo;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult origTxnAmt(BigDecimal origTxnAmt) {
    this.origTxnAmt = origTxnAmt;
    return this;
  }

   /**
   * 初始交易金额（原订单金额）
   * @return origTxnAmt
  **/

  public BigDecimal getOrigTxnAmt() {
    return origTxnAmt;
  }

  public void setOrigTxnAmt(BigDecimal origTxnAmt) {
    this.origTxnAmt = origTxnAmt;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult totalRefundOrigTxnAmt(BigDecimal totalRefundOrigTxnAmt) {
    this.totalRefundOrigTxnAmt = totalRefundOrigTxnAmt;
    return this;
  }

   /**
   * 申请退款总金额
   * @return totalRefundOrigTxnAmt
  **/

  public BigDecimal getTotalRefundOrigTxnAmt() {
    return totalRefundOrigTxnAmt;
  }

  public void setTotalRefundOrigTxnAmt(BigDecimal totalRefundOrigTxnAmt) {
    this.totalRefundOrigTxnAmt = totalRefundOrigTxnAmt;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult refundTradeDetailList(List<UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult> refundTradeDetailList) {
    this.refundTradeDetailList = refundTradeDetailList;
    return this;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult addRefundTradeDetailListItem(UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult refundTradeDetailListItem) {
    if (this.refundTradeDetailList == null) {
      this.refundTradeDetailList = new ArrayList<>();
    }
    this.refundTradeDetailList.add(refundTradeDetailListItem);
    return this;
  }

   /**
   * 退款明细列表
   * @return refundTradeDetailList
  **/

  public List<UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult> getRefundTradeDetailList() {
    return refundTradeDetailList;
  }

  public void setRefundTradeDetailList(List<UpopActivescanQuerypayresultRefundTradeDetailResponseDTOResult> refundTradeDetailList) {
    this.refundTradeDetailList = refundTradeDetailList;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult = (UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult) o;
    return ObjectUtils.equals(this.code, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.message) &&
    ObjectUtils.equals(this.origCode, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.origCode) &&
    ObjectUtils.equals(this.origMessage, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.origMessage) &&
    ObjectUtils.equals(this.payOrderNo, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.payOrderNo) &&
    ObjectUtils.equals(this.realTradeAmount, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.realTradeAmount) &&
    ObjectUtils.equals(this.totalRefundAmount, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.totalRefundAmount) &&
    ObjectUtils.equals(this.payeeMerchantId, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.payeeMerchantId) &&
    ObjectUtils.equals(this.payeeMerchantName, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.payeeMerchantName) &&
    ObjectUtils.equals(this.payeeMCC, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.payeeMCC) &&
    ObjectUtils.equals(this.status, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.status) &&
    ObjectUtils.equals(this.couponInfo, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.couponInfo) &&
    ObjectUtils.equals(this.origTxnAmt, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.origTxnAmt) &&
    ObjectUtils.equals(this.totalRefundOrigTxnAmt, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.totalRefundOrigTxnAmt) &&
    ObjectUtils.equals(this.refundTradeDetailList, upopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult.refundTradeDetailList);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, origCode, origMessage, payOrderNo, realTradeAmount, totalRefundAmount, payeeMerchantId, payeeMerchantName, payeeMCC, status, couponInfo, origTxnAmt, totalRefundOrigTxnAmt, refundTradeDetailList);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UpopActivescanQuerypayresultOpenQueryActiveScanPayResultResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    origCode: ").append(toIndentedString(origCode)).append("\n");
    sb.append("    origMessage: ").append(toIndentedString(origMessage)).append("\n");
    sb.append("    payOrderNo: ").append(toIndentedString(payOrderNo)).append("\n");
    sb.append("    realTradeAmount: ").append(toIndentedString(realTradeAmount)).append("\n");
    sb.append("    totalRefundAmount: ").append(toIndentedString(totalRefundAmount)).append("\n");
    sb.append("    payeeMerchantId: ").append(toIndentedString(payeeMerchantId)).append("\n");
    sb.append("    payeeMerchantName: ").append(toIndentedString(payeeMerchantName)).append("\n");
    sb.append("    payeeMCC: ").append(toIndentedString(payeeMCC)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    couponInfo: ").append(toIndentedString(couponInfo)).append("\n");
    sb.append("    origTxnAmt: ").append(toIndentedString(origTxnAmt)).append("\n");
    sb.append("    totalRefundOrigTxnAmt: ").append(toIndentedString(totalRefundOrigTxnAmt)).append("\n");
    sb.append("    refundTradeDetailList: ").append(toIndentedString(refundTradeDetailList)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

