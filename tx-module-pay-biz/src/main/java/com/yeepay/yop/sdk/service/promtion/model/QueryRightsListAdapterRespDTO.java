/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.promtion.model.RightsDto;
import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 请修改我
 */
public class QueryRightsListAdapterRespDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 
   */
  @JsonProperty("rightsList")
  private List<RightsDto> rightsList = null;

  /**
   * 
   */
  @JsonProperty("message")
  private String message = null;

  public QueryRightsListAdapterRespDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * Get code
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public QueryRightsListAdapterRespDTO rightsList(List<RightsDto> rightsList) {
    this.rightsList = rightsList;
    return this;
  }

  public QueryRightsListAdapterRespDTO addRightsListItem(RightsDto rightsListItem) {
    if (this.rightsList == null) {
      this.rightsList = new ArrayList<>();
    }
    this.rightsList.add(rightsListItem);
    return this;
  }

   /**
   * Get rightsList
   * @return rightsList
  **/

  public List<RightsDto> getRightsList() {
    return rightsList;
  }

  public void setRightsList(List<RightsDto> rightsList) {
    this.rightsList = rightsList;
  }

  public QueryRightsListAdapterRespDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * Get message
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QueryRightsListAdapterRespDTO queryRightsListAdapterRespDTO = (QueryRightsListAdapterRespDTO) o;
    return ObjectUtils.equals(this.code, queryRightsListAdapterRespDTO.code) &&
    ObjectUtils.equals(this.rightsList, queryRightsListAdapterRespDTO.rightsList) &&
    ObjectUtils.equals(this.message, queryRightsListAdapterRespDTO.message);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, rightsList, message);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryRightsListAdapterRespDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    rightsList: ").append(toIndentedString(rightsList)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

