/*
 * API收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.nccashierapi;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.nccashierapi.request.*;
import com.yeepay.yop.sdk.service.nccashierapi.response.*;

public interface NccashierapiClient {

    /**
     * 聚合API收银台
     * &lt;p&gt;API收银台统一支付接口，支持主扫支付(微信/支付宝/京东)、被扫支付(微信/支付宝/京东)、微信公众号、小程序支付、微信SDK支付&lt;/p&gt;
     * @return ApiPayResponse
     * @throws YopClientException if fails to make API call
     */
    ApiPayResponse apiPay(ApiPayRequest request) throws YopClientException;

    /**
     * 绑卡请求结果查询
     * 
     * @return BindCardQueryResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BindCardQueryResponse bindCardQuery(BindCardQueryRequest request) throws YopClientException;

    /**
     * 绑卡请求
     * 
     * @return BindCardRequestResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    BindCardRequestResponse bindCardRequest(BindCardRequestRequest request) throws YopClientException;

    /**
     * 绑卡请求
     * 
     * @return BindcardCreateRequestResponse
     * @throws YopClientException if fails to make API call
     */
    BindcardCreateRequestResponse bindcardCreateRequest(BindcardCreateRequestRequest request) throws YopClientException;

    /**
     * 绑卡请求结果查询
     * 
     * @return BindcardQueryRequestResponse
     * @throws YopClientException if fails to make API call
     */
    BindcardQueryRequestResponse bindcardQueryRequest(BindcardQueryRequestRequest request) throws YopClientException;

    /**
     * 关闭订单
     * 
     * @return OrderCloseResponse
     * @throws YopClientException if fails to make API call
     */
    OrderCloseResponse orderClose(OrderCloseRequest request) throws YopClientException;


    /**
     * Shuts down this client object, releasing any resources that might be held open. This is an optional method, and
     * callers are not expected to call it, but can if they want to explicitly release any open resources. Once a client
     * has been shutdown, it should not be used to make any more requests.
     */
    void shutdown();
}
