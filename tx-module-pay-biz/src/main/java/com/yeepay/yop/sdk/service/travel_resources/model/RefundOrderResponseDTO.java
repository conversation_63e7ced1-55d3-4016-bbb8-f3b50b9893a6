/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 退款订单返回参数
 */
public class RefundOrderResponseDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;订单状态需要关注status&lt;/p&gt;
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * &lt;pre&gt;请求方商编&lt;/pre&gt;
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * &lt;p&gt;请求方订单号&lt;/p&gt;
   */
  @JsonProperty("parentMerchantRequestNo")
  private String parentMerchantRequestNo = null;

  /**
   * &lt;p&gt;请求方退款订单号&lt;/p&gt;
   */
  @JsonProperty("parentMerchantRefundRequestNo")
  private String parentMerchantRefundRequestNo = null;

  /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;p&gt;业务商户订单号&lt;/p&gt;
   */
  @JsonProperty("merchantRequestNo")
  private String merchantRequestNo = null;

  /**
   * &lt;p&gt;业务商户退款订单号&lt;/p&gt;
   */
  @JsonProperty("merchantRefundRequestNo")
  private String merchantRefundRequestNo = null;

  /**
   * &lt;pre&gt;采购方在供应方的平台id&lt;/pre&gt;
   */
  @JsonProperty("purchasePlatformId")
  private String purchasePlatformId = null;

  /**
   * &lt;p&gt;易宝系统唯一订单号&lt;/p&gt;
   */
  @JsonProperty("systemOrderNo")
  private String systemOrderNo = null;

  /**
   * &lt;p&gt;退款金额&lt;/p&gt;
   */
  @JsonProperty("refundAmount")
  private BigDecimal refundAmount = null;

  /**
   * &lt;p&gt;退款状态&lt;/p&gt; &lt;pre&gt;0：处理中&lt;/pre&gt; &lt;pre&gt;1：退款成功&lt;/pre&gt; &lt;pre&gt;2：退款失败&lt;/pre&gt;
   */
  @JsonProperty("status")
  private String status = null;

  /**
   * &lt;p&gt;退款成功时间&lt;/p&gt;
   */
  @JsonProperty("refundSuccessTime")
  private String refundSuccessTime = null;

  /**
   * &lt;p&gt;在字段用来表示通知类型是交易还是退款。&lt;br /&gt;只有在异步通知结果里该参数才有值&lt;/p&gt;
   */
  @JsonProperty("trxType")
  private String trxType = null;

  public RefundOrderResponseDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * &lt;p&gt;订单状态需要关注status&lt;/p&gt;
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public RefundOrderResponseDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * Get message
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public RefundOrderResponseDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * &lt;pre&gt;请求方商编&lt;/pre&gt;
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public RefundOrderResponseDTO parentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;请求方订单号&lt;/p&gt;
   * @return parentMerchantRequestNo
  **/

  public String getParentMerchantRequestNo() {
    return parentMerchantRequestNo;
  }

  public void setParentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
  }

  public RefundOrderResponseDTO parentMerchantRefundRequestNo(String parentMerchantRefundRequestNo) {
    this.parentMerchantRefundRequestNo = parentMerchantRefundRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;请求方退款订单号&lt;/p&gt;
   * @return parentMerchantRefundRequestNo
  **/

  public String getParentMerchantRefundRequestNo() {
    return parentMerchantRefundRequestNo;
  }

  public void setParentMerchantRefundRequestNo(String parentMerchantRefundRequestNo) {
    this.parentMerchantRefundRequestNo = parentMerchantRefundRequestNo;
  }

  public RefundOrderResponseDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public RefundOrderResponseDTO merchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;业务商户订单号&lt;/p&gt;
   * @return merchantRequestNo
  **/

  public String getMerchantRequestNo() {
    return merchantRequestNo;
  }

  public void setMerchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
  }

  public RefundOrderResponseDTO merchantRefundRequestNo(String merchantRefundRequestNo) {
    this.merchantRefundRequestNo = merchantRefundRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;业务商户退款订单号&lt;/p&gt;
   * @return merchantRefundRequestNo
  **/

  public String getMerchantRefundRequestNo() {
    return merchantRefundRequestNo;
  }

  public void setMerchantRefundRequestNo(String merchantRefundRequestNo) {
    this.merchantRefundRequestNo = merchantRefundRequestNo;
  }

  public RefundOrderResponseDTO purchasePlatformId(String purchasePlatformId) {
    this.purchasePlatformId = purchasePlatformId;
    return this;
  }

   /**
   * &lt;pre&gt;采购方在供应方的平台id&lt;/pre&gt;
   * @return purchasePlatformId
  **/

  public String getPurchasePlatformId() {
    return purchasePlatformId;
  }

  public void setPurchasePlatformId(String purchasePlatformId) {
    this.purchasePlatformId = purchasePlatformId;
  }

  public RefundOrderResponseDTO systemOrderNo(String systemOrderNo) {
    this.systemOrderNo = systemOrderNo;
    return this;
  }

   /**
   * &lt;p&gt;易宝系统唯一订单号&lt;/p&gt;
   * @return systemOrderNo
  **/

  public String getSystemOrderNo() {
    return systemOrderNo;
  }

  public void setSystemOrderNo(String systemOrderNo) {
    this.systemOrderNo = systemOrderNo;
  }

  public RefundOrderResponseDTO refundAmount(BigDecimal refundAmount) {
    this.refundAmount = refundAmount;
    return this;
  }

   /**
   * &lt;p&gt;退款金额&lt;/p&gt;
   * @return refundAmount
  **/

  public BigDecimal getRefundAmount() {
    return refundAmount;
  }

  public void setRefundAmount(BigDecimal refundAmount) {
    this.refundAmount = refundAmount;
  }

  public RefundOrderResponseDTO status(String status) {
    this.status = status;
    return this;
  }

   /**
   * &lt;p&gt;退款状态&lt;/p&gt; &lt;pre&gt;0：处理中&lt;/pre&gt; &lt;pre&gt;1：退款成功&lt;/pre&gt; &lt;pre&gt;2：退款失败&lt;/pre&gt;
   * @return status
  **/

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public RefundOrderResponseDTO refundSuccessTime(String refundSuccessTime) {
    this.refundSuccessTime = refundSuccessTime;
    return this;
  }

   /**
   * &lt;p&gt;退款成功时间&lt;/p&gt;
   * @return refundSuccessTime
  **/

  public String getRefundSuccessTime() {
    return refundSuccessTime;
  }

  public void setRefundSuccessTime(String refundSuccessTime) {
    this.refundSuccessTime = refundSuccessTime;
  }

  public RefundOrderResponseDTO trxType(String trxType) {
    this.trxType = trxType;
    return this;
  }

   /**
   * &lt;p&gt;在字段用来表示通知类型是交易还是退款。&lt;br /&gt;只有在异步通知结果里该参数才有值&lt;/p&gt;
   * @return trxType
  **/

  public String getTrxType() {
    return trxType;
  }

  public void setTrxType(String trxType) {
    this.trxType = trxType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    RefundOrderResponseDTO refundOrderResponseDTO = (RefundOrderResponseDTO) o;
    return ObjectUtils.equals(this.code, refundOrderResponseDTO.code) &&
    ObjectUtils.equals(this.message, refundOrderResponseDTO.message) &&
    ObjectUtils.equals(this.parentMerchantNo, refundOrderResponseDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.parentMerchantRequestNo, refundOrderResponseDTO.parentMerchantRequestNo) &&
    ObjectUtils.equals(this.parentMerchantRefundRequestNo, refundOrderResponseDTO.parentMerchantRefundRequestNo) &&
    ObjectUtils.equals(this.merchantNo, refundOrderResponseDTO.merchantNo) &&
    ObjectUtils.equals(this.merchantRequestNo, refundOrderResponseDTO.merchantRequestNo) &&
    ObjectUtils.equals(this.merchantRefundRequestNo, refundOrderResponseDTO.merchantRefundRequestNo) &&
    ObjectUtils.equals(this.purchasePlatformId, refundOrderResponseDTO.purchasePlatformId) &&
    ObjectUtils.equals(this.systemOrderNo, refundOrderResponseDTO.systemOrderNo) &&
    ObjectUtils.equals(this.refundAmount, refundOrderResponseDTO.refundAmount) &&
    ObjectUtils.equals(this.status, refundOrderResponseDTO.status) &&
    ObjectUtils.equals(this.refundSuccessTime, refundOrderResponseDTO.refundSuccessTime) &&
    ObjectUtils.equals(this.trxType, refundOrderResponseDTO.trxType);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, parentMerchantNo, parentMerchantRequestNo, parentMerchantRefundRequestNo, merchantNo, merchantRequestNo, merchantRefundRequestNo, purchasePlatformId, systemOrderNo, refundAmount, status, refundSuccessTime, trxType);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RefundOrderResponseDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    parentMerchantRequestNo: ").append(toIndentedString(parentMerchantRequestNo)).append("\n");
    sb.append("    parentMerchantRefundRequestNo: ").append(toIndentedString(parentMerchantRefundRequestNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantRequestNo: ").append(toIndentedString(merchantRequestNo)).append("\n");
    sb.append("    merchantRefundRequestNo: ").append(toIndentedString(merchantRefundRequestNo)).append("\n");
    sb.append("    purchasePlatformId: ").append(toIndentedString(purchasePlatformId)).append("\n");
    sb.append("    systemOrderNo: ").append(toIndentedString(systemOrderNo)).append("\n");
    sb.append("    refundAmount: ").append(toIndentedString(refundAmount)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    refundSuccessTime: ").append(toIndentedString(refundSuccessTime)).append("\n");
    sb.append("    trxType: ").append(toIndentedString(trxType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

