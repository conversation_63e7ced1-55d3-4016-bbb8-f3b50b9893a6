/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.frontcashier.request.*;
import com.yeepay.yop.sdk.service.frontcashier.response.*;

public interface FrontcashierClient {

    /**
     * 到账信息确认接口
     *  
     * @return AccountConfirmResponse
     * @throws YopClientException if fails to make API call
     */
    AccountConfirmResponse accountConfirm(AccountConfirmRequest request) throws YopClientException;

    /**
     * 到账信息确认接口
     *  
     * @return AccountInfoConfirmResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    AccountInfoConfirmResponse accountInfoConfirm(AccountInfoConfirmRequest request) throws YopClientException;

    /**
     * 银行转账支付
     * 
     * @return BankTransferPayResponse
     * @throws YopClientException if fails to make API call
     */
    BankTransferPayResponse bankTransferPay(BankTransferPayRequest request) throws YopClientException;

    /**
     * 银行转账查询
     * 
     * @return BankTransferQueryResponse
     * @throws YopClientException if fails to make API call
     */
    BankTransferQueryResponse bankTransferQuery(BankTransferQueryRequest request) throws YopClientException;

    /**
     * 查询签约/绑卡列表
     * &lt;p&gt;查询用户所绑定的银行卡&lt;/p&gt;
     * @return BindcardBindcardlistResponse
     * @throws YopClientException if fails to make API call
     */
    BindcardBindcardlistResponse bindcardBindcardlist(BindcardBindcardlistRequest request) throws YopClientException;

    /**
     * 绑卡-短验确认
     * &lt;p&gt;该接口提供鉴权绑卡请求短信验证&lt;/p&gt;
     * @return BindcardConfirmResponse
     * @throws YopClientException if fails to make API call
     */
    BindcardConfirmResponse bindcardConfirm(BindcardConfirmRequest request) throws YopClientException;

    /**
     * 中台绑卡-短验确认
     * &lt;p&gt;该接口提供鉴权绑卡请求短信验证&lt;/p&gt;
     * @return BindcardConfirmV2Response
     * @throws YopClientException if fails to make API call
     */
    BindcardConfirmV2Response bindcardConfirmV2(BindcardConfirmV2Request request) throws YopClientException;

    /**
     * 银行卡卡bin识别
     * 
     * @return BindcardGetcardbinResponse
     * @throws YopClientException if fails to make API call
     */
    BindcardGetcardbinResponse bindcardGetcardbin(BindcardGetcardbinRequest request) throws YopClientException;

    /**
     * 付款方签约
     * 付款方签约
     * @return BindcardPayerrequestResponse
     * @throws YopClientException if fails to make API call
     */
    BindcardPayerrequestResponse bindcardPayerrequest(BindcardPayerrequestRequest request) throws YopClientException;

    /**
     * 查询签约/绑卡请求
     * 查询签约/绑卡请求
     * @return BindcardQueryorderResponse
     * @throws YopClientException if fails to make API call
     */
    BindcardQueryorderResponse bindcardQueryorder(BindcardQueryorderRequest request) throws YopClientException;

    /**
     * 签约/绑卡订单查询
     * 
     * @return BindcardQueryorderinfoResponse
     * @throws YopClientException if fails to make API call
     */
    BindcardQueryorderinfoResponse bindcardQueryorderinfo(BindcardQueryorderinfoRequest request) throws YopClientException;

    /**
     * 绑卡-绑卡请求
     * &lt;p&gt;该接口提供绑卡请求,该接口请求成功后需调求短验确认接口完成整个绑卡动作&lt;/p&gt;
     * @return BindcardRequestResponse
     * @throws YopClientException if fails to make API call
     */
    BindcardRequestResponse bindcardRequest(BindcardRequestRequest request) throws YopClientException;

    /**
     * 中台绑卡-绑卡请求
     * 该接口提供绑卡请求,该接口请求成功后需调求短验确认接口完成整个绑卡动作&lt;/p&gt;
     * @return BindcardRequestV2Response
     * @throws YopClientException if fails to make API call
     */
    BindcardRequestV2Response bindcardRequestV2(BindcardRequestV2Request request) throws YopClientException;

    /**
     * 中台绑卡-绑卡请求
     * &lt;p&gt;该接口提供绑卡请求,该接口请求成功后需调求短验确认接口完成整个绑卡动作&lt;/p&gt;
     * @return BindcardRequestV21Response
     * @throws YopClientException if fails to make API call
     */
    BindcardRequestV21Response bindcardRequestV2_1(BindcardRequestV21Request request) throws YopClientException;

    /**
     * 绑卡-短验重发
     * &lt;p&gt;当用户没有拿到短信验证码时调用该接口&lt;/p&gt;
     * @return BindcardResendsmsResponse
     * @throws YopClientException if fails to make API call
     */
    BindcardResendsmsResponse bindcardResendsms(BindcardResendsmsRequest request) throws YopClientException;

    /**
     * 中台绑卡-短验重发
     * &lt;p&gt;当用户没有拿到短信验证码时调用该接口&lt;/p&gt;
     * @return BindcardResendsmsV2Response
     * @throws YopClientException if fails to make API call
     */
    BindcardResendsmsV2Response bindcardResendsmsV2(BindcardResendsmsV2Request request) throws YopClientException;

    /**
     * 解绑银行卡
     * 解除用户和银行卡的绑卡关系
     * @return BindcardUnbindcardResponse
     * @throws YopClientException if fails to make API call
     */
    BindcardUnbindcardResponse bindcardUnbindcard(BindcardUnbindcardRequest request) throws YopClientException;

    /**
     * 绑卡支付-确认支付
     * &lt;p&gt;API收银台，绑卡支付，确认支付&lt;/p&gt;
     * @return BindpayConfirmResponse
     * @throws YopClientException if fails to make API call
     */
    BindpayConfirmResponse bindpayConfirm(BindpayConfirmRequest request) throws YopClientException;

    /**
     * 绑卡支付-支付下单
     * 
     * @return BindpayRequestResponse
     * @throws YopClientException if fails to make API call
     */
    BindpayRequestResponse bindpayRequest(BindpayRequestRequest request) throws YopClientException;

    /**
     * 绑卡支付-请求发短验
     * &lt;p&gt;API收银台，绑卡支付，请求发送验证码&lt;/p&gt;
     * @return BindpaySendsmsResponse
     * @throws YopClientException if fails to make API call
     */
    BindpaySendsmsResponse bindpaySendsms(BindpaySendsmsRequest request) throws YopClientException;

    /**
     * 查询本人银行卡并签约绑卡
     * 用于查询用户本人在指定银行开立的全部银行卡（可限定卡类型），并由商户选择一张银行卡在银行侧签约开通快捷支付，并完成易宝侧的绑卡。商户通过该接口获取用于查询银行卡并签约的银行页面地址等用于前端页面跳转相关的参数信息
     * @return FastbindcardRequestResponse
     * @throws YopClientException if fails to make API call
     */
    FastbindcardRequestResponse fastbindcardRequest(FastbindcardRequestRequest request) throws YopClientException;

    /**
     * 银行卡bin识别
     * 银行卡bin识别
     * @return GetcardbinResponse
     * @throws YopClientException if fails to make API call
     */
    GetcardbinResponse getcardbin(GetcardbinRequest request) throws YopClientException;

    /**
     * 付款方主扫下单
     * 付款方主扫下单
     * @return UpopActivescanPayResponse
     * @throws YopClientException if fails to make API call
     */
    UpopActivescanPayResponse upopActivescanPay(UpopActivescanPayRequest request) throws YopClientException;

    /**
     * 【NOP】银联主扫查询优惠信息
     * 
     * @return UpopActivescanQuerycouponResponse
     * @throws YopClientException if fails to make API call
     */
    UpopActivescanQuerycouponResponse upopActivescanQuerycoupon(UpopActivescanQuerycouponRequest request) throws YopClientException;

    /**
     * 收款方订单信息查询
     * 收款方订单信息查询
     * @return UpopActivescanQuerypayeeorderResponse
     * @throws YopClientException if fails to make API call
     */
    UpopActivescanQuerypayeeorderResponse upopActivescanQuerypayeeorder(UpopActivescanQuerypayeeorderRequest request) throws YopClientException;

    /**
     * 付款订单状态查询
     * 付款订单状态查询
     * @return UpopActivescanQuerypayresultResponse
     * @throws YopClientException if fails to make API call
     */
    UpopActivescanQuerypayresultResponse upopActivescanQuerypayresult(UpopActivescanQuerypayresultRequest request) throws YopClientException;

    /**
     * 付款方被扫申码
     * 付款方被扫申码
     * @return UpopPassivescanBindQrcodeResponse
     * @throws YopClientException if fails to make API call
     */
    UpopPassivescanBindQrcodeResponse upopPassivescanBindQrcode(UpopPassivescanBindQrcodeRequest request) throws YopClientException;

    /**
     * 付款方验密回调
     * 付款方验密回调
     * @return UpopPassivescanValidateResponse
     * @throws YopClientException if fails to make API call
     */
    UpopPassivescanValidateResponse upopPassivescanValidate(UpopPassivescanValidateRequest request) throws YopClientException;

    /**
     * 一键支付-二次支付下单
     * API收银台-一键支付-二次支付下单
     * @return YjzfBindpayrequestResponse
     * @throws YopClientException if fails to make API call
     */
    YjzfBindpayrequestResponse yjzfBindpayrequest(YjzfBindpayrequestRequest request) throws YopClientException;

    /**
     * 一键支付-首次支付下单
     * API收银台-一键支付-首次支付下单
     * @return YjzfFirstpayrequestResponse
     * @throws YopClientException if fails to make API call
     */
    YjzfFirstpayrequestResponse yjzfFirstpayrequest(YjzfFirstpayrequestRequest request) throws YopClientException;

    /**
     * 一键支付-确认支付
     * API收银台-一键支付-确认支付
     * @return YjzfPaymentconfirmResponse
     * @throws YopClientException if fails to make API call
     */
    YjzfPaymentconfirmResponse yjzfPaymentconfirm(YjzfPaymentconfirmRequest request) throws YopClientException;

    /**
     * 一键支付-请求发验证码
     * API收银台-一键支付-请求发验证码
     * @return YjzfSendsmsResponse
     * @throws YopClientException if fails to make API call
     */
    YjzfSendsmsResponse yjzfSendsms(YjzfSendsmsRequest request) throws YopClientException;


    /**
     * Shuts down this client object, releasing any resources that might be held open. This is an optional method, and
     * callers are not expected to call it, but can if they want to explicitly release any open resources. Once a client
     * has been shutdown, it should not be used to make any more requests.
     */
    void shutdown();
}
