/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.promtion.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class RightsQueryConsumeRecordsRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String operatedMerchantNo;

    private String userNo;

    private Integer pageNo;

    private Integer pageSize;

    private String consumeTimeEnd;

    private String consumeTimeStart;

    private String merchantNo;


    /**
     * Get operatedMerchantNo
     * @return operatedMerchantNo
     **/
    
    public String getOperatedMerchantNo() {
        return operatedMerchantNo;
    }

    public void setOperatedMerchantNo(String operatedMerchantNo) {
        this.operatedMerchantNo = operatedMerchantNo;
    }

    /**
     * Get userNo
     * @return userNo
     **/
    
    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    /**
     * Get pageNo
     * @return pageNo
     **/
    
    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    /**
     * Get pageSize
     * @return pageSize
     **/
    
    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * Get consumeTimeEnd
     * @return consumeTimeEnd
     **/
    
    public String getConsumeTimeEnd() {
        return consumeTimeEnd;
    }

    public void setConsumeTimeEnd(String consumeTimeEnd) {
        this.consumeTimeEnd = consumeTimeEnd;
    }

    /**
     * Get consumeTimeStart
     * @return consumeTimeStart
     **/
    
    public String getConsumeTimeStart() {
        return consumeTimeStart;
    }

    public void setConsumeTimeStart(String consumeTimeStart) {
        this.consumeTimeStart = consumeTimeStart;
    }

    /**
     * Get merchantNo
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    @Override
    public String getOperationId() {
        return "rightsQueryConsumeRecords";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
