/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class TradeAutoDeductionQueryRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String parentMerchantNo;

    private String merchantNo;

    private String requestNo;


    /**
     * Get parentMerchantNo
     * @return parentMerchantNo
     **/
    
    public String getParentMerchantNo() {
        return parentMerchantNo;
    }

    public void setParentMerchantNo(String parentMerchantNo) {
        this.parentMerchantNo = parentMerchantNo;
    }

    /**
     * Get merchantNo
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    /**
     * Get requestNo
     * @return requestNo
     **/
    
    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    @Override
    public String getOperationId() {
        return "tradeAutoDeductionQuery";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
