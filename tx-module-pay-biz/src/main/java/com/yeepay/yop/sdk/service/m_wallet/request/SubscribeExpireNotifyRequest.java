/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet.request;

import com.yeepay.yop.sdk.service.m_wallet.model.SubscribeNotifyRequestDTO;
public class SubscribeExpireNotifyRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private SubscribeNotifyRequestDTO body;


    /**
     * Get body
     * @return body
     **/
    
    public SubscribeNotifyRequestDTO getBody() {
        return body;
    }

    public void setBody(SubscribeNotifyRequestDTO body) {
        this.body = body;
    }

    @Override
    public String getOperationId() {
        return "subscribe_expire_notify";
    }
}
