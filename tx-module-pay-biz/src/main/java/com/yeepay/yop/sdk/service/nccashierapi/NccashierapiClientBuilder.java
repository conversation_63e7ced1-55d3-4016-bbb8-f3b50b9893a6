/*
 * API收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.nccashierapi;


import com.yeepay.yop.sdk.auth.req.AuthorizationReqRegistry;
import com.yeepay.yop.sdk.auth.req.DefaultAuthorizationReqRegistry;
import com.yeepay.yop.sdk.client.ClientParams;
import com.yeepay.yop.sdk.client.AbstractServiceClientBuilder;

public class NccashierapiClientBuilder extends AbstractServiceClientBuilder<NccashierapiClientBuilder, NccashierapiClientImpl>{

    private static final AuthorizationReqRegistry REGISTRY;

    static {
        REGISTRY = new DefaultAuthorizationReqRegistry();
        REGISTRY.register("apiPay", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindCardQuery", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindCardRequest", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardCreateRequest", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("bindcardQueryRequest", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
        REGISTRY.register("orderClose", "YOP-SM2-SM3,YOP-RSA2048-SHA256");
    }

    @Override
    protected AuthorizationReqRegistry authorizationReqRegistry() {
        return REGISTRY;
    }

    @Override
    protected NccashierapiClientImpl build(ClientParams params) {
        return new NccashierapiClientImpl(params);
    }

    /**
     * @return Create new instance of builder with all defaults set.
     */
    public static NccashierapiClientBuilder builder(){
        return new NccashierapiClientBuilder();
    }

}
