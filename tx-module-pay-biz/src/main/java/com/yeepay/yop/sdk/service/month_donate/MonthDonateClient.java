/*
 * 公益-月捐
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.month_donate;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.month_donate.request.*;
import com.yeepay.yop.sdk.service.month_donate.response.*;

public interface MonthDonateClient {

    /**
     * 修改月捐
     * 用户修改月捐接口
     * @return ChangeResponse
     * @throws YopClientException if fails to make API call
     */
    ChangeResponse change(ChangeRequest request) throws YopClientException;

    /**
     * 关闭月捐
     * 开放客户关闭月捐
     * @return CloseResponse
     * @throws YopClientException if fails to make API call
     */
    CloseResponse close(CloseRequest request) throws YopClientException;

    /**
     * 创建易宝公益账号
     * 用于创建易宝公益账号
     * @return CreateUserResponse
     * @throws YopClientException if fails to make API call
     */
    CreateUserResponse createUser(CreateUserRequest request) throws YopClientException;

    /**
     * 开通月捐
     * 外放客户开通月捐接口，支持微信公众号、微信小程序、绑定银行卡
     * @return OpenResponse
     * @throws YopClientException if fails to make API call
     */
    OpenResponse open(OpenRequest request) throws YopClientException;

    /**
     * 查询月捐订单
     * 查询月捐订单
     * @return QueryOrderInfoResponse
     * @throws YopClientException if fails to make API call
     */
    QueryOrderInfoResponse queryOrderInfo(QueryOrderInfoRequest request) throws YopClientException;

    /**
     * 查询签约信息
     * 查询签约信息
     * @return QuerySignInfoResponse
     * @throws YopClientException if fails to make API call
     */
    QuerySignInfoResponse querySignInfo(QuerySignInfoRequest request) throws YopClientException;

    /**
     * 查询用户月捐信息
     * 查询用户月捐信息
     * @return QueryUserInfoResponse
     * @throws YopClientException if fails to make API call
     */
    QueryUserInfoResponse queryUserInfo(QueryUserInfoRequest request) throws YopClientException;


    /**
     * Shuts down this client object, releasing any resources that might be held open. This is an optional method, and
     * callers are not expected to call it, but can if they want to explicitly release any open resources. Once a client
     * has been shutdown, it should not be used to make any more requests.
     */
    void shutdown();
}
