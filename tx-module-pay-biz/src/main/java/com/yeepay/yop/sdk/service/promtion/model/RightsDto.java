/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 请修改我
 */
public class RightsDto implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 
   */
  @JsonProperty("endEffectDate")
  private String endEffectDate = null;

  /**
   * 
   */
  @JsonProperty("rightsCode")
  private String rightsCode = null;

  /**
   * 
   */
  @JsonProperty("rightsTitle")
  private String rightsTitle = null;

  /**
   * 
   */
  @JsonProperty("brandNo")
  private String brandNo = null;

  /**
   * 
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  public RightsDto endEffectDate(String endEffectDate) {
    this.endEffectDate = endEffectDate;
    return this;
  }

   /**
   * Get endEffectDate
   * @return endEffectDate
  **/

  public String getEndEffectDate() {
    return endEffectDate;
  }

  public void setEndEffectDate(String endEffectDate) {
    this.endEffectDate = endEffectDate;
  }

  public RightsDto rightsCode(String rightsCode) {
    this.rightsCode = rightsCode;
    return this;
  }

   /**
   * Get rightsCode
   * @return rightsCode
  **/

  public String getRightsCode() {
    return rightsCode;
  }

  public void setRightsCode(String rightsCode) {
    this.rightsCode = rightsCode;
  }

  public RightsDto rightsTitle(String rightsTitle) {
    this.rightsTitle = rightsTitle;
    return this;
  }

   /**
   * Get rightsTitle
   * @return rightsTitle
  **/

  public String getRightsTitle() {
    return rightsTitle;
  }

  public void setRightsTitle(String rightsTitle) {
    this.rightsTitle = rightsTitle;
  }

  public RightsDto brandNo(String brandNo) {
    this.brandNo = brandNo;
    return this;
  }

   /**
   * Get brandNo
   * @return brandNo
  **/

  public String getBrandNo() {
    return brandNo;
  }

  public void setBrandNo(String brandNo) {
    this.brandNo = brandNo;
  }

  public RightsDto merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * Get merchantNo
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    RightsDto rightsDto = (RightsDto) o;
    return ObjectUtils.equals(this.endEffectDate, rightsDto.endEffectDate) &&
    ObjectUtils.equals(this.rightsCode, rightsDto.rightsCode) &&
    ObjectUtils.equals(this.rightsTitle, rightsDto.rightsTitle) &&
    ObjectUtils.equals(this.brandNo, rightsDto.brandNo) &&
    ObjectUtils.equals(this.merchantNo, rightsDto.merchantNo);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(endEffectDate, rightsCode, rightsTitle, brandNo, merchantNo);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RightsDto {\n");
    
    sb.append("    endEffectDate: ").append(toIndentedString(endEffectDate)).append("\n");
    sb.append("    rightsCode: ").append(toIndentedString(rightsCode)).append("\n");
    sb.append("    rightsTitle: ").append(toIndentedString(rightsTitle)).append("\n");
    sb.append("    brandNo: ").append(toIndentedString(brandNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

