/*
 * 供应商付款系统
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.supplier_remit;

import com.yeepay.yop.sdk.client.*;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.HttpResponseAnalyzerSupport;
import com.yeepay.yop.sdk.http.HttpResponseHandler;
import com.yeepay.yop.sdk.http.handler.DefaultHttpResponseHandler;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.model.yos.YosDownloadResponse;
import com.yeepay.yop.sdk.service.supplier_remit.request.*;

public class SupplierRemitClientImpl implements SupplierRemitClient {

    private final ClientHandler clientHandler;

    SupplierRemitClientImpl(ClientParams clientParams) {
        this.clientHandler = new ClientHandlerImpl(new ClientHandlerParams().withClientParams(clientParams));
    }

    @Override
    public YosDownloadResponse billDownload(BillDownloadRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BillDownloadRequest> requestMarshaller = BillDownloadRequestMarshaller.getInstance();
        HttpResponseHandler<YosDownloadResponse> responseHandler =
                new DefaultHttpResponseHandler<YosDownloadResponse>(YosDownloadResponse.class,
                        HttpResponseAnalyzerSupport.getYosDownloadAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BillDownloadRequest, YosDownloadResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public YosDownloadResponse bill_download_v1_0(BillDownloadV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BillDownloadV10Request> requestMarshaller = BillDownloadV10RequestMarshaller.getInstance();
        HttpResponseHandler<YosDownloadResponse> responseHandler =
                new DefaultHttpResponseHandler<YosDownloadResponse>(YosDownloadResponse.class,
                        HttpResponseAnalyzerSupport.getYosDownloadAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BillDownloadV10Request, YosDownloadResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }

    @Override
    public void shutdown() {
        clientHandler.shutdown();
    }

}
