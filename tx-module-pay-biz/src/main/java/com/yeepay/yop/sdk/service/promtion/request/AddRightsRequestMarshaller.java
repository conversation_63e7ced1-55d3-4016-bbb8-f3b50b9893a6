/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.promtion.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class AddRightsRequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<AddRightsRequest> {
    private final String serviceName = "Promtion";

    private final String resourcePath = "/rest/v1.0/promtion/add-rights";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.POST;


    @Override
    public Request<AddRightsRequest> marshall(AddRightsRequest request) {
        Request<AddRightsRequest> internalRequest = new DefaultRequest<AddRightsRequest>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getMerchantNo() != null) {
            internalRequest.addParameter("merchantNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantNo(), "String"));
        }
        if (request.getRightsCode() != null) {
            internalRequest.addParameter("rightsCode", PrimitiveMarshallerUtils.marshalling(request.getRightsCode(), "String"));
        }
        if (request.getStartEffectDate() != null) {
            internalRequest.addParameter("startEffectDate", PrimitiveMarshallerUtils.marshalling(request.getStartEffectDate(), "String"));
        }
        if (request.getEndEffectDate() != null) {
            internalRequest.addParameter("endEffectDate", PrimitiveMarshallerUtils.marshalling(request.getEndEffectDate(), "String"));
        }
        if (request.getBrandNo() != null) {
            internalRequest.addParameter("brandNo", PrimitiveMarshallerUtils.marshalling(request.getBrandNo(), "String"));
        }
        if (request.getTitle() != null) {
            internalRequest.addParameter("title", PrimitiveMarshallerUtils.marshalling(request.getTitle(), "String"));
        }
        if (request.getRuleDesc() != null) {
            internalRequest.addParameter("ruleDesc", PrimitiveMarshallerUtils.marshalling(request.getRuleDesc(), "String"));
        }
        if (request.getPeriodType() != null) {
            internalRequest.addParameter("periodType", PrimitiveMarshallerUtils.marshalling(request.getPeriodType(), "String"));
        }
        if (request.getPeriod() != null) {
            internalRequest.addParameter("period", PrimitiveMarshallerUtils.marshalling(request.getPeriod(), "Integer"));
        }
        if (request.getFrequency() != null) {
            internalRequest.addParameter("frequency", PrimitiveMarshallerUtils.marshalling(request.getFrequency(), "Integer"));
        }
        if (request.getBusinessType() != null) {
            internalRequest.addParameter("businessType", PrimitiveMarshallerUtils.marshalling(request.getBusinessType(), "String"));
        }
        if (request.getUserNo() != null) {
            internalRequest.addParameter("userNo", PrimitiveMarshallerUtils.marshalling(request.getUserNo(), "String"));
        }
        if (request.getProductNo() != null) {
            internalRequest.addParameter("productNo", PrimitiveMarshallerUtils.marshalling(request.getProductNo(), "String"));
        }
        if (request.getProductName() != null) {
            internalRequest.addParameter("productName", PrimitiveMarshallerUtils.marshalling(request.getProductName(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static AddRightsRequestMarshaller INSTANCE = new AddRightsRequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static AddRightsRequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
