/*
 * 商户充值
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.recharge;

import com.yeepay.yop.sdk.client.*;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.HttpResponseAnalyzerSupport;
import com.yeepay.yop.sdk.http.HttpResponseHandler;
import com.yeepay.yop.sdk.http.handler.DefaultHttpResponseHandler;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.service.recharge.request.*;
import com.yeepay.yop.sdk.service.recharge.response.*;

public class RechargeClientImpl implements RechargeClient {

    private final ClientHandler clientHandler;

    RechargeClientImpl(ClientParams clientParams) {
        this.clientHandler = new ClientHandlerImpl(new ClientHandlerParams().withClientParams(clientParams));
    }

    @Override
    public BankAccountQueryResponse bankAccountQuery(BankAccountQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountQueryRequest> requestMarshaller = BankAccountQueryRequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<BankAccountQueryResponse>(BankAccountQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountQueryRequest, BankAccountQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }

    @Override
    public void shutdown() {
        clientHandler.shutdown();
    }

}
