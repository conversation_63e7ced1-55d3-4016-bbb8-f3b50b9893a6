/*
 * 供应商付款系统
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.supplier_remit;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.model.yos.YosDownloadResponse;
import com.yeepay.yop.sdk.service.supplier_remit.request.*;

public interface SupplierRemitClient {

    /**
     * 付款对账文件获取接口
     * 支持商户进行对账文件下载。 受理失败，返回响应错误码；受理成功，返回文件
     * @return YosDownloadResponse
     * @throws YopClientException if fails to make API call
     */
    YosDownloadResponse billDownload(BillDownloadRequest request) throws YopClientException;

    /**
     * 付款对账文件获取接口
     * 支持商户进行对账文件下载。 受理失败，返回响应错误码；受理成功，返回文件
     * @return YosDownloadResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    YosDownloadResponse bill_download_v1_0(BillDownloadV10Request request) throws YopClientException;


    /**
     * Shuts down this client object, releasing any resources that might be held open. This is an optional method, and
     * callers are not expected to call it, but can if they want to explicitly release any open resources. Once a client
     * has been shutdown, it should not be used to make any more requests.
     */
    void shutdown();
}
