/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.promtion.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class SubsidyApplyRequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<SubsidyApplyRequest> {
    private final String serviceName = "Promtion";

    private final String resourcePath = "/rest/v1.0/promtion/subsidy/apply";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.POST;


    @Override
    public Request<SubsidyApplyRequest> marshall(SubsidyApplyRequest request) {
        Request<SubsidyApplyRequest> internalRequest = new DefaultRequest<SubsidyApplyRequest>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getOrderId() != null) {
            internalRequest.addParameter("orderId", PrimitiveMarshallerUtils.marshalling(request.getOrderId(), "String"));
        }
        if (request.getUniqueOrderNo() != null) {
            internalRequest.addParameter("uniqueOrderNo", PrimitiveMarshallerUtils.marshalling(request.getUniqueOrderNo(), "String"));
        }
        if (request.getSubsidyRequestId() != null) {
            internalRequest.addParameter("subsidyRequestId", PrimitiveMarshallerUtils.marshalling(request.getSubsidyRequestId(), "String"));
        }
        if (request.getSubsidyAmount() != null) {
            internalRequest.addParameter("subsidyAmount", PrimitiveMarshallerUtils.marshalling(request.getSubsidyAmount(), "String"));
        }
        if (request.getAssumeMerchantNo() != null) {
            internalRequest.addParameter("assumeMerchantNo", PrimitiveMarshallerUtils.marshalling(request.getAssumeMerchantNo(), "String"));
        }
        if (request.getMemo() != null) {
            internalRequest.addParameter("memo", PrimitiveMarshallerUtils.marshalling(request.getMemo(), "String"));
        }
        if (request.getParentMerchantNo() != null) {
            internalRequest.addParameter("parentMerchantNo", PrimitiveMarshallerUtils.marshalling(request.getParentMerchantNo(), "String"));
        }
        if (request.getMerchantNo() != null) {
            internalRequest.addParameter("merchantNo", PrimitiveMarshallerUtils.marshalling(request.getMerchantNo(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static SubsidyApplyRequestMarshaller INSTANCE = new SubsidyApplyRequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static SubsidyApplyRequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
