/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 请修改我
 */
public class APIOfflineTransferAccountInfoConfirmRequestDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;发起方商户编号。&lt;br /&gt;*标准商户收付款方案中此参数与收款商户编号一致；&lt;br /&gt;*平台商户收付款方案中此参数为平台商商户编号；&lt;br /&gt;*服务商解决方案中，①标准商户收款时，该参数为标准商户商编 ②平台商收款或平台商入驻商户收款时，该参数为平台商商编。&lt;br /&gt;&lt;br /&gt;&lt;/p&gt;
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * &lt;p&gt;收单主体商编，平台商或服务商下的子商户，普通特约商户&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;p&gt;商户收款请求号&lt;/p&gt;
   */
  @JsonProperty("orderId")
  private String orderId = null;

  /**
   * &lt;div data-page-id&#x3D;\&quot;VAcYdB1hso405hxOgwDcyFkSnZg\&quot; data-lark-html-role&#x3D;\&quot;root\&quot; data-docx-has-block-data&#x3D;\&quot;false\&quot;&gt; &lt;div class&#x3D;\&quot;ace-line ace-line old-record-id-BMIYdqnLiozlrSxIpKpc9Q4pnPe\&quot;&gt;入金通知流水号&lt;/div&gt; &lt;/div&gt;
   */
  @JsonProperty("bankOrderNo")
  private String bankOrderNo = null;

  public APIOfflineTransferAccountInfoConfirmRequestDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;发起方商户编号。&lt;br /&gt;*标准商户收付款方案中此参数与收款商户编号一致；&lt;br /&gt;*平台商户收付款方案中此参数为平台商商户编号；&lt;br /&gt;*服务商解决方案中，①标准商户收款时，该参数为标准商户商编 ②平台商收款或平台商入驻商户收款时，该参数为平台商商编。&lt;br /&gt;&lt;br /&gt;&lt;/p&gt;
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public APIOfflineTransferAccountInfoConfirmRequestDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;收单主体商编，平台商或服务商下的子商户，普通特约商户&lt;/p&gt;
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public APIOfflineTransferAccountInfoConfirmRequestDTO orderId(String orderId) {
    this.orderId = orderId;
    return this;
  }

   /**
   * &lt;p&gt;商户收款请求号&lt;/p&gt;
   * @return orderId
  **/

  public String getOrderId() {
    return orderId;
  }

  public void setOrderId(String orderId) {
    this.orderId = orderId;
  }

  public APIOfflineTransferAccountInfoConfirmRequestDTO bankOrderNo(String bankOrderNo) {
    this.bankOrderNo = bankOrderNo;
    return this;
  }

   /**
   * &lt;div data-page-id&#x3D;\&quot;VAcYdB1hso405hxOgwDcyFkSnZg\&quot; data-lark-html-role&#x3D;\&quot;root\&quot; data-docx-has-block-data&#x3D;\&quot;false\&quot;&gt; &lt;div class&#x3D;\&quot;ace-line ace-line old-record-id-BMIYdqnLiozlrSxIpKpc9Q4pnPe\&quot;&gt;入金通知流水号&lt;/div&gt; &lt;/div&gt;
   * @return bankOrderNo
  **/

  public String getBankOrderNo() {
    return bankOrderNo;
  }

  public void setBankOrderNo(String bankOrderNo) {
    this.bankOrderNo = bankOrderNo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    APIOfflineTransferAccountInfoConfirmRequestDTO apIOfflineTransferAccountInfoConfirmRequestDTO = (APIOfflineTransferAccountInfoConfirmRequestDTO) o;
    return ObjectUtils.equals(this.parentMerchantNo, apIOfflineTransferAccountInfoConfirmRequestDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, apIOfflineTransferAccountInfoConfirmRequestDTO.merchantNo) &&
    ObjectUtils.equals(this.orderId, apIOfflineTransferAccountInfoConfirmRequestDTO.orderId) &&
    ObjectUtils.equals(this.bankOrderNo, apIOfflineTransferAccountInfoConfirmRequestDTO.bankOrderNo);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(parentMerchantNo, merchantNo, orderId, bankOrderNo);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class APIOfflineTransferAccountInfoConfirmRequestDTO {\n");
    
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    orderId: ").append(toIndentedString(orderId)).append("\n");
    sb.append("    bankOrderNo: ").append(toIndentedString(bankOrderNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

