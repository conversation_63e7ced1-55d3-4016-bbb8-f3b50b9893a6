/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yeepay.yop.sdk.service.base.request.serializer.YopCustomSerializer;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 电影票订单请求参数
 */
public class CinemaOrderRequestDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;座位数量&lt;/p&gt;
   */
  @JsonProperty("seatsCount")
  private Integer seatsCount = null;

  /**
   * &lt;p&gt;手机号&lt;/p&gt;
   */
  @JsonProperty("mobile")
  private String mobile = null;

  /**
   * &lt;p&gt;供应商编码&lt;/p&gt;
   */
  @JsonProperty("supplierChannel")
  private String supplierChannel = null;

  /**
   * &lt;pre&gt;是否接受调座&lt;/pre&gt;
   */
  @JsonProperty("acceptAdjust")
  private Boolean acceptAdjust = null;

  /**
   * &lt;pre&gt;原始座位id，多个座位用英文,号分割&lt;/pre&gt;
   */
  @JsonProperty("seats")
  private String seats = null;

  /**
   * &lt;pre&gt;场次id&lt;/pre&gt;
   */
  @JsonProperty("showId")
  private Long showId = null;

  /**
   * &lt;pre&gt;0:特惠出票 5:快速出票&lt;/pre&gt;
   */
  @JsonProperty("drawMode")
  private Integer drawMode = null;

  /**
   * &lt;pre&gt;订单完成通知地址：订单发货成功、订单取消、订单已完成&lt;a class&#x3D;\&quot;json_link\&quot; href&#x3D;\&quot;https://wwww.yeepay.com/\&quot; target&#x3D;\&quot;_blank\&quot; rel&#x3D;\&quot;noopener\&quot;&gt;https://wwww.yeepay.com&lt;/a&gt;&lt;/pre&gt;
   */
  @JsonProperty("complateNotifyUrl")
  private String complateNotifyUrl = null;

  /**
   * &lt;pre&gt;平台用户唯一标识，&lt;br /&gt;不传则会使用一个统一的平台用户来作为订单用户&lt;/pre&gt;
   */
  @JsonProperty("platformUniqueId")
  private String platformUniqueId = null;

  /**
   * &lt;pre&gt;供应方平台给采购方分配的账号信息。&lt;br /&gt;platformId与secret要拼接起来使用&lt;br /&gt;aes加密后传递，aes key在对接时找易宝技术支持&lt;/pre&gt; &lt;pre&gt;加密方式：&lt;br /&gt;platformId&#x3D;123&amp;amp;secret&#x3D;abc &lt;br /&gt;将拼接的字符串直接使用aes加密&lt;/pre&gt;
   */
  @JsonProperty("platformIdAccountInfo")
  private String platformIdAccountInfo = null;

  /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;p&gt;请求方商编&lt;/p&gt;
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * &lt;p&gt;商户订单号&lt;/p&gt;
   */
  @JsonProperty("merchantRequestNo")
  private String merchantRequestNo = null;

  /**
   * &lt;pre&gt;请求方订单号&lt;/pre&gt;
   */
  @JsonProperty("parentMerchantRequestNo")
  private String parentMerchantRequestNo = null;

  /**
   * &lt;pre&gt;订单金额，保留2位小数。&lt;/pre&gt; &lt;p&gt;如果传入金额和资源方生成的订单金额不一致则会报错&lt;/p&gt;
   */
  @JsonProperty("orderAmount")
  private BigDecimal orderAmount = null;

  public CinemaOrderRequestDTO seatsCount(Integer seatsCount) {
    this.seatsCount = seatsCount;
    return this;
  }

   /**
   * &lt;p&gt;座位数量&lt;/p&gt;
   * @return seatsCount
  **/

  public Integer getSeatsCount() {
    return seatsCount;
  }

  public void setSeatsCount(Integer seatsCount) {
    this.seatsCount = seatsCount;
  }

  public CinemaOrderRequestDTO mobile(String mobile) {
    this.mobile = mobile;
    return this;
  }

   /**
   * &lt;p&gt;手机号&lt;/p&gt;
   * @return mobile
  **/

  public String getMobile() {
    return mobile;
  }

  public void setMobile(String mobile) {
    this.mobile = mobile;
  }

  public CinemaOrderRequestDTO supplierChannel(String supplierChannel) {
    this.supplierChannel = supplierChannel;
    return this;
  }

   /**
   * &lt;p&gt;供应商编码&lt;/p&gt;
   * @return supplierChannel
  **/

  public String getSupplierChannel() {
    return supplierChannel;
  }

  public void setSupplierChannel(String supplierChannel) {
    this.supplierChannel = supplierChannel;
  }

  public CinemaOrderRequestDTO acceptAdjust(Boolean acceptAdjust) {
    this.acceptAdjust = acceptAdjust;
    return this;
  }

   /**
   * &lt;pre&gt;是否接受调座&lt;/pre&gt;
   * @return acceptAdjust
  **/

  public Boolean isAcceptAdjust() {
    return acceptAdjust;
  }

  public void setAcceptAdjust(Boolean acceptAdjust) {
    this.acceptAdjust = acceptAdjust;
  }

  public CinemaOrderRequestDTO seats(String seats) {
    this.seats = seats;
    return this;
  }

   /**
   * &lt;pre&gt;原始座位id，多个座位用英文,号分割&lt;/pre&gt;
   * @return seats
  **/

  public String getSeats() {
    return seats;
  }

  public void setSeats(String seats) {
    this.seats = seats;
  }

  public CinemaOrderRequestDTO showId(Long showId) {
    this.showId = showId;
    return this;
  }

   /**
   * &lt;pre&gt;场次id&lt;/pre&gt;
   * @return showId
  **/

  public Long getShowId() {
    return showId;
  }

  public void setShowId(Long showId) {
    this.showId = showId;
  }

  public CinemaOrderRequestDTO drawMode(Integer drawMode) {
    this.drawMode = drawMode;
    return this;
  }

   /**
   * &lt;pre&gt;0:特惠出票 5:快速出票&lt;/pre&gt;
   * @return drawMode
  **/

  public Integer getDrawMode() {
    return drawMode;
  }

  public void setDrawMode(Integer drawMode) {
    this.drawMode = drawMode;
  }

  public CinemaOrderRequestDTO complateNotifyUrl(String complateNotifyUrl) {
    this.complateNotifyUrl = complateNotifyUrl;
    return this;
  }

   /**
   * &lt;pre&gt;订单完成通知地址：订单发货成功、订单取消、订单已完成&lt;a class&#x3D;\&quot;json_link\&quot; href&#x3D;\&quot;https://wwww.yeepay.com/\&quot; target&#x3D;\&quot;_blank\&quot; rel&#x3D;\&quot;noopener\&quot;&gt;https://wwww.yeepay.com&lt;/a&gt;&lt;/pre&gt;
   * @return complateNotifyUrl
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getComplateNotifyUrl() {
    return complateNotifyUrl;
  }

  public void setComplateNotifyUrl(String complateNotifyUrl) {
    this.complateNotifyUrl = complateNotifyUrl;
  }

  public CinemaOrderRequestDTO platformUniqueId(String platformUniqueId) {
    this.platformUniqueId = platformUniqueId;
    return this;
  }

   /**
   * &lt;pre&gt;平台用户唯一标识，&lt;br /&gt;不传则会使用一个统一的平台用户来作为订单用户&lt;/pre&gt;
   * @return platformUniqueId
  **/

  public String getPlatformUniqueId() {
    return platformUniqueId;
  }

  public void setPlatformUniqueId(String platformUniqueId) {
    this.platformUniqueId = platformUniqueId;
  }

  public CinemaOrderRequestDTO platformIdAccountInfo(String platformIdAccountInfo) {
    this.platformIdAccountInfo = platformIdAccountInfo;
    return this;
  }

   /**
   * &lt;pre&gt;供应方平台给采购方分配的账号信息。&lt;br /&gt;platformId与secret要拼接起来使用&lt;br /&gt;aes加密后传递，aes key在对接时找易宝技术支持&lt;/pre&gt; &lt;pre&gt;加密方式：&lt;br /&gt;platformId&#x3D;123&amp;amp;secret&#x3D;abc &lt;br /&gt;将拼接的字符串直接使用aes加密&lt;/pre&gt;
   * @return platformIdAccountInfo
  **/

  public String getPlatformIdAccountInfo() {
    return platformIdAccountInfo;
  }

  public void setPlatformIdAccountInfo(String platformIdAccountInfo) {
    this.platformIdAccountInfo = platformIdAccountInfo;
  }

  public CinemaOrderRequestDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public CinemaOrderRequestDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;请求方商编&lt;/p&gt;
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public CinemaOrderRequestDTO merchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;商户订单号&lt;/p&gt;
   * @return merchantRequestNo
  **/

  public String getMerchantRequestNo() {
    return merchantRequestNo;
  }

  public void setMerchantRequestNo(String merchantRequestNo) {
    this.merchantRequestNo = merchantRequestNo;
  }

  public CinemaOrderRequestDTO parentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
    return this;
  }

   /**
   * &lt;pre&gt;请求方订单号&lt;/pre&gt;
   * @return parentMerchantRequestNo
  **/

  public String getParentMerchantRequestNo() {
    return parentMerchantRequestNo;
  }

  public void setParentMerchantRequestNo(String parentMerchantRequestNo) {
    this.parentMerchantRequestNo = parentMerchantRequestNo;
  }

  public CinemaOrderRequestDTO orderAmount(BigDecimal orderAmount) {
    this.orderAmount = orderAmount;
    return this;
  }

   /**
   * &lt;pre&gt;订单金额，保留2位小数。&lt;/pre&gt; &lt;p&gt;如果传入金额和资源方生成的订单金额不一致则会报错&lt;/p&gt;
   * @return orderAmount
  **/

  public BigDecimal getOrderAmount() {
    return orderAmount;
  }

  public void setOrderAmount(BigDecimal orderAmount) {
    this.orderAmount = orderAmount;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    CinemaOrderRequestDTO cinemaOrderRequestDTO = (CinemaOrderRequestDTO) o;
    return ObjectUtils.equals(this.seatsCount, cinemaOrderRequestDTO.seatsCount) &&
    ObjectUtils.equals(this.mobile, cinemaOrderRequestDTO.mobile) &&
    ObjectUtils.equals(this.supplierChannel, cinemaOrderRequestDTO.supplierChannel) &&
    ObjectUtils.equals(this.acceptAdjust, cinemaOrderRequestDTO.acceptAdjust) &&
    ObjectUtils.equals(this.seats, cinemaOrderRequestDTO.seats) &&
    ObjectUtils.equals(this.showId, cinemaOrderRequestDTO.showId) &&
    ObjectUtils.equals(this.drawMode, cinemaOrderRequestDTO.drawMode) &&
    ObjectUtils.equals(this.complateNotifyUrl, cinemaOrderRequestDTO.complateNotifyUrl) &&
    ObjectUtils.equals(this.platformUniqueId, cinemaOrderRequestDTO.platformUniqueId) &&
    ObjectUtils.equals(this.platformIdAccountInfo, cinemaOrderRequestDTO.platformIdAccountInfo) &&
    ObjectUtils.equals(this.merchantNo, cinemaOrderRequestDTO.merchantNo) &&
    ObjectUtils.equals(this.parentMerchantNo, cinemaOrderRequestDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantRequestNo, cinemaOrderRequestDTO.merchantRequestNo) &&
    ObjectUtils.equals(this.parentMerchantRequestNo, cinemaOrderRequestDTO.parentMerchantRequestNo) &&
    ObjectUtils.equals(this.orderAmount, cinemaOrderRequestDTO.orderAmount);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(seatsCount, mobile, supplierChannel, acceptAdjust, seats, showId, drawMode, complateNotifyUrl, platformUniqueId, platformIdAccountInfo, merchantNo, parentMerchantNo, merchantRequestNo, parentMerchantRequestNo, orderAmount);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CinemaOrderRequestDTO {\n");
    
    sb.append("    seatsCount: ").append(toIndentedString(seatsCount)).append("\n");
    sb.append("    mobile: ").append(toIndentedString(mobile)).append("\n");
    sb.append("    supplierChannel: ").append(toIndentedString(supplierChannel)).append("\n");
    sb.append("    acceptAdjust: ").append(toIndentedString(acceptAdjust)).append("\n");
    sb.append("    seats: ").append(toIndentedString(seats)).append("\n");
    sb.append("    showId: ").append(toIndentedString(showId)).append("\n");
    sb.append("    drawMode: ").append(toIndentedString(drawMode)).append("\n");
    sb.append("    complateNotifyUrl: ").append(toIndentedString(complateNotifyUrl)).append("\n");
    sb.append("    platformUniqueId: ").append(toIndentedString(platformUniqueId)).append("\n");
    sb.append("    platformIdAccountInfo: ").append(toIndentedString(platformIdAccountInfo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantRequestNo: ").append(toIndentedString(merchantRequestNo)).append("\n");
    sb.append("    parentMerchantRequestNo: ").append(toIndentedString(parentMerchantRequestNo)).append("\n");
    sb.append("    orderAmount: ").append(toIndentedString(orderAmount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

