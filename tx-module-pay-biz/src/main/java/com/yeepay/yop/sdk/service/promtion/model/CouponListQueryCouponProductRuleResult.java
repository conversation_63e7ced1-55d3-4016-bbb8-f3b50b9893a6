/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 支持产品规则
 */
public class CouponListQueryCouponProductRuleResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 产品类型
   */
  @JsonProperty("productType")
  private String productType = null;

  /**
   * 产品ID列表
   */
  @JsonProperty("productIds")
  private List<String> productIds = null;

  public CouponListQueryCouponProductRuleResult productType(String productType) {
    this.productType = productType;
    return this;
  }

   /**
   * 产品类型
   * @return productType
  **/

  public String getProductType() {
    return productType;
  }

  public void setProductType(String productType) {
    this.productType = productType;
  }

  public CouponListQueryCouponProductRuleResult productIds(List<String> productIds) {
    this.productIds = productIds;
    return this;
  }

  public CouponListQueryCouponProductRuleResult addProductIdsItem(String productIdsItem) {
    if (this.productIds == null) {
      this.productIds = new ArrayList<>();
    }
    this.productIds.add(productIdsItem);
    return this;
  }

   /**
   * 产品ID列表
   * @return productIds
  **/

  public List<String> getProductIds() {
    return productIds;
  }

  public void setProductIds(List<String> productIds) {
    this.productIds = productIds;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    CouponListQueryCouponProductRuleResult couponListQueryCouponProductRuleResult = (CouponListQueryCouponProductRuleResult) o;
    return ObjectUtils.equals(this.productType, couponListQueryCouponProductRuleResult.productType) &&
    ObjectUtils.equals(this.productIds, couponListQueryCouponProductRuleResult.productIds);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(productType, productIds);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CouponListQueryCouponProductRuleResult {\n");
    
    sb.append("    productType: ").append(toIndentedString(productType)).append("\n");
    sb.append("    productIds: ").append(toIndentedString(productIds)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

