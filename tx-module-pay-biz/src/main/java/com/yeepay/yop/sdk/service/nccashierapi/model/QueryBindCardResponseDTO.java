/*
 * API收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.nccashierapi.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yeepay.yop.sdk.service.base.request.serializer.YopCustomSerializer;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 查询绑卡请求响应
 */
public class QueryBindCardResponseDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;业务响应码&lt;br /&gt;0000表示成功&lt;/p&gt;
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * 
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 
   */
  @JsonProperty("merchantFlowId")
  private String merchantFlowId = null;

  /**
   * 
   */
  @JsonProperty("userNo")
  private String userNo = null;

  /**
   *  可选项如下: USER_ID:用 ID WECHAT:微信号 PHONE:用户注册手机号 ID_CARD:用户身份证号 IMEI:imei MAC:网卡地址 EMAIL:用户注册email AGREEMENT_NO:用户纸质订单协议号 
   */
  public enum UserTypeEnum {
    USER_ID("USER_ID"),
    
    WECHAT("WECHAT"),
    
    PHONE("PHONE"),
    
    ID_CARD("ID_CARD"),
    
    IMEI("IMEI"),
    
    MAC("MAC"),
    
    EMAIL("EMAIL"),
    
    AGREEMENT_NO("AGREEMENT_NO");

    private String value;

    UserTypeEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static UserTypeEnum fromValue(String text) {
      for (UserTypeEnum b : UserTypeEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      return null;
    }
  }

  /**
   *  可选项如下: USER_ID:用 ID WECHAT:微信号 PHONE:用户注册手机号 ID_CARD:用户身份证号 IMEI:imei MAC:网卡地址 EMAIL:用户注册email AGREEMENT_NO:用户纸质订单协议号 
   */
  @JsonProperty("userType")
  private UserTypeEnum userType = null;

  /**
   * &lt;p&gt;暂未支持查询失败的绑卡信息，绑卡请求为H5 形态，用户可以修改银行卡信息重新绑卡，所以默认会绑卡成功，如果在过期时间内没有绑定成功，则以超时处理。&lt;/p&gt; 可选项如下: BIND_SUCCESS:绑卡成功 BIND_PROCESSING:绑卡处理中 TIME_OUT:超时 
   */
  public enum OrderStatusEnum {
    BIND_SUCCESS("BIND_SUCCESS"),
    
    BIND_PROCESSING("BIND_PROCESSING"),
    
    TIME_OUT("TIME_OUT");

    private String value;

    OrderStatusEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static OrderStatusEnum fromValue(String text) {
      for (OrderStatusEnum b : OrderStatusEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      return null;
    }
  }

  /**
   * &lt;p&gt;暂未支持查询失败的绑卡信息，绑卡请求为H5 形态，用户可以修改银行卡信息重新绑卡，所以默认会绑卡成功，如果在过期时间内没有绑定成功，则以超时处理。&lt;/p&gt; 可选项如下: BIND_SUCCESS:绑卡成功 BIND_PROCESSING:绑卡处理中 TIME_OUT:超时 
   */
  @JsonProperty("orderStatus")
  private OrderStatusEnum orderStatus = null;

  /**
   * &lt;p&gt;易宝内部绑卡请求号&lt;/p&gt;
   */
  @JsonProperty("bindRequestId")
  private String bindRequestId = null;

  /**
   * &lt;p&gt;易宝内部绑卡订单号，只有用户真正绑卡成功才有值&lt;/p&gt;
   */
  @JsonProperty("nopOrderId")
  private String nopOrderId = null;

  /**
   * &lt;p&gt;绑卡成功返回绑卡id，支付时传递&lt;/p&gt;
   */
  @JsonProperty("bindId")
  private String bindId = null;

  /**
   * &lt;p&gt;绑卡银行卡号&lt;/p&gt;
   */
  @JsonProperty("cardNo")
  private BigDecimal cardNo = null;

  /**
   * 
   */
  @JsonProperty("phone")
  private BigDecimal phone = null;

  public QueryBindCardResponseDTO code(String code) {
    this.code = code;
    return this;
  }

   /**
   * &lt;p&gt;业务响应码&lt;br /&gt;0000表示成功&lt;/p&gt;
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public QueryBindCardResponseDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * Get message
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public QueryBindCardResponseDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * Get parentMerchantNo
   * @return parentMerchantNo
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public QueryBindCardResponseDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * Get merchantNo
   * @return merchantNo
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public QueryBindCardResponseDTO merchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
    return this;
  }

   /**
   * Get merchantFlowId
   * @return merchantFlowId
  **/

  public String getMerchantFlowId() {
    return merchantFlowId;
  }

  public void setMerchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
  }

  public QueryBindCardResponseDTO userNo(String userNo) {
    this.userNo = userNo;
    return this;
  }

   /**
   * Get userNo
   * @return userNo
  **/

  public String getUserNo() {
    return userNo;
  }

  public void setUserNo(String userNo) {
    this.userNo = userNo;
  }

  public QueryBindCardResponseDTO userType(UserTypeEnum userType) {
    this.userType = userType;
    return this;
  }

   /**
   *  可选项如下: USER_ID:用 ID WECHAT:微信号 PHONE:用户注册手机号 ID_CARD:用户身份证号 IMEI:imei MAC:网卡地址 EMAIL:用户注册email AGREEMENT_NO:用户纸质订单协议号 
   * @return userType
  **/

  public UserTypeEnum getUserType() {
    return userType;
  }

  public void setUserType(UserTypeEnum userType) {
    this.userType = userType;
  }

  public QueryBindCardResponseDTO orderStatus(OrderStatusEnum orderStatus) {
    this.orderStatus = orderStatus;
    return this;
  }

   /**
   * &lt;p&gt;暂未支持查询失败的绑卡信息，绑卡请求为H5 形态，用户可以修改银行卡信息重新绑卡，所以默认会绑卡成功，如果在过期时间内没有绑定成功，则以超时处理。&lt;/p&gt; 可选项如下: BIND_SUCCESS:绑卡成功 BIND_PROCESSING:绑卡处理中 TIME_OUT:超时 
   * @return orderStatus
  **/

  public OrderStatusEnum getOrderStatus() {
    return orderStatus;
  }

  public void setOrderStatus(OrderStatusEnum orderStatus) {
    this.orderStatus = orderStatus;
  }

  public QueryBindCardResponseDTO bindRequestId(String bindRequestId) {
    this.bindRequestId = bindRequestId;
    return this;
  }

   /**
   * &lt;p&gt;易宝内部绑卡请求号&lt;/p&gt;
   * @return bindRequestId
  **/

  public String getBindRequestId() {
    return bindRequestId;
  }

  public void setBindRequestId(String bindRequestId) {
    this.bindRequestId = bindRequestId;
  }

  public QueryBindCardResponseDTO nopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
    return this;
  }

   /**
   * &lt;p&gt;易宝内部绑卡订单号，只有用户真正绑卡成功才有值&lt;/p&gt;
   * @return nopOrderId
  **/

  public String getNopOrderId() {
    return nopOrderId;
  }

  public void setNopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
  }

  public QueryBindCardResponseDTO bindId(String bindId) {
    this.bindId = bindId;
    return this;
  }

   /**
   * &lt;p&gt;绑卡成功返回绑卡id，支付时传递&lt;/p&gt;
   * @return bindId
  **/

  public String getBindId() {
    return bindId;
  }

  public void setBindId(String bindId) {
    this.bindId = bindId;
  }

  public QueryBindCardResponseDTO cardNo(BigDecimal cardNo) {
    this.cardNo = cardNo;
    return this;
  }

   /**
   * &lt;p&gt;绑卡银行卡号&lt;/p&gt;
   * @return cardNo
  **/

  public BigDecimal getCardNo() {
    return cardNo;
  }

  public void setCardNo(BigDecimal cardNo) {
    this.cardNo = cardNo;
  }

  public QueryBindCardResponseDTO phone(BigDecimal phone) {
    this.phone = phone;
    return this;
  }

   /**
   * Get phone
   * @return phone
  **/

  public BigDecimal getPhone() {
    return phone;
  }

  public void setPhone(BigDecimal phone) {
    this.phone = phone;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QueryBindCardResponseDTO queryBindCardResponseDTO = (QueryBindCardResponseDTO) o;
    return ObjectUtils.equals(this.code, queryBindCardResponseDTO.code) &&
    ObjectUtils.equals(this.message, queryBindCardResponseDTO.message) &&
    ObjectUtils.equals(this.parentMerchantNo, queryBindCardResponseDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, queryBindCardResponseDTO.merchantNo) &&
    ObjectUtils.equals(this.merchantFlowId, queryBindCardResponseDTO.merchantFlowId) &&
    ObjectUtils.equals(this.userNo, queryBindCardResponseDTO.userNo) &&
    ObjectUtils.equals(this.userType, queryBindCardResponseDTO.userType) &&
    ObjectUtils.equals(this.orderStatus, queryBindCardResponseDTO.orderStatus) &&
    ObjectUtils.equals(this.bindRequestId, queryBindCardResponseDTO.bindRequestId) &&
    ObjectUtils.equals(this.nopOrderId, queryBindCardResponseDTO.nopOrderId) &&
    ObjectUtils.equals(this.bindId, queryBindCardResponseDTO.bindId) &&
    ObjectUtils.equals(this.cardNo, queryBindCardResponseDTO.cardNo) &&
    ObjectUtils.equals(this.phone, queryBindCardResponseDTO.phone);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, parentMerchantNo, merchantNo, merchantFlowId, userNo, userType, orderStatus, bindRequestId, nopOrderId, bindId, cardNo, phone);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryBindCardResponseDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantFlowId: ").append(toIndentedString(merchantFlowId)).append("\n");
    sb.append("    userNo: ").append(toIndentedString(userNo)).append("\n");
    sb.append("    userType: ").append(toIndentedString(userType)).append("\n");
    sb.append("    orderStatus: ").append(toIndentedString(orderStatus)).append("\n");
    sb.append("    bindRequestId: ").append(toIndentedString(bindRequestId)).append("\n");
    sb.append("    nopOrderId: ").append(toIndentedString(nopOrderId)).append("\n");
    sb.append("    bindId: ").append(toIndentedString(bindId)).append("\n");
    sb.append("    cardNo: ").append(toIndentedString(cardNo)).append("\n");
    sb.append("    phone: ").append(toIndentedString(phone)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

