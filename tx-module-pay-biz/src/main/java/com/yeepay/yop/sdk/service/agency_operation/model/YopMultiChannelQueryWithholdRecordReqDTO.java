/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.agency_operation.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 请修改我
 */
public class YopMultiChannelQueryWithholdRecordReqDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;抽佣接收方商编&lt;/p&gt;
   */
  @JsonProperty("receiveMerchantNo")
  private String receiveMerchantNo = null;

  /**
   * &lt;p&gt;被抽佣方商编&lt;/p&gt;
   */
  @JsonProperty("contributeMerchantNo")
  private String contributeMerchantNo = null;

  /**
   * &lt;p&gt;请求号&lt;/p&gt;
   */
  @JsonProperty("requestNo")
  private String requestNo = null;

  public YopMultiChannelQueryWithholdRecordReqDTO receiveMerchantNo(String receiveMerchantNo) {
    this.receiveMerchantNo = receiveMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;抽佣接收方商编&lt;/p&gt;
   * @return receiveMerchantNo
  **/

  public String getReceiveMerchantNo() {
    return receiveMerchantNo;
  }

  public void setReceiveMerchantNo(String receiveMerchantNo) {
    this.receiveMerchantNo = receiveMerchantNo;
  }

  public YopMultiChannelQueryWithholdRecordReqDTO contributeMerchantNo(String contributeMerchantNo) {
    this.contributeMerchantNo = contributeMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;被抽佣方商编&lt;/p&gt;
   * @return contributeMerchantNo
  **/

  public String getContributeMerchantNo() {
    return contributeMerchantNo;
  }

  public void setContributeMerchantNo(String contributeMerchantNo) {
    this.contributeMerchantNo = contributeMerchantNo;
  }

  public YopMultiChannelQueryWithholdRecordReqDTO requestNo(String requestNo) {
    this.requestNo = requestNo;
    return this;
  }

   /**
   * &lt;p&gt;请求号&lt;/p&gt;
   * @return requestNo
  **/

  public String getRequestNo() {
    return requestNo;
  }

  public void setRequestNo(String requestNo) {
    this.requestNo = requestNo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    YopMultiChannelQueryWithholdRecordReqDTO yopMultiChannelQueryWithholdRecordReqDTO = (YopMultiChannelQueryWithholdRecordReqDTO) o;
    return ObjectUtils.equals(this.receiveMerchantNo, yopMultiChannelQueryWithholdRecordReqDTO.receiveMerchantNo) &&
    ObjectUtils.equals(this.contributeMerchantNo, yopMultiChannelQueryWithholdRecordReqDTO.contributeMerchantNo) &&
    ObjectUtils.equals(this.requestNo, yopMultiChannelQueryWithholdRecordReqDTO.requestNo);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(receiveMerchantNo, contributeMerchantNo, requestNo);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class YopMultiChannelQueryWithholdRecordReqDTO {\n");
    
    sb.append("    receiveMerchantNo: ").append(toIndentedString(receiveMerchantNo)).append("\n");
    sb.append("    contributeMerchantNo: ").append(toIndentedString(contributeMerchantNo)).append("\n");
    sb.append("    requestNo: ").append(toIndentedString(requestNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

