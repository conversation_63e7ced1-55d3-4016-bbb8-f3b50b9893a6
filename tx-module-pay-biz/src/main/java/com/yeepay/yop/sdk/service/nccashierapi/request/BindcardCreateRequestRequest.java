/*
 * API收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.nccashierapi.request;

import com.yeepay.yop.sdk.service.nccashierapi.model.BindCardRequestDTO;
public class BindcardCreateRequestRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private BindCardRequestDTO body;


    /**
     * Get body
     * @return body
     **/
    
    public BindCardRequestDTO getBody() {
        return body;
    }

    public void setBody(BindCardRequestDTO body) {
        this.body = body;
    }

    @Override
    public String getOperationId() {
        return "bindcardCreateRequest";
    }
}
