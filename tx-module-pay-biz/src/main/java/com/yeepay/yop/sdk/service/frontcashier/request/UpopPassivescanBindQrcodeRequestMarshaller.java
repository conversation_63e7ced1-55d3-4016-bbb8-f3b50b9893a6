/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import com.yeepay.yop.sdk.YopConstants;
import com.yeepay.yop.sdk.http.Headers;
import com.yeepay.yop.sdk.http.HttpMethodName;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.internal.DefaultRequest;
import com.yeepay.yop.sdk.internal.Request;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.protocol.marshaller.PrimitiveMarshallerUtils;
import java.util.Map;
import java.util.UUID;
import java.util.Iterator;
import org.apache.commons.lang3.BooleanUtils;
import com.yeepay.yop.sdk.service.base.request.BaseRequestMarshaller;


public class UpopPassivescanBindQrcodeRequestMarshaller extends BaseRequestMarshaller implements RequestMarshaller<UpopPassivescanBindQrcodeRequest> {
    private final String serviceName = "Frontcashier";

    private final String resourcePath = "/rest/v1.0/frontcashier/upop/passivescan/bind/qrcode";

    private final String contentType = "application/x-www-form-urlencoded";

    private final HttpMethodName httpMethodName = HttpMethodName.POST;


    @Override
    public Request<UpopPassivescanBindQrcodeRequest> marshall(UpopPassivescanBindQrcodeRequest request) {
        Request<UpopPassivescanBindQrcodeRequest> internalRequest = new DefaultRequest<UpopPassivescanBindQrcodeRequest>(request, serviceName);
        internalRequest.setResourcePath(resourcePath);
        internalRequest.setHttpMethod(httpMethodName);
        if (!internalRequest.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            internalRequest.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }
        Map<String, String> customerHeaders = request.getHeaders();
        if (customerHeaders != null) {
            for (String key : customerHeaders.keySet()) {
                internalRequest.addHeader(key, customerHeaders.get(key));
            }
        }
        if (request.getMerchantFlowId() != null) {
            internalRequest.addParameter("merchantFlowId", PrimitiveMarshallerUtils.marshalling(request.getMerchantFlowId(), "String"));
        }
        if (request.getPinFreeAmount() != null) {
            internalRequest.addParameter("pinFreeAmount", PrimitiveMarshallerUtils.marshalling(request.getPinFreeAmount(), "BigDecimal"));
        }
        if (request.getMaxAmont() != null) {
            internalRequest.addParameter("maxAmont", PrimitiveMarshallerUtils.marshalling(request.getMaxAmont(), "BigDecimal"));
        }
        if (request.getNotifyUrl() != null) {
            internalRequest.addParameter("notifyUrl", PrimitiveMarshallerUtils.marshalling(request.getNotifyUrl(), "String"));
        }
        if (request.getValidNotifyUrl() != null) {
            internalRequest.addParameter("validNotifyUrl", PrimitiveMarshallerUtils.marshalling(request.getValidNotifyUrl(), "String"));
        }
        if (request.getExpireTime() != null) {
            internalRequest.addParameter("expireTime", PrimitiveMarshallerUtils.marshalling(request.getExpireTime(), "Integer"));
        }
        if (request.getBindId() != null) {
            internalRequest.addParameter("bindId", PrimitiveMarshallerUtils.marshalling(request.getBindId(), "Long"));
        }
        if (request.getUserNo() != null) {
            internalRequest.addParameter("userNo", PrimitiveMarshallerUtils.marshalling(request.getUserNo(), "String"));
        }
        if (request.getUserType() != null) {
            internalRequest.addParameter("userType", PrimitiveMarshallerUtils.marshalling(request.getUserType(), "String"));
        }
        if (request.getCvv() != null) {
            internalRequest.addParameter("cvv", PrimitiveMarshallerUtils.marshalling(request.getCvv(), "String"));
        }
        if (request.getValid() != null) {
            internalRequest.addParameter("valid", PrimitiveMarshallerUtils.marshalling(request.getValid(), "String"));
        }
        if (request.getCouponInfo() != null) {
            internalRequest.addParameter("couponInfo", PrimitiveMarshallerUtils.marshalling(request.getCouponInfo(), "String"));
        }
        if (request.getDeviceID() != null) {
            internalRequest.addParameter("deviceID", PrimitiveMarshallerUtils.marshalling(request.getDeviceID(), "String"));
        }
        if (request.getDeviceType() != null) {
            internalRequest.addParameter("deviceType", PrimitiveMarshallerUtils.marshalling(request.getDeviceType(), "String"));
        }
        if (request.getAccountIDHash() != null) {
            internalRequest.addParameter("accountIDHash", PrimitiveMarshallerUtils.marshalling(request.getAccountIDHash(), "String"));
        }
        if (request.getSourceIP() != null) {
            internalRequest.addParameter("sourceIP", PrimitiveMarshallerUtils.marshalling(request.getSourceIP(), "String"));
        }
        if (request.getRiskInfoMobile() != null) {
            internalRequest.addParameter("riskInfoMobile", PrimitiveMarshallerUtils.marshalling(request.getRiskInfoMobile(), "String"));
        }
        if (request.getUsrRgstrDt() != null) {
            internalRequest.addParameter("usrRgstrDt", PrimitiveMarshallerUtils.marshalling(request.getUsrRgstrDt(), "String"));
        }
        if (request.getAccountEmailLife() != null) {
            internalRequest.addParameter("accountEmailLife", PrimitiveMarshallerUtils.marshalling(request.getAccountEmailLife(), "String"));
        }
        if (request.getDeviceLocation() != null) {
            internalRequest.addParameter("deviceLocation", PrimitiveMarshallerUtils.marshalling(request.getDeviceLocation(), "String"));
        }
        if (request.getFullDeviceNumber() != null) {
            internalRequest.addParameter("fullDeviceNumber", PrimitiveMarshallerUtils.marshalling(request.getFullDeviceNumber(), "String"));
        }
        if (request.getCaptureMethod() != null) {
            internalRequest.addParameter("captureMethod", PrimitiveMarshallerUtils.marshalling(request.getCaptureMethod(), "String"));
        }
        if (request.getDeviceSimNumber() != null) {
            internalRequest.addParameter("deviceSimNumber", PrimitiveMarshallerUtils.marshalling(request.getDeviceSimNumber(), "String"));
        }
        if (request.getDeviceName() != null) {
            internalRequest.addParameter("deviceName", PrimitiveMarshallerUtils.marshalling(request.getDeviceName(), "String"));
        }
        internalRequest.setContentType(YopContentType.FORM_URL_ENCODE);
        internalRequest.addHeader(Headers.CONTENT_TYPE, internalRequest.getContentType().getValue());
        super.marshall(internalRequest, request.get_extParamMap());
        return internalRequest;
    }


    private static class CacheInstanceHolder {
        public static UpopPassivescanBindQrcodeRequestMarshaller INSTANCE = new UpopPassivescanBindQrcodeRequestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static UpopPassivescanBindQrcodeRequestMarshaller getInstance() {
        return CacheInstanceHolder.INSTANCE;
    }
}
