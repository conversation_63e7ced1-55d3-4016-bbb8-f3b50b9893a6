/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.travel_resources.model.QueryKfcOptiionItemsBean;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * kfc商品信息
 */
public class QueryKfcItemsBean implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;pre&gt;商品id&lt;/pre&gt;
   */
  @JsonProperty("productId")
  private String productId = null;

  /**
   * &lt;pre&gt;商品名称&lt;/pre&gt;
   */
  @JsonProperty("productName")
  private String productName = null;

  /**
   * &lt;pre&gt;数量&lt;/pre&gt;
   */
  @JsonProperty("quantity")
  private BigDecimal quantity = null;

  /**
   * &lt;pre&gt;价格&lt;/pre&gt;
   */
  @JsonProperty("price")
  private BigDecimal price = null;

  /**
   * &lt;pre&gt;图片地址&lt;/pre&gt;
   */
  @JsonProperty("imageUrl")
  private String imageUrl = null;

  /**
   * &lt;pre&gt;是否已取消&lt;/pre&gt;
   */
  @JsonProperty("canceled")
  private Boolean canceled = null;

  /**
   * &lt;pre&gt;明细&lt;/pre&gt;
   */
  @JsonProperty("optionItems")
  private List<QueryKfcOptiionItemsBean> optionItems = null;

  public QueryKfcItemsBean productId(String productId) {
    this.productId = productId;
    return this;
  }

   /**
   * &lt;pre&gt;商品id&lt;/pre&gt;
   * @return productId
  **/

  public String getProductId() {
    return productId;
  }

  public void setProductId(String productId) {
    this.productId = productId;
  }

  public QueryKfcItemsBean productName(String productName) {
    this.productName = productName;
    return this;
  }

   /**
   * &lt;pre&gt;商品名称&lt;/pre&gt;
   * @return productName
  **/

  public String getProductName() {
    return productName;
  }

  public void setProductName(String productName) {
    this.productName = productName;
  }

  public QueryKfcItemsBean quantity(BigDecimal quantity) {
    this.quantity = quantity;
    return this;
  }

   /**
   * &lt;pre&gt;数量&lt;/pre&gt;
   * @return quantity
  **/

  public BigDecimal getQuantity() {
    return quantity;
  }

  public void setQuantity(BigDecimal quantity) {
    this.quantity = quantity;
  }

  public QueryKfcItemsBean price(BigDecimal price) {
    this.price = price;
    return this;
  }

   /**
   * &lt;pre&gt;价格&lt;/pre&gt;
   * @return price
  **/

  public BigDecimal getPrice() {
    return price;
  }

  public void setPrice(BigDecimal price) {
    this.price = price;
  }

  public QueryKfcItemsBean imageUrl(String imageUrl) {
    this.imageUrl = imageUrl;
    return this;
  }

   /**
   * &lt;pre&gt;图片地址&lt;/pre&gt;
   * @return imageUrl
  **/

  public String getImageUrl() {
    return imageUrl;
  }

  public void setImageUrl(String imageUrl) {
    this.imageUrl = imageUrl;
  }

  public QueryKfcItemsBean canceled(Boolean canceled) {
    this.canceled = canceled;
    return this;
  }

   /**
   * &lt;pre&gt;是否已取消&lt;/pre&gt;
   * @return canceled
  **/

  public Boolean isCanceled() {
    return canceled;
  }

  public void setCanceled(Boolean canceled) {
    this.canceled = canceled;
  }

  public QueryKfcItemsBean optionItems(List<QueryKfcOptiionItemsBean> optionItems) {
    this.optionItems = optionItems;
    return this;
  }

  public QueryKfcItemsBean addOptionItemsItem(QueryKfcOptiionItemsBean optionItemsItem) {
    if (this.optionItems == null) {
      this.optionItems = new ArrayList<>();
    }
    this.optionItems.add(optionItemsItem);
    return this;
  }

   /**
   * &lt;pre&gt;明细&lt;/pre&gt;
   * @return optionItems
  **/

  public List<QueryKfcOptiionItemsBean> getOptionItems() {
    return optionItems;
  }

  public void setOptionItems(List<QueryKfcOptiionItemsBean> optionItems) {
    this.optionItems = optionItems;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QueryKfcItemsBean queryKfcItemsBean = (QueryKfcItemsBean) o;
    return ObjectUtils.equals(this.productId, queryKfcItemsBean.productId) &&
    ObjectUtils.equals(this.productName, queryKfcItemsBean.productName) &&
    ObjectUtils.equals(this.quantity, queryKfcItemsBean.quantity) &&
    ObjectUtils.equals(this.price, queryKfcItemsBean.price) &&
    ObjectUtils.equals(this.imageUrl, queryKfcItemsBean.imageUrl) &&
    ObjectUtils.equals(this.canceled, queryKfcItemsBean.canceled) &&
    ObjectUtils.equals(this.optionItems, queryKfcItemsBean.optionItems);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(productId, productName, quantity, price, imageUrl, canceled, optionItems);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryKfcItemsBean {\n");
    
    sb.append("    productId: ").append(toIndentedString(productId)).append("\n");
    sb.append("    productName: ").append(toIndentedString(productName)).append("\n");
    sb.append("    quantity: ").append(toIndentedString(quantity)).append("\n");
    sb.append("    price: ").append(toIndentedString(price)).append("\n");
    sb.append("    imageUrl: ").append(toIndentedString(imageUrl)).append("\n");
    sb.append("    canceled: ").append(toIndentedString(canceled)).append("\n");
    sb.append("    optionItems: ").append(toIndentedString(optionItems)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

