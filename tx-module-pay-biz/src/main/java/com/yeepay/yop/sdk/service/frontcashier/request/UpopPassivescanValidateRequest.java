/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class UpopPassivescanValidateRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String merchantFlowId;

    private String payOrderNo;

    private String couponInfo;

    private BigDecimal realTradeAmount;


    /**
     * Get merchantFlowId
     * @return merchantFlowId
     **/
    
    public String getMerchantFlowId() {
        return merchantFlowId;
    }

    public void setMerchantFlowId(String merchantFlowId) {
        this.merchantFlowId = merchantFlowId;
    }

    /**
     * Get payOrderNo
     * @return payOrderNo
     **/
    
    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    /**
     * Get couponInfo
     * @return couponInfo
     **/
    
    public String getCouponInfo() {
        return couponInfo;
    }

    public void setCouponInfo(String couponInfo) {
        this.couponInfo = couponInfo;
    }

    /**
     * Get realTradeAmount
     * @return realTradeAmount
     **/
    
    public BigDecimal getRealTradeAmount() {
        return realTradeAmount;
    }

    public void setRealTradeAmount(BigDecimal realTradeAmount) {
        this.realTradeAmount = realTradeAmount;
    }

    @Override
    public String getOperationId() {
        return "upopPassivescanValidate";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
