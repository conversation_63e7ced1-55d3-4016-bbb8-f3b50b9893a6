/*
 * 公益-月捐
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.month_donate.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 更改月捐
 */
public class ChangeMonthDonateRequestDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;月捐金额&lt;/p&gt;
   */
  @JsonProperty("amount")
  private BigDecimal amount = null;

  /**
   * &lt;p&gt;绑卡ID&lt;/p&gt;
   */
  @JsonProperty("userCardId")
  private Long userCardId = null;

  /**
   * &lt;p&gt;月捐类型&lt;/p&gt; &lt;div&gt; &lt;pre&gt;WECHAT_WITHHOLDING 微信代扣&lt;/pre&gt; &lt;div&gt; &lt;pre&gt;BIND_CARD 绑卡&lt;/pre&gt; &lt;/div&gt; &lt;/div&gt;
   */
  @JsonProperty("monthType")
  private String monthType = null;

  /**
   * &lt;p&gt;项目ID&lt;/p&gt;
   */
  @JsonProperty("projectId")
  private Long projectId = null;

  /**
   * &lt;p&gt;用户ID&lt;/p&gt;
   */
  @JsonProperty("userId")
  private Long userId = null;

  /**
   * &lt;p&gt;商户编号&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  public ChangeMonthDonateRequestDTO amount(BigDecimal amount) {
    this.amount = amount;
    return this;
  }

   /**
   * &lt;p&gt;月捐金额&lt;/p&gt;
   * minimum: 0
   * @return amount
  **/

  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public ChangeMonthDonateRequestDTO userCardId(Long userCardId) {
    this.userCardId = userCardId;
    return this;
  }

   /**
   * &lt;p&gt;绑卡ID&lt;/p&gt;
   * @return userCardId
  **/

  public Long getUserCardId() {
    return userCardId;
  }

  public void setUserCardId(Long userCardId) {
    this.userCardId = userCardId;
  }

  public ChangeMonthDonateRequestDTO monthType(String monthType) {
    this.monthType = monthType;
    return this;
  }

   /**
   * &lt;p&gt;月捐类型&lt;/p&gt; &lt;div&gt; &lt;pre&gt;WECHAT_WITHHOLDING 微信代扣&lt;/pre&gt; &lt;div&gt; &lt;pre&gt;BIND_CARD 绑卡&lt;/pre&gt; &lt;/div&gt; &lt;/div&gt;
   * @return monthType
  **/

  public String getMonthType() {
    return monthType;
  }

  public void setMonthType(String monthType) {
    this.monthType = monthType;
  }

  public ChangeMonthDonateRequestDTO projectId(Long projectId) {
    this.projectId = projectId;
    return this;
  }

   /**
   * &lt;p&gt;项目ID&lt;/p&gt;
   * @return projectId
  **/

  public Long getProjectId() {
    return projectId;
  }

  public void setProjectId(Long projectId) {
    this.projectId = projectId;
  }

  public ChangeMonthDonateRequestDTO userId(Long userId) {
    this.userId = userId;
    return this;
  }

   /**
   * &lt;p&gt;用户ID&lt;/p&gt;
   * @return userId
  **/

  public Long getUserId() {
    return userId;
  }

  public void setUserId(Long userId) {
    this.userId = userId;
  }

  public ChangeMonthDonateRequestDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;商户编号&lt;/p&gt;
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    ChangeMonthDonateRequestDTO changeMonthDonateRequestDTO = (ChangeMonthDonateRequestDTO) o;
    return ObjectUtils.equals(this.amount, changeMonthDonateRequestDTO.amount) &&
    ObjectUtils.equals(this.userCardId, changeMonthDonateRequestDTO.userCardId) &&
    ObjectUtils.equals(this.monthType, changeMonthDonateRequestDTO.monthType) &&
    ObjectUtils.equals(this.projectId, changeMonthDonateRequestDTO.projectId) &&
    ObjectUtils.equals(this.userId, changeMonthDonateRequestDTO.userId) &&
    ObjectUtils.equals(this.merchantNo, changeMonthDonateRequestDTO.merchantNo);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(amount, userCardId, monthType, projectId, userId, merchantNo);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ChangeMonthDonateRequestDTO {\n");
    
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("    userCardId: ").append(toIndentedString(userCardId)).append("\n");
    sb.append("    monthType: ").append(toIndentedString(monthType)).append("\n");
    sb.append("    projectId: ").append(toIndentedString(projectId)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

