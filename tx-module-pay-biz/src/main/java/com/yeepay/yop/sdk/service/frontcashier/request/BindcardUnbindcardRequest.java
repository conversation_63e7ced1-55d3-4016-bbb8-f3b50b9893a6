/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class BindcardUnbindcardRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String merchantNo;

    private String userNo;

    private String userType;

    private String bindId;

    private String bankCardNoFirst6;

    private String bankCardNoLast4;

    private String bankCardNo;


    /**
     * Get merchantNo
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    /**
     * Get userNo
     * @return userNo
     **/
    
    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    /**
     * Get userType
     * @return userType
     **/
    
    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    /**
     * Get bindId
     * @return bindId
     **/
    
    public String getBindId() {
        return bindId;
    }

    public void setBindId(String bindId) {
        this.bindId = bindId;
    }

    /**
     * Get bankCardNoFirst6
     * @return bankCardNoFirst6
     **/
    
    public String getBankCardNoFirst6() {
        return bankCardNoFirst6;
    }

    public void setBankCardNoFirst6(String bankCardNoFirst6) {
        this.bankCardNoFirst6 = bankCardNoFirst6;
    }

    /**
     * Get bankCardNoLast4
     * @return bankCardNoLast4
     **/
    
    public String getBankCardNoLast4() {
        return bankCardNoLast4;
    }

    public void setBankCardNoLast4(String bankCardNoLast4) {
        this.bankCardNoLast4 = bankCardNoLast4;
    }

    /**
     * Get bankCardNo
     * @return bankCardNo
     **/
    
    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    @Override
    public String getOperationId() {
        return "bindcardUnbindcard";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
