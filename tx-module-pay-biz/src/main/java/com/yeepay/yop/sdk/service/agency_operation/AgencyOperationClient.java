/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.agency_operation;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.agency_operation.request.*;
import com.yeepay.yop.sdk.service.agency_operation.response.*;

public interface AgencyOperationClient {

    /**
     * 代运营服务商抽佣绑定门店
     *  
     * @return ShopBindResponse
     * @throws YopClientException if fails to make API call
     */
    ShopBindResponse shopBind(ShopBindRequest request) throws YopClientException;

    /**
     * 代运营服务商抽佣绑定门店结果查询
     * 
     * @return ShopBindQueryResponse
     * @throws YopClientException if fails to make API call
     */
    ShopBindQueryResponse shopBindQuery(ShopBindQueryRequest request) throws YopClientException;

    /**
     * 代运营服务商抽佣
     *  
     * @return WithholdResponse
     * @throws YopClientException if fails to make API call
     */
    WithholdResponse withhold(WithholdRequest request) throws YopClientException;

    /**
     * 代运营服务商抽佣记录查询
     * 
     * @return WithholdQueryResponse
     * @throws YopClientException if fails to make API call
     */
    WithholdQueryResponse withholdQuery(WithholdQueryRequest request) throws YopClientException;

    /**
     * 代运营服务商抽佣记录查询
     * 
     * @return WithholdRecordQueryV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    WithholdRecordQueryV10Response withhold_record_query_v1_0(WithholdRecordQueryV10Request request) throws YopClientException;

    /**
     * 代运营服务商抽佣绑定门店结果查询
     * 
     * @return WithholdShopBindQueryV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    WithholdShopBindQueryV10Response withhold_shop_bind_query_v1_0(WithholdShopBindQueryV10Request request) throws YopClientException;

    /**
     * 代运营服务商抽佣绑定门店
     *  
     * @return WithholdShopBindV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    WithholdShopBindV10Response withhold_shop_bind_v1_0(WithholdShopBindV10Request request) throws YopClientException;

    /**
     * 代运营服务商抽佣
     *  
     * @return WithholdV10Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    WithholdV10Response withhold_v1_0(WithholdV10Request request) throws YopClientException;


    /**
     * Shuts down this client object, releasing any resources that might be held open. This is an optional method, and
     * callers are not expected to call it, but can if they want to explicitly release any open resources. Once a client
     * has been shutdown, it should not be used to make any more requests.
     */
    void shutdown();
}
