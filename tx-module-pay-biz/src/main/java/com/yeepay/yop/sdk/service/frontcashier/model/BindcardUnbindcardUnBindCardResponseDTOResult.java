/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * BindcardUnbindcardUnBindCardResponseDTOResult
 */
public class BindcardUnbindcardUnBindCardResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 响应描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 用户标识
   */
  @JsonProperty("userNo")
  private String userNo = null;

  /**
   * 用户标识类型
   */
  @JsonProperty("userType")
  private String userType = null;

  /**
   * 绑卡id
   */
  @JsonProperty("bindId")
  private String bindId = null;

  public BindcardUnbindcardUnBindCardResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BindcardUnbindcardUnBindCardResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 响应描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public BindcardUnbindcardUnBindCardResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public BindcardUnbindcardUnBindCardResponseDTOResult userNo(String userNo) {
    this.userNo = userNo;
    return this;
  }

   /**
   * 用户标识
   * @return userNo
  **/

  public String getUserNo() {
    return userNo;
  }

  public void setUserNo(String userNo) {
    this.userNo = userNo;
  }

  public BindcardUnbindcardUnBindCardResponseDTOResult userType(String userType) {
    this.userType = userType;
    return this;
  }

   /**
   * 用户标识类型
   * @return userType
  **/

  public String getUserType() {
    return userType;
  }

  public void setUserType(String userType) {
    this.userType = userType;
  }

  public BindcardUnbindcardUnBindCardResponseDTOResult bindId(String bindId) {
    this.bindId = bindId;
    return this;
  }

   /**
   * 绑卡id
   * @return bindId
  **/

  public String getBindId() {
    return bindId;
  }

  public void setBindId(String bindId) {
    this.bindId = bindId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BindcardUnbindcardUnBindCardResponseDTOResult bindcardUnbindcardUnBindCardResponseDTOResult = (BindcardUnbindcardUnBindCardResponseDTOResult) o;
    return ObjectUtils.equals(this.code, bindcardUnbindcardUnBindCardResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, bindcardUnbindcardUnBindCardResponseDTOResult.message) &&
    ObjectUtils.equals(this.merchantNo, bindcardUnbindcardUnBindCardResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.userNo, bindcardUnbindcardUnBindCardResponseDTOResult.userNo) &&
    ObjectUtils.equals(this.userType, bindcardUnbindcardUnBindCardResponseDTOResult.userType) &&
    ObjectUtils.equals(this.bindId, bindcardUnbindcardUnBindCardResponseDTOResult.bindId);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, merchantNo, userNo, userType, bindId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindcardUnbindcardUnBindCardResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    userNo: ").append(toIndentedString(userNo)).append("\n");
    sb.append("    userType: ").append(toIndentedString(userType)).append("\n");
    sb.append("    bindId: ").append(toIndentedString(bindId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

