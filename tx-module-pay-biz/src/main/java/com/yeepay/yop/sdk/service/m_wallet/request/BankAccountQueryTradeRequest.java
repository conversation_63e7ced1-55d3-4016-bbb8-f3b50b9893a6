/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class BankAccountQueryTradeRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String merchantMemberNo;

    private String accountNo;

    private String beginDate;

    private String endDate;

    private String parentMerchantNo;

    private String merchantNo;

    private String loanFlag;

    private Integer startNum;

    private Integer queryNum;

    private String queryToken;

    private String queryTime;


    /**
     * 商户侧存的会员编号，不同人的会员编号不能相同
     * @return merchantMemberNo
     **/
    
    public String getMerchantMemberNo() {
        return merchantMemberNo;
    }

    public void setMerchantMemberNo(String merchantMemberNo) {
        this.merchantMemberNo = merchantMemberNo;
    }

    /**
     * 电子账号
     * @return accountNo
     **/
    
    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    /**
     * 日期参数格式（yyyyMMdd）&lt;br&gt;起始日期和截止日期间隔不能超过6个月
     * @return beginDate
     **/
    
    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    /**
     * 日期参数格式（yyyyMMdd）&lt;br&gt;起始日期和截止日期间隔不能超过6个月
     * @return endDate
     **/
    
    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    /**
     * 发起方商户编号&lt;br&gt;（标准商户收付款方案中此参数与商编一致，平台商户收付款方案中此参数为平台商商户编号）
     * @return parentMerchantNo
     **/
    
    public String getParentMerchantNo() {
        return parentMerchantNo;
    }

    public void setParentMerchantNo(String parentMerchantNo) {
        this.parentMerchantNo = parentMerchantNo;
    }

    /**
     * 商户编号&lt;br&gt;易宝支付分配的的商户唯一标识
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    /**
     * 可选项如下:&lt;br&gt;INCOME:收入&lt;br&gt;PAY:支出
     * @return loanFlag
     **/
    
    public String getLoanFlag() {
        return loanFlag;
    }

    public void setLoanFlag(String loanFlag) {
        this.loanFlag = loanFlag;
    }

    /**
     * 按此值查询其后交易流水，不填的话，默认是1。传1是首次查询，传其他值是非首次查询，非首次查询需要传queryToken和queryTime
     * @return startNum
     **/
    
    public Integer getStartNum() {
        return startNum;
    }

    public void setStartNum(Integer startNum) {
        this.startNum = startNum;
    }

    /**
     * 不填的话默认是10，最大条数99
     * @return queryNum
     **/
    
    public Integer getQueryNum() {
        return queryNum;
    }

    public void setQueryNum(Integer queryNum) {
        this.queryNum = queryNum;
    }

    /**
     * 查询token&lt;br&gt;首次查询不用传，非首次查询传上次查询的返回queryToken
     * @return queryToken
     **/
    
    public String getQueryToken() {
        return queryToken;
    }

    public void setQueryToken(String queryToken) {
        this.queryToken = queryToken;
    }

    /**
     * 首次查询不用传，非首次查询传上次查询的返回值queryTime
     * @return queryTime
     **/
    
    public String getQueryTime() {
        return queryTime;
    }

    public void setQueryTime(String queryTime) {
        this.queryTime = queryTime;
    }

    @Override
    public String getOperationId() {
        return "bankAccountQueryTrade";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
