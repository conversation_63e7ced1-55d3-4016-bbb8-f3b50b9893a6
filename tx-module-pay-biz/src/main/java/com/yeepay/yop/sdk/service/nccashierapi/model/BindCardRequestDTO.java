/*
 * API收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.nccashierapi.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yeepay.yop.sdk.service.base.request.serializer.YopCustomSerializer;
import org.joda.time.LocalDate;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 绑卡收银台绑卡请求
 */
public class BindCardRequestDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;系统商或者平台商商编，如果是单商户，和收单商户商编保持一致&lt;/p&gt;
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * &lt;p&gt;收单商户编号&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;p&gt;商户绑卡唯一流水号，需保证唯一&lt;/p&gt;
   */
  @JsonProperty("merchantFlowId")
  private String merchantFlowId = null;

  /**
   * &lt;p&gt;必须保证每个支付用户唯一，绑卡的标识性数据。形成绑卡关系后，卡会绑定在这个用户标识下。&lt;br /&gt;如子商户性质为个人或个体工商户，则 userNo 必须与 merchantNo 一致。&lt;/p&gt;
   */
  @JsonProperty("userNo")
  private String userNo = null;

  /**
   *  可选项如下: USER_ID:用户ID WECHAT:微信号 PHONE:用户注册手机号 ID_CARD:用户身份证号 IMEI:imei MAC:网卡地址 EMAIL:用户注册email AGREEMENT_NO:用户纸质订单协议号 
   */
  public enum UserTypeEnum {
    USER_ID("USER_ID"),
    
    WECHAT("WECHAT"),
    
    PHONE("PHONE"),
    
    ID_CARD("ID_CARD"),
    
    IMEI("IMEI"),
    
    MAC("MAC"),
    
    EMAIL("EMAIL"),
    
    AGREEMENT_NO("AGREEMENT_NO");

    private String value;

    UserTypeEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static UserTypeEnum fromValue(String text) {
      for (UserTypeEnum b : UserTypeEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      return null;
    }
  }

  /**
   *  可选项如下: USER_ID:用户ID WECHAT:微信号 PHONE:用户注册手机号 ID_CARD:用户身份证号 IMEI:imei MAC:网卡地址 EMAIL:用户注册email AGREEMENT_NO:用户纸质订单协议号 
   */
  @JsonProperty("userType")
  private UserTypeEnum userType = null;

  /**
   * &lt;p&gt;请求过期时间，格式:yyyy-MM-dd HH:mm:ss&lt;/p&gt;
   */
  @JsonProperty("expireTime")
  private LocalDate expireTime = null;

  /**
   * &lt;p&gt;绑卡成功或失败后回跳商户应用的页面跳转地址&lt;/p&gt;
   */
  @JsonProperty("pageReturnUrl")
  private String pageReturnUrl = null;

  /**
   * &lt;p&gt;用户绑卡成功或超时通知商户结果地址&lt;/p&gt;
   */
  @JsonProperty("bindNotifyUrl")
  private String bindNotifyUrl = null;

  public BindCardRequestDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;系统商或者平台商商编，如果是单商户，和收单商户商编保持一致&lt;/p&gt;
   * @return parentMerchantNo
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public BindCardRequestDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;收单商户编号&lt;/p&gt;
   * @return merchantNo
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public BindCardRequestDTO merchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
    return this;
  }

   /**
   * &lt;p&gt;商户绑卡唯一流水号，需保证唯一&lt;/p&gt;
   * @return merchantFlowId
  **/

  public String getMerchantFlowId() {
    return merchantFlowId;
  }

  public void setMerchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
  }

  public BindCardRequestDTO userNo(String userNo) {
    this.userNo = userNo;
    return this;
  }

   /**
   * &lt;p&gt;必须保证每个支付用户唯一，绑卡的标识性数据。形成绑卡关系后，卡会绑定在这个用户标识下。&lt;br /&gt;如子商户性质为个人或个体工商户，则 userNo 必须与 merchantNo 一致。&lt;/p&gt;
   * @return userNo
  **/

  public String getUserNo() {
    return userNo;
  }

  public void setUserNo(String userNo) {
    this.userNo = userNo;
  }

  public BindCardRequestDTO userType(UserTypeEnum userType) {
    this.userType = userType;
    return this;
  }

   /**
   *  可选项如下: USER_ID:用户ID WECHAT:微信号 PHONE:用户注册手机号 ID_CARD:用户身份证号 IMEI:imei MAC:网卡地址 EMAIL:用户注册email AGREEMENT_NO:用户纸质订单协议号 
   * @return userType
  **/

  public UserTypeEnum getUserType() {
    return userType;
  }

  public void setUserType(UserTypeEnum userType) {
    this.userType = userType;
  }

  public BindCardRequestDTO expireTime(LocalDate expireTime) {
    this.expireTime = expireTime;
    return this;
  }

   /**
   * &lt;p&gt;请求过期时间，格式:yyyy-MM-dd HH:mm:ss&lt;/p&gt;
   * @return expireTime
  **/

  public LocalDate getExpireTime() {
    return expireTime;
  }

  public void setExpireTime(LocalDate expireTime) {
    this.expireTime = expireTime;
  }

  public BindCardRequestDTO pageReturnUrl(String pageReturnUrl) {
    this.pageReturnUrl = pageReturnUrl;
    return this;
  }

   /**
   * &lt;p&gt;绑卡成功或失败后回跳商户应用的页面跳转地址&lt;/p&gt;
   * @return pageReturnUrl
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getPageReturnUrl() {
    return pageReturnUrl;
  }

  public void setPageReturnUrl(String pageReturnUrl) {
    this.pageReturnUrl = pageReturnUrl;
  }

  public BindCardRequestDTO bindNotifyUrl(String bindNotifyUrl) {
    this.bindNotifyUrl = bindNotifyUrl;
    return this;
  }

   /**
   * &lt;p&gt;用户绑卡成功或超时通知商户结果地址&lt;/p&gt;
   * @return bindNotifyUrl
  **/

  @JsonSerialize(using = YopCustomSerializer.class)
  public String getBindNotifyUrl() {
    return bindNotifyUrl;
  }

  public void setBindNotifyUrl(String bindNotifyUrl) {
    this.bindNotifyUrl = bindNotifyUrl;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BindCardRequestDTO bindCardRequestDTO = (BindCardRequestDTO) o;
    return ObjectUtils.equals(this.parentMerchantNo, bindCardRequestDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, bindCardRequestDTO.merchantNo) &&
    ObjectUtils.equals(this.merchantFlowId, bindCardRequestDTO.merchantFlowId) &&
    ObjectUtils.equals(this.userNo, bindCardRequestDTO.userNo) &&
    ObjectUtils.equals(this.userType, bindCardRequestDTO.userType) &&
    ObjectUtils.equals(this.expireTime, bindCardRequestDTO.expireTime) &&
    ObjectUtils.equals(this.pageReturnUrl, bindCardRequestDTO.pageReturnUrl) &&
    ObjectUtils.equals(this.bindNotifyUrl, bindCardRequestDTO.bindNotifyUrl);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(parentMerchantNo, merchantNo, merchantFlowId, userNo, userType, expireTime, pageReturnUrl, bindNotifyUrl);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindCardRequestDTO {\n");
    
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantFlowId: ").append(toIndentedString(merchantFlowId)).append("\n");
    sb.append("    userNo: ").append(toIndentedString(userNo)).append("\n");
    sb.append("    userType: ").append(toIndentedString(userType)).append("\n");
    sb.append("    expireTime: ").append(toIndentedString(expireTime)).append("\n");
    sb.append("    pageReturnUrl: ").append(toIndentedString(pageReturnUrl)).append("\n");
    sb.append("    bindNotifyUrl: ").append(toIndentedString(bindNotifyUrl)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

