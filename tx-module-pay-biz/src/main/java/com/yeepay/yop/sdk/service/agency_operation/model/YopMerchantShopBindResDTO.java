/*
 * 代运营服务
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.agency_operation.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 请修改我
 */
public class YopMerchantShopBindResDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;绑定状态&lt;/p&gt; &lt;p&gt;PROCESSING ： 处理中&lt;/p&gt; &lt;p&gt;FAIL ：失败&lt;/p&gt; &lt;p&gt;SUCCESS : 成功&lt;/p&gt;
   */
  @JsonProperty("bindStatus")
  private String bindStatus = null;

  /**
   * &lt;p&gt;绑定链接&lt;/p&gt;
   */
  @JsonProperty("bindUrl")
  private String bindUrl = null;

  public YopMerchantShopBindResDTO bindStatus(String bindStatus) {
    this.bindStatus = bindStatus;
    return this;
  }

   /**
   * &lt;p&gt;绑定状态&lt;/p&gt; &lt;p&gt;PROCESSING ： 处理中&lt;/p&gt; &lt;p&gt;FAIL ：失败&lt;/p&gt; &lt;p&gt;SUCCESS : 成功&lt;/p&gt;
   * @return bindStatus
  **/

  public String getBindStatus() {
    return bindStatus;
  }

  public void setBindStatus(String bindStatus) {
    this.bindStatus = bindStatus;
  }

  public YopMerchantShopBindResDTO bindUrl(String bindUrl) {
    this.bindUrl = bindUrl;
    return this;
  }

   /**
   * &lt;p&gt;绑定链接&lt;/p&gt;
   * @return bindUrl
  **/

  public String getBindUrl() {
    return bindUrl;
  }

  public void setBindUrl(String bindUrl) {
    this.bindUrl = bindUrl;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    YopMerchantShopBindResDTO yopMerchantShopBindResDTO = (YopMerchantShopBindResDTO) o;
    return ObjectUtils.equals(this.bindStatus, yopMerchantShopBindResDTO.bindStatus) &&
    ObjectUtils.equals(this.bindUrl, yopMerchantShopBindResDTO.bindUrl);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(bindStatus, bindUrl);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class YopMerchantShopBindResDTO {\n");
    
    sb.append("    bindStatus: ").append(toIndentedString(bindStatus)).append("\n");
    sb.append("    bindUrl: ").append(toIndentedString(bindUrl)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

