/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.promtion.model.ActivityListQueryActivityQueryInfoResult;
import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * ActivityListQueryActivityPageListQueryResponseResult
 */
public class ActivityListQueryActivityPageListQueryResponseResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 响应码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 响应描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 活动列表
   */
  @JsonProperty("activityList")
  private List<ActivityListQueryActivityQueryInfoResult> activityList = null;

  public ActivityListQueryActivityPageListQueryResponseResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 响应码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public ActivityListQueryActivityPageListQueryResponseResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 响应描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public ActivityListQueryActivityPageListQueryResponseResult activityList(List<ActivityListQueryActivityQueryInfoResult> activityList) {
    this.activityList = activityList;
    return this;
  }

  public ActivityListQueryActivityPageListQueryResponseResult addActivityListItem(ActivityListQueryActivityQueryInfoResult activityListItem) {
    if (this.activityList == null) {
      this.activityList = new ArrayList<>();
    }
    this.activityList.add(activityListItem);
    return this;
  }

   /**
   * 活动列表
   * @return activityList
  **/

  public List<ActivityListQueryActivityQueryInfoResult> getActivityList() {
    return activityList;
  }

  public void setActivityList(List<ActivityListQueryActivityQueryInfoResult> activityList) {
    this.activityList = activityList;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    ActivityListQueryActivityPageListQueryResponseResult activityListQueryActivityPageListQueryResponseResult = (ActivityListQueryActivityPageListQueryResponseResult) o;
    return ObjectUtils.equals(this.code, activityListQueryActivityPageListQueryResponseResult.code) &&
    ObjectUtils.equals(this.message, activityListQueryActivityPageListQueryResponseResult.message) &&
    ObjectUtils.equals(this.activityList, activityListQueryActivityPageListQueryResponseResult.activityList);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, activityList);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ActivityListQueryActivityPageListQueryResponseResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    activityList: ").append(toIndentedString(activityList)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

