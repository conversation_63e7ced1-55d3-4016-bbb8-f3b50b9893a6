/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * BindpayRequestApiBindPayPaymentResponseDTOResult
 */
public class BindpayRequestApiBindPayPaymentResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 结果码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 信息描述
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 订单token
   */
  @JsonProperty("token")
  private String token = null;

  /**
   * 绑卡ID
   */
  @JsonProperty("bindId")
  private String bindId = null;

  /**
   * 需补充项名称的 集合，以\&quot;,\&quot;分隔
   */
  @JsonProperty("needItems")
  private String needItems = null;

  /**
   * 验证码类型
   */
  @JsonProperty("verifyCodeType")
  private String verifyCodeType = null;

  /**
   * 提交补充项场景
   */
  @JsonProperty("supplyNeedItemScene")
  private String supplyNeedItemScene = null;

  /**
   * 支付记录ID
   */
  @JsonProperty("recordId")
  private String recordId = null;

  public BindpayRequestApiBindPayPaymentResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 结果码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BindpayRequestApiBindPayPaymentResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 信息描述
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public BindpayRequestApiBindPayPaymentResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public BindpayRequestApiBindPayPaymentResponseDTOResult token(String token) {
    this.token = token;
    return this;
  }

   /**
   * 订单token
   * @return token
  **/

  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  public BindpayRequestApiBindPayPaymentResponseDTOResult bindId(String bindId) {
    this.bindId = bindId;
    return this;
  }

   /**
   * 绑卡ID
   * @return bindId
  **/

  public String getBindId() {
    return bindId;
  }

  public void setBindId(String bindId) {
    this.bindId = bindId;
  }

  public BindpayRequestApiBindPayPaymentResponseDTOResult needItems(String needItems) {
    this.needItems = needItems;
    return this;
  }

   /**
   * 需补充项名称的 集合，以\&quot;,\&quot;分隔
   * @return needItems
  **/

  public String getNeedItems() {
    return needItems;
  }

  public void setNeedItems(String needItems) {
    this.needItems = needItems;
  }

  public BindpayRequestApiBindPayPaymentResponseDTOResult verifyCodeType(String verifyCodeType) {
    this.verifyCodeType = verifyCodeType;
    return this;
  }

   /**
   * 验证码类型
   * @return verifyCodeType
  **/

  public String getVerifyCodeType() {
    return verifyCodeType;
  }

  public void setVerifyCodeType(String verifyCodeType) {
    this.verifyCodeType = verifyCodeType;
  }

  public BindpayRequestApiBindPayPaymentResponseDTOResult supplyNeedItemScene(String supplyNeedItemScene) {
    this.supplyNeedItemScene = supplyNeedItemScene;
    return this;
  }

   /**
   * 提交补充项场景
   * @return supplyNeedItemScene
  **/

  public String getSupplyNeedItemScene() {
    return supplyNeedItemScene;
  }

  public void setSupplyNeedItemScene(String supplyNeedItemScene) {
    this.supplyNeedItemScene = supplyNeedItemScene;
  }

  public BindpayRequestApiBindPayPaymentResponseDTOResult recordId(String recordId) {
    this.recordId = recordId;
    return this;
  }

   /**
   * 支付记录ID
   * @return recordId
  **/

  public String getRecordId() {
    return recordId;
  }

  public void setRecordId(String recordId) {
    this.recordId = recordId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BindpayRequestApiBindPayPaymentResponseDTOResult bindpayRequestApiBindPayPaymentResponseDTOResult = (BindpayRequestApiBindPayPaymentResponseDTOResult) o;
    return ObjectUtils.equals(this.code, bindpayRequestApiBindPayPaymentResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, bindpayRequestApiBindPayPaymentResponseDTOResult.message) &&
    ObjectUtils.equals(this.merchantNo, bindpayRequestApiBindPayPaymentResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.token, bindpayRequestApiBindPayPaymentResponseDTOResult.token) &&
    ObjectUtils.equals(this.bindId, bindpayRequestApiBindPayPaymentResponseDTOResult.bindId) &&
    ObjectUtils.equals(this.needItems, bindpayRequestApiBindPayPaymentResponseDTOResult.needItems) &&
    ObjectUtils.equals(this.verifyCodeType, bindpayRequestApiBindPayPaymentResponseDTOResult.verifyCodeType) &&
    ObjectUtils.equals(this.supplyNeedItemScene, bindpayRequestApiBindPayPaymentResponseDTOResult.supplyNeedItemScene) &&
    ObjectUtils.equals(this.recordId, bindpayRequestApiBindPayPaymentResponseDTOResult.recordId);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, merchantNo, token, bindId, needItems, verifyCodeType, supplyNeedItemScene, recordId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindpayRequestApiBindPayPaymentResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    token: ").append(toIndentedString(token)).append("\n");
    sb.append("    bindId: ").append(toIndentedString(bindId)).append("\n");
    sb.append("    needItems: ").append(toIndentedString(needItems)).append("\n");
    sb.append("    verifyCodeType: ").append(toIndentedString(verifyCodeType)).append("\n");
    sb.append("    supplyNeedItemScene: ").append(toIndentedString(supplyNeedItemScene)).append("\n");
    sb.append("    recordId: ").append(toIndentedString(recordId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

