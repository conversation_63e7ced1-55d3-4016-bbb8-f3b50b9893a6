/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.travel_resources.model.QueryKfcPlaceOrderBean;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * Kfc订单信息
 */
public class QuerKfcOrderDataBeanDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;pre&gt;总价（元）&lt;/pre&gt;
   */
  @JsonProperty("totalPrice")
  private BigDecimal totalPrice = null;

  /**
   * &lt;pre&gt;市场价（元）&lt;/pre&gt;
   */
  @JsonProperty("marketUnitPrice")
  private BigDecimal marketUnitPrice = null;

  /**
   * &lt;pre&gt;退款金额（元）&lt;/pre&gt;
   */
  @JsonProperty("refundPrice")
  private BigDecimal refundPrice = null;

  /**
   * 
   */
  @JsonProperty("kfcPlaceOrder")
  private QueryKfcPlaceOrderBean kfcPlaceOrder = null;

  /**
   * &lt;pre&gt;单价(元)&lt;/pre&gt;
   */
  @JsonProperty("unitPrice")
  private BigDecimal unitPrice = null;

  /**
   * &lt;pre&gt;订单号&lt;/pre&gt;
   */
  @JsonProperty("orderNo")
  private String orderNo = null;

  /**
   * &lt;pre&gt;数量&lt;/pre&gt;
   */
  @JsonProperty("quantity")
  private Integer quantity = null;

  /**
   * &lt;pre&gt;取餐码&lt;/pre&gt;
   */
  @JsonProperty("ticket")
  private String ticket = null;

  /**
   * &lt;pre&gt;佣金（元），此字段在交易完成时才会有值&lt;/pre&gt;
   */
  @JsonProperty("commissionPrice")
  private BigDecimal commissionPrice = null;

  /**
   * &lt;pre&gt;用户id&lt;/pre&gt;
   */
  @JsonProperty("userId")
  private Integer userId = null;

  /**
   * &lt;pre&gt;用户备注&lt;/pre&gt;
   */
  @JsonProperty("userRemark")
  private String userRemark = null;

  /**
   * &lt;pre&gt;用户手机号码&lt;/pre&gt;
   */
  @JsonProperty("userMobile")
  private String userMobile = null;

  /**
   * &lt;pre&gt;取消时间&lt;/pre&gt;
   */
  @JsonProperty("cancelTime")
  private String cancelTime = null;

  /**
   * &lt;pre&gt;用户昵称&lt;/pre&gt;
   */
  @JsonProperty("userName")
  private String userName = null;

  /**
   * &lt;pre&gt;下单手机后4位（非用户手机号）&lt;/pre&gt;
   */
  @JsonProperty("kfcOrderMobileSuffix")
  private String kfcOrderMobileSuffix = null;

  /**
   * &lt;pre&gt;下单手机备注&lt;/pre&gt;
   */
  @JsonProperty("kfcOrderMobileRemark")
  private String kfcOrderMobileRemark = null;

  /**
   * &lt;pre&gt;平台用户唯一标识&lt;/pre&gt;
   */
  @JsonProperty("platformUniqueId")
  private String platformUniqueId = null;

  /**
   * &lt;pre&gt;是否外卖&lt;/pre&gt;
   */
  @JsonProperty("takeout")
  private Boolean takeout = null;

  /**
   * &lt;pre&gt;外送费(元)）&lt;/pre&gt;
   */
  @JsonProperty("takeoutPrice")
  private BigDecimal takeoutPrice = null;

  /**
   * &lt;pre&gt;外卖省份名称&lt;/pre&gt;
   */
  @JsonProperty("takeoutProvinceName")
  private String takeoutProvinceName = null;

  /**
   * &lt;pre&gt;外卖省份编号&lt;/pre&gt;
   */
  @JsonProperty("takeoutProvinceCode")
  private String takeoutProvinceCode = null;

  /**
   * &lt;pre&gt;外卖城市名称&lt;/pre&gt;
   */
  @JsonProperty("takeoutCityName")
  private String takeoutCityName = null;

  /**
   * &lt;pre&gt;外卖城市编号&lt;/pre&gt;
   */
  @JsonProperty("takeoutCityCode")
  private String takeoutCityCode = null;

  /**
   * &lt;pre&gt;外卖区域名称&lt;/pre&gt;
   */
  @JsonProperty("takeoutRegionName")
  private String takeoutRegionName = null;

  /**
   * &lt;pre&gt;外卖区域编号&lt;/pre&gt;
   */
  @JsonProperty("takeoutRegionCode")
  private String takeoutRegionCode = null;

  /**
   * &lt;pre&gt;外卖主要地址&lt;/pre&gt;
   */
  @JsonProperty("takeoutMainAddress")
  private String takeoutMainAddress = null;

  /**
   * &lt;pre&gt;外卖详细地址&lt;/pre&gt;
   */
  @JsonProperty("takeoutDetailAddress")
  private String takeoutDetailAddress = null;

  /**
   * &lt;pre&gt;外卖地址经度&lt;/pre&gt;
   */
  @JsonProperty("takeoutLon")
  private String takeoutLon = null;

  /**
   * &lt;pre&gt;外卖地址纬度&lt;/pre&gt;
   */
  @JsonProperty("takeoutLat")
  private String takeoutLat = null;

  /**
   * &lt;pre&gt;外卖预计送达时间&lt;/pre&gt;
   */
  @JsonProperty("takeoutDeliveryTime")
  private String takeoutDeliveryTime = null;

  /**
   * &lt;p&gt;千猪系统中创建订单的时间&lt;/p&gt;
   */
  @JsonProperty("createTime")
  private String createTime = null;

  /**
   * &lt;p&gt;千猪系统中订单的最后更新时间&lt;/p&gt;
   */
  @JsonProperty("updateTime")
  private String updateTime = null;

  public QuerKfcOrderDataBeanDTO totalPrice(BigDecimal totalPrice) {
    this.totalPrice = totalPrice;
    return this;
  }

   /**
   * &lt;pre&gt;总价（元）&lt;/pre&gt;
   * @return totalPrice
  **/

  public BigDecimal getTotalPrice() {
    return totalPrice;
  }

  public void setTotalPrice(BigDecimal totalPrice) {
    this.totalPrice = totalPrice;
  }

  public QuerKfcOrderDataBeanDTO marketUnitPrice(BigDecimal marketUnitPrice) {
    this.marketUnitPrice = marketUnitPrice;
    return this;
  }

   /**
   * &lt;pre&gt;市场价（元）&lt;/pre&gt;
   * @return marketUnitPrice
  **/

  public BigDecimal getMarketUnitPrice() {
    return marketUnitPrice;
  }

  public void setMarketUnitPrice(BigDecimal marketUnitPrice) {
    this.marketUnitPrice = marketUnitPrice;
  }

  public QuerKfcOrderDataBeanDTO refundPrice(BigDecimal refundPrice) {
    this.refundPrice = refundPrice;
    return this;
  }

   /**
   * &lt;pre&gt;退款金额（元）&lt;/pre&gt;
   * @return refundPrice
  **/

  public BigDecimal getRefundPrice() {
    return refundPrice;
  }

  public void setRefundPrice(BigDecimal refundPrice) {
    this.refundPrice = refundPrice;
  }

  public QuerKfcOrderDataBeanDTO kfcPlaceOrder(QueryKfcPlaceOrderBean kfcPlaceOrder) {
    this.kfcPlaceOrder = kfcPlaceOrder;
    return this;
  }

   /**
   * Get kfcPlaceOrder
   * @return kfcPlaceOrder
  **/

  public QueryKfcPlaceOrderBean getKfcPlaceOrder() {
    return kfcPlaceOrder;
  }

  public void setKfcPlaceOrder(QueryKfcPlaceOrderBean kfcPlaceOrder) {
    this.kfcPlaceOrder = kfcPlaceOrder;
  }

  public QuerKfcOrderDataBeanDTO unitPrice(BigDecimal unitPrice) {
    this.unitPrice = unitPrice;
    return this;
  }

   /**
   * &lt;pre&gt;单价(元)&lt;/pre&gt;
   * @return unitPrice
  **/

  public BigDecimal getUnitPrice() {
    return unitPrice;
  }

  public void setUnitPrice(BigDecimal unitPrice) {
    this.unitPrice = unitPrice;
  }

  public QuerKfcOrderDataBeanDTO orderNo(String orderNo) {
    this.orderNo = orderNo;
    return this;
  }

   /**
   * &lt;pre&gt;订单号&lt;/pre&gt;
   * @return orderNo
  **/

  public String getOrderNo() {
    return orderNo;
  }

  public void setOrderNo(String orderNo) {
    this.orderNo = orderNo;
  }

  public QuerKfcOrderDataBeanDTO quantity(Integer quantity) {
    this.quantity = quantity;
    return this;
  }

   /**
   * &lt;pre&gt;数量&lt;/pre&gt;
   * @return quantity
  **/

  public Integer getQuantity() {
    return quantity;
  }

  public void setQuantity(Integer quantity) {
    this.quantity = quantity;
  }

  public QuerKfcOrderDataBeanDTO ticket(String ticket) {
    this.ticket = ticket;
    return this;
  }

   /**
   * &lt;pre&gt;取餐码&lt;/pre&gt;
   * @return ticket
  **/

  public String getTicket() {
    return ticket;
  }

  public void setTicket(String ticket) {
    this.ticket = ticket;
  }

  public QuerKfcOrderDataBeanDTO commissionPrice(BigDecimal commissionPrice) {
    this.commissionPrice = commissionPrice;
    return this;
  }

   /**
   * &lt;pre&gt;佣金（元），此字段在交易完成时才会有值&lt;/pre&gt;
   * @return commissionPrice
  **/

  public BigDecimal getCommissionPrice() {
    return commissionPrice;
  }

  public void setCommissionPrice(BigDecimal commissionPrice) {
    this.commissionPrice = commissionPrice;
  }

  public QuerKfcOrderDataBeanDTO userId(Integer userId) {
    this.userId = userId;
    return this;
  }

   /**
   * &lt;pre&gt;用户id&lt;/pre&gt;
   * @return userId
  **/

  public Integer getUserId() {
    return userId;
  }

  public void setUserId(Integer userId) {
    this.userId = userId;
  }

  public QuerKfcOrderDataBeanDTO userRemark(String userRemark) {
    this.userRemark = userRemark;
    return this;
  }

   /**
   * &lt;pre&gt;用户备注&lt;/pre&gt;
   * @return userRemark
  **/

  public String getUserRemark() {
    return userRemark;
  }

  public void setUserRemark(String userRemark) {
    this.userRemark = userRemark;
  }

  public QuerKfcOrderDataBeanDTO userMobile(String userMobile) {
    this.userMobile = userMobile;
    return this;
  }

   /**
   * &lt;pre&gt;用户手机号码&lt;/pre&gt;
   * @return userMobile
  **/

  public String getUserMobile() {
    return userMobile;
  }

  public void setUserMobile(String userMobile) {
    this.userMobile = userMobile;
  }

  public QuerKfcOrderDataBeanDTO cancelTime(String cancelTime) {
    this.cancelTime = cancelTime;
    return this;
  }

   /**
   * &lt;pre&gt;取消时间&lt;/pre&gt;
   * @return cancelTime
  **/

  public String getCancelTime() {
    return cancelTime;
  }

  public void setCancelTime(String cancelTime) {
    this.cancelTime = cancelTime;
  }

  public QuerKfcOrderDataBeanDTO userName(String userName) {
    this.userName = userName;
    return this;
  }

   /**
   * &lt;pre&gt;用户昵称&lt;/pre&gt;
   * @return userName
  **/

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public QuerKfcOrderDataBeanDTO kfcOrderMobileSuffix(String kfcOrderMobileSuffix) {
    this.kfcOrderMobileSuffix = kfcOrderMobileSuffix;
    return this;
  }

   /**
   * &lt;pre&gt;下单手机后4位（非用户手机号）&lt;/pre&gt;
   * @return kfcOrderMobileSuffix
  **/

  public String getKfcOrderMobileSuffix() {
    return kfcOrderMobileSuffix;
  }

  public void setKfcOrderMobileSuffix(String kfcOrderMobileSuffix) {
    this.kfcOrderMobileSuffix = kfcOrderMobileSuffix;
  }

  public QuerKfcOrderDataBeanDTO kfcOrderMobileRemark(String kfcOrderMobileRemark) {
    this.kfcOrderMobileRemark = kfcOrderMobileRemark;
    return this;
  }

   /**
   * &lt;pre&gt;下单手机备注&lt;/pre&gt;
   * @return kfcOrderMobileRemark
  **/

  public String getKfcOrderMobileRemark() {
    return kfcOrderMobileRemark;
  }

  public void setKfcOrderMobileRemark(String kfcOrderMobileRemark) {
    this.kfcOrderMobileRemark = kfcOrderMobileRemark;
  }

  public QuerKfcOrderDataBeanDTO platformUniqueId(String platformUniqueId) {
    this.platformUniqueId = platformUniqueId;
    return this;
  }

   /**
   * &lt;pre&gt;平台用户唯一标识&lt;/pre&gt;
   * @return platformUniqueId
  **/

  public String getPlatformUniqueId() {
    return platformUniqueId;
  }

  public void setPlatformUniqueId(String platformUniqueId) {
    this.platformUniqueId = platformUniqueId;
  }

  public QuerKfcOrderDataBeanDTO takeout(Boolean takeout) {
    this.takeout = takeout;
    return this;
  }

   /**
   * &lt;pre&gt;是否外卖&lt;/pre&gt;
   * @return takeout
  **/

  public Boolean isTakeout() {
    return takeout;
  }

  public void setTakeout(Boolean takeout) {
    this.takeout = takeout;
  }

  public QuerKfcOrderDataBeanDTO takeoutPrice(BigDecimal takeoutPrice) {
    this.takeoutPrice = takeoutPrice;
    return this;
  }

   /**
   * &lt;pre&gt;外送费(元)）&lt;/pre&gt;
   * @return takeoutPrice
  **/

  public BigDecimal getTakeoutPrice() {
    return takeoutPrice;
  }

  public void setTakeoutPrice(BigDecimal takeoutPrice) {
    this.takeoutPrice = takeoutPrice;
  }

  public QuerKfcOrderDataBeanDTO takeoutProvinceName(String takeoutProvinceName) {
    this.takeoutProvinceName = takeoutProvinceName;
    return this;
  }

   /**
   * &lt;pre&gt;外卖省份名称&lt;/pre&gt;
   * @return takeoutProvinceName
  **/

  public String getTakeoutProvinceName() {
    return takeoutProvinceName;
  }

  public void setTakeoutProvinceName(String takeoutProvinceName) {
    this.takeoutProvinceName = takeoutProvinceName;
  }

  public QuerKfcOrderDataBeanDTO takeoutProvinceCode(String takeoutProvinceCode) {
    this.takeoutProvinceCode = takeoutProvinceCode;
    return this;
  }

   /**
   * &lt;pre&gt;外卖省份编号&lt;/pre&gt;
   * @return takeoutProvinceCode
  **/

  public String getTakeoutProvinceCode() {
    return takeoutProvinceCode;
  }

  public void setTakeoutProvinceCode(String takeoutProvinceCode) {
    this.takeoutProvinceCode = takeoutProvinceCode;
  }

  public QuerKfcOrderDataBeanDTO takeoutCityName(String takeoutCityName) {
    this.takeoutCityName = takeoutCityName;
    return this;
  }

   /**
   * &lt;pre&gt;外卖城市名称&lt;/pre&gt;
   * @return takeoutCityName
  **/

  public String getTakeoutCityName() {
    return takeoutCityName;
  }

  public void setTakeoutCityName(String takeoutCityName) {
    this.takeoutCityName = takeoutCityName;
  }

  public QuerKfcOrderDataBeanDTO takeoutCityCode(String takeoutCityCode) {
    this.takeoutCityCode = takeoutCityCode;
    return this;
  }

   /**
   * &lt;pre&gt;外卖城市编号&lt;/pre&gt;
   * @return takeoutCityCode
  **/

  public String getTakeoutCityCode() {
    return takeoutCityCode;
  }

  public void setTakeoutCityCode(String takeoutCityCode) {
    this.takeoutCityCode = takeoutCityCode;
  }

  public QuerKfcOrderDataBeanDTO takeoutRegionName(String takeoutRegionName) {
    this.takeoutRegionName = takeoutRegionName;
    return this;
  }

   /**
   * &lt;pre&gt;外卖区域名称&lt;/pre&gt;
   * @return takeoutRegionName
  **/

  public String getTakeoutRegionName() {
    return takeoutRegionName;
  }

  public void setTakeoutRegionName(String takeoutRegionName) {
    this.takeoutRegionName = takeoutRegionName;
  }

  public QuerKfcOrderDataBeanDTO takeoutRegionCode(String takeoutRegionCode) {
    this.takeoutRegionCode = takeoutRegionCode;
    return this;
  }

   /**
   * &lt;pre&gt;外卖区域编号&lt;/pre&gt;
   * @return takeoutRegionCode
  **/

  public String getTakeoutRegionCode() {
    return takeoutRegionCode;
  }

  public void setTakeoutRegionCode(String takeoutRegionCode) {
    this.takeoutRegionCode = takeoutRegionCode;
  }

  public QuerKfcOrderDataBeanDTO takeoutMainAddress(String takeoutMainAddress) {
    this.takeoutMainAddress = takeoutMainAddress;
    return this;
  }

   /**
   * &lt;pre&gt;外卖主要地址&lt;/pre&gt;
   * @return takeoutMainAddress
  **/

  public String getTakeoutMainAddress() {
    return takeoutMainAddress;
  }

  public void setTakeoutMainAddress(String takeoutMainAddress) {
    this.takeoutMainAddress = takeoutMainAddress;
  }

  public QuerKfcOrderDataBeanDTO takeoutDetailAddress(String takeoutDetailAddress) {
    this.takeoutDetailAddress = takeoutDetailAddress;
    return this;
  }

   /**
   * &lt;pre&gt;外卖详细地址&lt;/pre&gt;
   * @return takeoutDetailAddress
  **/

  public String getTakeoutDetailAddress() {
    return takeoutDetailAddress;
  }

  public void setTakeoutDetailAddress(String takeoutDetailAddress) {
    this.takeoutDetailAddress = takeoutDetailAddress;
  }

  public QuerKfcOrderDataBeanDTO takeoutLon(String takeoutLon) {
    this.takeoutLon = takeoutLon;
    return this;
  }

   /**
   * &lt;pre&gt;外卖地址经度&lt;/pre&gt;
   * @return takeoutLon
  **/

  public String getTakeoutLon() {
    return takeoutLon;
  }

  public void setTakeoutLon(String takeoutLon) {
    this.takeoutLon = takeoutLon;
  }

  public QuerKfcOrderDataBeanDTO takeoutLat(String takeoutLat) {
    this.takeoutLat = takeoutLat;
    return this;
  }

   /**
   * &lt;pre&gt;外卖地址纬度&lt;/pre&gt;
   * @return takeoutLat
  **/

  public String getTakeoutLat() {
    return takeoutLat;
  }

  public void setTakeoutLat(String takeoutLat) {
    this.takeoutLat = takeoutLat;
  }

  public QuerKfcOrderDataBeanDTO takeoutDeliveryTime(String takeoutDeliveryTime) {
    this.takeoutDeliveryTime = takeoutDeliveryTime;
    return this;
  }

   /**
   * &lt;pre&gt;外卖预计送达时间&lt;/pre&gt;
   * @return takeoutDeliveryTime
  **/

  public String getTakeoutDeliveryTime() {
    return takeoutDeliveryTime;
  }

  public void setTakeoutDeliveryTime(String takeoutDeliveryTime) {
    this.takeoutDeliveryTime = takeoutDeliveryTime;
  }

  public QuerKfcOrderDataBeanDTO createTime(String createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * &lt;p&gt;千猪系统中创建订单的时间&lt;/p&gt;
   * @return createTime
  **/

  public String getCreateTime() {
    return createTime;
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public QuerKfcOrderDataBeanDTO updateTime(String updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * &lt;p&gt;千猪系统中订单的最后更新时间&lt;/p&gt;
   * @return updateTime
  **/

  public String getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(String updateTime) {
    this.updateTime = updateTime;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QuerKfcOrderDataBeanDTO querKfcOrderDataBeanDTO = (QuerKfcOrderDataBeanDTO) o;
    return ObjectUtils.equals(this.totalPrice, querKfcOrderDataBeanDTO.totalPrice) &&
    ObjectUtils.equals(this.marketUnitPrice, querKfcOrderDataBeanDTO.marketUnitPrice) &&
    ObjectUtils.equals(this.refundPrice, querKfcOrderDataBeanDTO.refundPrice) &&
    ObjectUtils.equals(this.kfcPlaceOrder, querKfcOrderDataBeanDTO.kfcPlaceOrder) &&
    ObjectUtils.equals(this.unitPrice, querKfcOrderDataBeanDTO.unitPrice) &&
    ObjectUtils.equals(this.orderNo, querKfcOrderDataBeanDTO.orderNo) &&
    ObjectUtils.equals(this.quantity, querKfcOrderDataBeanDTO.quantity) &&
    ObjectUtils.equals(this.ticket, querKfcOrderDataBeanDTO.ticket) &&
    ObjectUtils.equals(this.commissionPrice, querKfcOrderDataBeanDTO.commissionPrice) &&
    ObjectUtils.equals(this.userId, querKfcOrderDataBeanDTO.userId) &&
    ObjectUtils.equals(this.userRemark, querKfcOrderDataBeanDTO.userRemark) &&
    ObjectUtils.equals(this.userMobile, querKfcOrderDataBeanDTO.userMobile) &&
    ObjectUtils.equals(this.cancelTime, querKfcOrderDataBeanDTO.cancelTime) &&
    ObjectUtils.equals(this.userName, querKfcOrderDataBeanDTO.userName) &&
    ObjectUtils.equals(this.kfcOrderMobileSuffix, querKfcOrderDataBeanDTO.kfcOrderMobileSuffix) &&
    ObjectUtils.equals(this.kfcOrderMobileRemark, querKfcOrderDataBeanDTO.kfcOrderMobileRemark) &&
    ObjectUtils.equals(this.platformUniqueId, querKfcOrderDataBeanDTO.platformUniqueId) &&
    ObjectUtils.equals(this.takeout, querKfcOrderDataBeanDTO.takeout) &&
    ObjectUtils.equals(this.takeoutPrice, querKfcOrderDataBeanDTO.takeoutPrice) &&
    ObjectUtils.equals(this.takeoutProvinceName, querKfcOrderDataBeanDTO.takeoutProvinceName) &&
    ObjectUtils.equals(this.takeoutProvinceCode, querKfcOrderDataBeanDTO.takeoutProvinceCode) &&
    ObjectUtils.equals(this.takeoutCityName, querKfcOrderDataBeanDTO.takeoutCityName) &&
    ObjectUtils.equals(this.takeoutCityCode, querKfcOrderDataBeanDTO.takeoutCityCode) &&
    ObjectUtils.equals(this.takeoutRegionName, querKfcOrderDataBeanDTO.takeoutRegionName) &&
    ObjectUtils.equals(this.takeoutRegionCode, querKfcOrderDataBeanDTO.takeoutRegionCode) &&
    ObjectUtils.equals(this.takeoutMainAddress, querKfcOrderDataBeanDTO.takeoutMainAddress) &&
    ObjectUtils.equals(this.takeoutDetailAddress, querKfcOrderDataBeanDTO.takeoutDetailAddress) &&
    ObjectUtils.equals(this.takeoutLon, querKfcOrderDataBeanDTO.takeoutLon) &&
    ObjectUtils.equals(this.takeoutLat, querKfcOrderDataBeanDTO.takeoutLat) &&
    ObjectUtils.equals(this.takeoutDeliveryTime, querKfcOrderDataBeanDTO.takeoutDeliveryTime) &&
    ObjectUtils.equals(this.createTime, querKfcOrderDataBeanDTO.createTime) &&
    ObjectUtils.equals(this.updateTime, querKfcOrderDataBeanDTO.updateTime);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(totalPrice, marketUnitPrice, refundPrice, kfcPlaceOrder, unitPrice, orderNo, quantity, ticket, commissionPrice, userId, userRemark, userMobile, cancelTime, userName, kfcOrderMobileSuffix, kfcOrderMobileRemark, platformUniqueId, takeout, takeoutPrice, takeoutProvinceName, takeoutProvinceCode, takeoutCityName, takeoutCityCode, takeoutRegionName, takeoutRegionCode, takeoutMainAddress, takeoutDetailAddress, takeoutLon, takeoutLat, takeoutDeliveryTime, createTime, updateTime);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QuerKfcOrderDataBeanDTO {\n");
    
    sb.append("    totalPrice: ").append(toIndentedString(totalPrice)).append("\n");
    sb.append("    marketUnitPrice: ").append(toIndentedString(marketUnitPrice)).append("\n");
    sb.append("    refundPrice: ").append(toIndentedString(refundPrice)).append("\n");
    sb.append("    kfcPlaceOrder: ").append(toIndentedString(kfcPlaceOrder)).append("\n");
    sb.append("    unitPrice: ").append(toIndentedString(unitPrice)).append("\n");
    sb.append("    orderNo: ").append(toIndentedString(orderNo)).append("\n");
    sb.append("    quantity: ").append(toIndentedString(quantity)).append("\n");
    sb.append("    ticket: ").append(toIndentedString(ticket)).append("\n");
    sb.append("    commissionPrice: ").append(toIndentedString(commissionPrice)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    userRemark: ").append(toIndentedString(userRemark)).append("\n");
    sb.append("    userMobile: ").append(toIndentedString(userMobile)).append("\n");
    sb.append("    cancelTime: ").append(toIndentedString(cancelTime)).append("\n");
    sb.append("    userName: ").append(toIndentedString(userName)).append("\n");
    sb.append("    kfcOrderMobileSuffix: ").append(toIndentedString(kfcOrderMobileSuffix)).append("\n");
    sb.append("    kfcOrderMobileRemark: ").append(toIndentedString(kfcOrderMobileRemark)).append("\n");
    sb.append("    platformUniqueId: ").append(toIndentedString(platformUniqueId)).append("\n");
    sb.append("    takeout: ").append(toIndentedString(takeout)).append("\n");
    sb.append("    takeoutPrice: ").append(toIndentedString(takeoutPrice)).append("\n");
    sb.append("    takeoutProvinceName: ").append(toIndentedString(takeoutProvinceName)).append("\n");
    sb.append("    takeoutProvinceCode: ").append(toIndentedString(takeoutProvinceCode)).append("\n");
    sb.append("    takeoutCityName: ").append(toIndentedString(takeoutCityName)).append("\n");
    sb.append("    takeoutCityCode: ").append(toIndentedString(takeoutCityCode)).append("\n");
    sb.append("    takeoutRegionName: ").append(toIndentedString(takeoutRegionName)).append("\n");
    sb.append("    takeoutRegionCode: ").append(toIndentedString(takeoutRegionCode)).append("\n");
    sb.append("    takeoutMainAddress: ").append(toIndentedString(takeoutMainAddress)).append("\n");
    sb.append("    takeoutDetailAddress: ").append(toIndentedString(takeoutDetailAddress)).append("\n");
    sb.append("    takeoutLon: ").append(toIndentedString(takeoutLon)).append("\n");
    sb.append("    takeoutLat: ").append(toIndentedString(takeoutLat)).append("\n");
    sb.append("    takeoutDeliveryTime: ").append(toIndentedString(takeoutDeliveryTime)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

