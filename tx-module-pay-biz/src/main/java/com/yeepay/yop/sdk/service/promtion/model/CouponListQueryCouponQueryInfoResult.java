/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.yop.sdk.service.promtion.model.CouponListQueryCouponProductRuleResult;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 */
public class CouponListQueryCouponQueryInfoResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 优惠券码
   */
  @JsonProperty("couponNo")
  private String couponNo = null;

  /**
   * 优惠券名称
   */
  @JsonProperty("couponName")
  private String couponName = null;

  /**
   * 优惠券类型
   */
  @JsonProperty("couponType")
  private String couponType = null;

  /**
   * 最小使用金额
   */
  @JsonProperty("minUseAmount")
  private String minUseAmount = null;

  /**
   * 优惠金额
   */
  @JsonProperty("preferentialAmount")
  private String preferentialAmount = null;

  /**
   * 折扣比例
   */
  @JsonProperty("discountRatio")
  private String discountRatio = null;

  /**
   * 最大优惠金额
   */
  @JsonProperty("maxPreferentialAmount")
  private String maxPreferentialAmount = null;

  /**
   * 支持产品规则
   */
  @JsonProperty("productRule")
  private CouponListQueryCouponProductRuleResult productRule = null;

  /**
   * 申请时间
   */
  @JsonProperty("applyTime")
  private String applyTime = null;

  /**
   * 生效时间
   */
  @JsonProperty("effectTime")
  private String effectTime = null;

  /**
   * 失效时间
   */
  @JsonProperty("expireTme")
  private String expireTme = null;

  /**
   * 优惠券说明
   */
  @JsonProperty("couponRemark")
  private String couponRemark = null;

  /**
   * 优惠券状态
   */
  @JsonProperty("couponStatus")
  private String couponStatus = null;

  public CouponListQueryCouponQueryInfoResult couponNo(String couponNo) {
    this.couponNo = couponNo;
    return this;
  }

   /**
   * 优惠券码
   * @return couponNo
  **/

  public String getCouponNo() {
    return couponNo;
  }

  public void setCouponNo(String couponNo) {
    this.couponNo = couponNo;
  }

  public CouponListQueryCouponQueryInfoResult couponName(String couponName) {
    this.couponName = couponName;
    return this;
  }

   /**
   * 优惠券名称
   * @return couponName
  **/

  public String getCouponName() {
    return couponName;
  }

  public void setCouponName(String couponName) {
    this.couponName = couponName;
  }

  public CouponListQueryCouponQueryInfoResult couponType(String couponType) {
    this.couponType = couponType;
    return this;
  }

   /**
   * 优惠券类型
   * @return couponType
  **/

  public String getCouponType() {
    return couponType;
  }

  public void setCouponType(String couponType) {
    this.couponType = couponType;
  }

  public CouponListQueryCouponQueryInfoResult minUseAmount(String minUseAmount) {
    this.minUseAmount = minUseAmount;
    return this;
  }

   /**
   * 最小使用金额
   * @return minUseAmount
  **/

  public String getMinUseAmount() {
    return minUseAmount;
  }

  public void setMinUseAmount(String minUseAmount) {
    this.minUseAmount = minUseAmount;
  }

  public CouponListQueryCouponQueryInfoResult preferentialAmount(String preferentialAmount) {
    this.preferentialAmount = preferentialAmount;
    return this;
  }

   /**
   * 优惠金额
   * @return preferentialAmount
  **/

  public String getPreferentialAmount() {
    return preferentialAmount;
  }

  public void setPreferentialAmount(String preferentialAmount) {
    this.preferentialAmount = preferentialAmount;
  }

  public CouponListQueryCouponQueryInfoResult discountRatio(String discountRatio) {
    this.discountRatio = discountRatio;
    return this;
  }

   /**
   * 折扣比例
   * @return discountRatio
  **/

  public String getDiscountRatio() {
    return discountRatio;
  }

  public void setDiscountRatio(String discountRatio) {
    this.discountRatio = discountRatio;
  }

  public CouponListQueryCouponQueryInfoResult maxPreferentialAmount(String maxPreferentialAmount) {
    this.maxPreferentialAmount = maxPreferentialAmount;
    return this;
  }

   /**
   * 最大优惠金额
   * @return maxPreferentialAmount
  **/

  public String getMaxPreferentialAmount() {
    return maxPreferentialAmount;
  }

  public void setMaxPreferentialAmount(String maxPreferentialAmount) {
    this.maxPreferentialAmount = maxPreferentialAmount;
  }

  public CouponListQueryCouponQueryInfoResult productRule(CouponListQueryCouponProductRuleResult productRule) {
    this.productRule = productRule;
    return this;
  }

   /**
   * 支持产品规则
   * @return productRule
  **/

  public CouponListQueryCouponProductRuleResult getProductRule() {
    return productRule;
  }

  public void setProductRule(CouponListQueryCouponProductRuleResult productRule) {
    this.productRule = productRule;
  }

  public CouponListQueryCouponQueryInfoResult applyTime(String applyTime) {
    this.applyTime = applyTime;
    return this;
  }

   /**
   * 申请时间
   * @return applyTime
  **/

  public String getApplyTime() {
    return applyTime;
  }

  public void setApplyTime(String applyTime) {
    this.applyTime = applyTime;
  }

  public CouponListQueryCouponQueryInfoResult effectTime(String effectTime) {
    this.effectTime = effectTime;
    return this;
  }

   /**
   * 生效时间
   * @return effectTime
  **/

  public String getEffectTime() {
    return effectTime;
  }

  public void setEffectTime(String effectTime) {
    this.effectTime = effectTime;
  }

  public CouponListQueryCouponQueryInfoResult expireTme(String expireTme) {
    this.expireTme = expireTme;
    return this;
  }

   /**
   * 失效时间
   * @return expireTme
  **/

  public String getExpireTme() {
    return expireTme;
  }

  public void setExpireTme(String expireTme) {
    this.expireTme = expireTme;
  }

  public CouponListQueryCouponQueryInfoResult couponRemark(String couponRemark) {
    this.couponRemark = couponRemark;
    return this;
  }

   /**
   * 优惠券说明
   * @return couponRemark
  **/

  public String getCouponRemark() {
    return couponRemark;
  }

  public void setCouponRemark(String couponRemark) {
    this.couponRemark = couponRemark;
  }

  public CouponListQueryCouponQueryInfoResult couponStatus(String couponStatus) {
    this.couponStatus = couponStatus;
    return this;
  }

   /**
   * 优惠券状态
   * @return couponStatus
  **/

  public String getCouponStatus() {
    return couponStatus;
  }

  public void setCouponStatus(String couponStatus) {
    this.couponStatus = couponStatus;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    CouponListQueryCouponQueryInfoResult couponListQueryCouponQueryInfoResult = (CouponListQueryCouponQueryInfoResult) o;
    return ObjectUtils.equals(this.couponNo, couponListQueryCouponQueryInfoResult.couponNo) &&
    ObjectUtils.equals(this.couponName, couponListQueryCouponQueryInfoResult.couponName) &&
    ObjectUtils.equals(this.couponType, couponListQueryCouponQueryInfoResult.couponType) &&
    ObjectUtils.equals(this.minUseAmount, couponListQueryCouponQueryInfoResult.minUseAmount) &&
    ObjectUtils.equals(this.preferentialAmount, couponListQueryCouponQueryInfoResult.preferentialAmount) &&
    ObjectUtils.equals(this.discountRatio, couponListQueryCouponQueryInfoResult.discountRatio) &&
    ObjectUtils.equals(this.maxPreferentialAmount, couponListQueryCouponQueryInfoResult.maxPreferentialAmount) &&
    ObjectUtils.equals(this.productRule, couponListQueryCouponQueryInfoResult.productRule) &&
    ObjectUtils.equals(this.applyTime, couponListQueryCouponQueryInfoResult.applyTime) &&
    ObjectUtils.equals(this.effectTime, couponListQueryCouponQueryInfoResult.effectTime) &&
    ObjectUtils.equals(this.expireTme, couponListQueryCouponQueryInfoResult.expireTme) &&
    ObjectUtils.equals(this.couponRemark, couponListQueryCouponQueryInfoResult.couponRemark) &&
    ObjectUtils.equals(this.couponStatus, couponListQueryCouponQueryInfoResult.couponStatus);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(couponNo, couponName, couponType, minUseAmount, preferentialAmount, discountRatio, maxPreferentialAmount, productRule, applyTime, effectTime, expireTme, couponRemark, couponStatus);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CouponListQueryCouponQueryInfoResult {\n");
    
    sb.append("    couponNo: ").append(toIndentedString(couponNo)).append("\n");
    sb.append("    couponName: ").append(toIndentedString(couponName)).append("\n");
    sb.append("    couponType: ").append(toIndentedString(couponType)).append("\n");
    sb.append("    minUseAmount: ").append(toIndentedString(minUseAmount)).append("\n");
    sb.append("    preferentialAmount: ").append(toIndentedString(preferentialAmount)).append("\n");
    sb.append("    discountRatio: ").append(toIndentedString(discountRatio)).append("\n");
    sb.append("    maxPreferentialAmount: ").append(toIndentedString(maxPreferentialAmount)).append("\n");
    sb.append("    productRule: ").append(toIndentedString(productRule)).append("\n");
    sb.append("    applyTime: ").append(toIndentedString(applyTime)).append("\n");
    sb.append("    effectTime: ").append(toIndentedString(effectTime)).append("\n");
    sb.append("    expireTme: ").append(toIndentedString(expireTme)).append("\n");
    sb.append("    couponRemark: ").append(toIndentedString(couponRemark)).append("\n");
    sb.append("    couponStatus: ").append(toIndentedString(couponStatus)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

