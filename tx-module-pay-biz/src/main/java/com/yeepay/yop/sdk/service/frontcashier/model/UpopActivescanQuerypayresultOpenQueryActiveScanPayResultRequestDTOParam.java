/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 方法签名第0个参数，请自行修改arg0等参数的名字
 */
public class UpopActivescanQuerypayresultOpenQueryActiveScanPayResultRequestDTOParam implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 
   */
  @JsonProperty("merchanNo")
  private String merchanNo = null;

  /**
   * 
   */
  @JsonProperty("merchantFlowId")
  private String merchantFlowId = null;

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultRequestDTOParam merchanNo(String merchanNo) {
    this.merchanNo = merchanNo;
    return this;
  }

   /**
   * Get merchanNo
   * @return merchanNo
  **/

  public String getMerchanNo() {
    return merchanNo;
  }

  public void setMerchanNo(String merchanNo) {
    this.merchanNo = merchanNo;
  }

  public UpopActivescanQuerypayresultOpenQueryActiveScanPayResultRequestDTOParam merchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
    return this;
  }

   /**
   * Get merchantFlowId
   * @return merchantFlowId
  **/

  public String getMerchantFlowId() {
    return merchantFlowId;
  }

  public void setMerchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    UpopActivescanQuerypayresultOpenQueryActiveScanPayResultRequestDTOParam upopActivescanQuerypayresultOpenQueryActiveScanPayResultRequestDTOParam = (UpopActivescanQuerypayresultOpenQueryActiveScanPayResultRequestDTOParam) o;
    return ObjectUtils.equals(this.merchanNo, upopActivescanQuerypayresultOpenQueryActiveScanPayResultRequestDTOParam.merchanNo) &&
    ObjectUtils.equals(this.merchantFlowId, upopActivescanQuerypayresultOpenQueryActiveScanPayResultRequestDTOParam.merchantFlowId);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(merchanNo, merchantFlowId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UpopActivescanQuerypayresultOpenQueryActiveScanPayResultRequestDTOParam {\n");
    
    sb.append("    merchanNo: ").append(toIndentedString(merchanNo)).append("\n");
    sb.append("    merchantFlowId: ").append(toIndentedString(merchantFlowId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

