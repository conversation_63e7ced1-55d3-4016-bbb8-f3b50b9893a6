/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * kfc可选商品信息
 */
public class QueryKfcOptiionItemsBean implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;pre&gt;商品名称&lt;/pre&gt;
   */
  @JsonProperty("productName")
  private String productName = null;

  /**
   * &lt;pre&gt;数量&lt;/pre&gt;
   */
  @JsonProperty("quantity")
  private BigDecimal quantity = null;

  /**
   * &lt;pre&gt;图片地址&lt;/pre&gt;
   */
  @JsonProperty("imageUrl")
  private String imageUrl = null;

  public QueryKfcOptiionItemsBean productName(String productName) {
    this.productName = productName;
    return this;
  }

   /**
   * &lt;pre&gt;商品名称&lt;/pre&gt;
   * @return productName
  **/

  public String getProductName() {
    return productName;
  }

  public void setProductName(String productName) {
    this.productName = productName;
  }

  public QueryKfcOptiionItemsBean quantity(BigDecimal quantity) {
    this.quantity = quantity;
    return this;
  }

   /**
   * &lt;pre&gt;数量&lt;/pre&gt;
   * @return quantity
  **/

  public BigDecimal getQuantity() {
    return quantity;
  }

  public void setQuantity(BigDecimal quantity) {
    this.quantity = quantity;
  }

  public QueryKfcOptiionItemsBean imageUrl(String imageUrl) {
    this.imageUrl = imageUrl;
    return this;
  }

   /**
   * &lt;pre&gt;图片地址&lt;/pre&gt;
   * @return imageUrl
  **/

  public String getImageUrl() {
    return imageUrl;
  }

  public void setImageUrl(String imageUrl) {
    this.imageUrl = imageUrl;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QueryKfcOptiionItemsBean queryKfcOptiionItemsBean = (QueryKfcOptiionItemsBean) o;
    return ObjectUtils.equals(this.productName, queryKfcOptiionItemsBean.productName) &&
    ObjectUtils.equals(this.quantity, queryKfcOptiionItemsBean.quantity) &&
    ObjectUtils.equals(this.imageUrl, queryKfcOptiionItemsBean.imageUrl);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(productName, quantity, imageUrl);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryKfcOptiionItemsBean {\n");
    
    sb.append("    productName: ").append(toIndentedString(productName)).append("\n");
    sb.append("    quantity: ").append(toIndentedString(quantity)).append("\n");
    sb.append("    imageUrl: ").append(toIndentedString(imageUrl)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

