/*
 * API收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.nccashierapi;

import com.yeepay.yop.sdk.client.*;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.HttpResponseAnalyzerSupport;
import com.yeepay.yop.sdk.http.HttpResponseHandler;
import com.yeepay.yop.sdk.http.handler.DefaultHttpResponseHandler;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.service.nccashierapi.request.*;
import com.yeepay.yop.sdk.service.nccashierapi.response.*;

public class NccashierapiClientImpl implements NccashierapiClient {

    private final ClientHandler clientHandler;

    NccashierapiClientImpl(ClientParams clientParams) {
        this.clientHandler = new ClientHandlerImpl(new ClientHandlerParams().withClientParams(clientParams));
    }

    @Override
    public ApiPayResponse apiPay(ApiPayRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<ApiPayRequest> requestMarshaller = ApiPayRequestMarshaller.getInstance();
        HttpResponseHandler<ApiPayResponse> responseHandler =
                new DefaultHttpResponseHandler<ApiPayResponse>(ApiPayResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<ApiPayRequest, ApiPayResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindCardQueryResponse bindCardQuery(BindCardQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindCardQueryRequest> requestMarshaller = BindCardQueryRequestMarshaller.getInstance();
        HttpResponseHandler<BindCardQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<BindCardQueryResponse>(BindCardQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindCardQueryRequest, BindCardQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindCardRequestResponse bindCardRequest(BindCardRequestRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindCardRequestRequest> requestMarshaller = BindCardRequestRequestMarshaller.getInstance();
        HttpResponseHandler<BindCardRequestResponse> responseHandler =
                new DefaultHttpResponseHandler<BindCardRequestResponse>(BindCardRequestResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindCardRequestRequest, BindCardRequestResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardCreateRequestResponse bindcardCreateRequest(BindcardCreateRequestRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardCreateRequestRequest> requestMarshaller = BindcardCreateRequestRequestMarshaller.getInstance();
        HttpResponseHandler<BindcardCreateRequestResponse> responseHandler =
                new DefaultHttpResponseHandler<BindcardCreateRequestResponse>(BindcardCreateRequestResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardCreateRequestRequest, BindcardCreateRequestResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BindcardQueryRequestResponse bindcardQueryRequest(BindcardQueryRequestRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BindcardQueryRequestRequest> requestMarshaller = BindcardQueryRequestRequestMarshaller.getInstance();
        HttpResponseHandler<BindcardQueryRequestResponse> responseHandler =
                new DefaultHttpResponseHandler<BindcardQueryRequestResponse>(BindcardQueryRequestResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BindcardQueryRequestRequest, BindcardQueryRequestResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public OrderCloseResponse orderClose(OrderCloseRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<OrderCloseRequest> requestMarshaller = OrderCloseRequestMarshaller.getInstance();
        HttpResponseHandler<OrderCloseResponse> responseHandler =
                new DefaultHttpResponseHandler<OrderCloseResponse>(OrderCloseResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<OrderCloseRequest, OrderCloseResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }

    @Override
    public void shutdown() {
        clientHandler.shutdown();
    }

}
