/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.travel_resources.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 退款订单查询请求参数
 */
public class QueryRefundOrderRequestDTO implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * &lt;p&gt;请求方商编&lt;/p&gt;
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * &lt;p&gt;业务商户退款请求号&lt;/p&gt;
   */
  @JsonProperty("merchantRefundRequestNo")
  private String merchantRefundRequestNo = null;

  public QueryRefundOrderRequestDTO parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * &lt;p&gt;请求方商编&lt;/p&gt;
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public QueryRefundOrderRequestDTO merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * &lt;p&gt;业务商编&lt;/p&gt;
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public QueryRefundOrderRequestDTO merchantRefundRequestNo(String merchantRefundRequestNo) {
    this.merchantRefundRequestNo = merchantRefundRequestNo;
    return this;
  }

   /**
   * &lt;p&gt;业务商户退款请求号&lt;/p&gt;
   * @return merchantRefundRequestNo
  **/

  public String getMerchantRefundRequestNo() {
    return merchantRefundRequestNo;
  }

  public void setMerchantRefundRequestNo(String merchantRefundRequestNo) {
    this.merchantRefundRequestNo = merchantRefundRequestNo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    QueryRefundOrderRequestDTO queryRefundOrderRequestDTO = (QueryRefundOrderRequestDTO) o;
    return ObjectUtils.equals(this.parentMerchantNo, queryRefundOrderRequestDTO.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, queryRefundOrderRequestDTO.merchantNo) &&
    ObjectUtils.equals(this.merchantRefundRequestNo, queryRefundOrderRequestDTO.merchantRefundRequestNo);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(parentMerchantNo, merchantNo, merchantRefundRequestNo);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryRefundOrderRequestDTO {\n");
    
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantRefundRequestNo: ").append(toIndentedString(merchantRefundRequestNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

