/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.promtion.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class SubsidyBackRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String orderId;

    private String subsidyRequestId;

    private String subsidyBackRequestId;

    private String subsidyBackAmount;

    private String returnAccountType;

    private String memo;

    private String parentMerchantNo;

    private String merchantNo;


    /**
     * Get orderId
     * @return orderId
     **/
    
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * Get subsidyRequestId
     * @return subsidyRequestId
     **/
    
    public String getSubsidyRequestId() {
        return subsidyRequestId;
    }

    public void setSubsidyRequestId(String subsidyRequestId) {
        this.subsidyRequestId = subsidyRequestId;
    }

    /**
     * Get subsidyBackRequestId
     * @return subsidyBackRequestId
     **/
    
    public String getSubsidyBackRequestId() {
        return subsidyBackRequestId;
    }

    public void setSubsidyBackRequestId(String subsidyBackRequestId) {
        this.subsidyBackRequestId = subsidyBackRequestId;
    }

    /**
     * Get subsidyBackAmount
     * @return subsidyBackAmount
     **/
    
    public String getSubsidyBackAmount() {
        return subsidyBackAmount;
    }

    public void setSubsidyBackAmount(String subsidyBackAmount) {
        this.subsidyBackAmount = subsidyBackAmount;
    }

    /**
     * Get returnAccountType
     * @return returnAccountType
     **/
    
    public String getReturnAccountType() {
        return returnAccountType;
    }

    public void setReturnAccountType(String returnAccountType) {
        this.returnAccountType = returnAccountType;
    }

    /**
     * Get memo
     * @return memo
     **/
    
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    /**
     * Get parentMerchantNo
     * @return parentMerchantNo
     **/
    
    public String getParentMerchantNo() {
        return parentMerchantNo;
    }

    public void setParentMerchantNo(String parentMerchantNo) {
        this.parentMerchantNo = parentMerchantNo;
    }

    /**
     * Get merchantNo
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    @Override
    public String getOperationId() {
        return "subsidyBack";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
