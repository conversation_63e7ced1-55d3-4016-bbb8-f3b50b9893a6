/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.response;

import com.yeepay.yop.sdk.service.frontcashier.model.BindcardConfirmV2OpenAuthBindCardConfirmResponseDTOResult;

public class BindcardConfirmV2Response extends com.yeepay.yop.sdk.model.BaseResponse {
    private static final long serialVersionUID = 1L;

    private BindcardConfirmV2OpenAuthBindCardConfirmResponseDTOResult result;

    public BindcardConfirmV2OpenAuthBindCardConfirmResponseDTOResult getResult() {
        return result;
    }

    public void setResult(BindcardConfirmV2OpenAuthBindCardConfirmResponseDTOResult result) {
        this.result = result;
    }

}
