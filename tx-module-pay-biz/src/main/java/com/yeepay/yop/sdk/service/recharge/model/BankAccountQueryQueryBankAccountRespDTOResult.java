/*
 * 商户充值
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.recharge.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * BankAccountQueryQueryBankAccountRespDTOResult
 */
public class BankAccountQueryQueryBankAccountRespDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("returnCode")
  private String returnCode = null;

  /**
   * 返回信息
   */
  @JsonProperty("returnMsg")
  private String returnMsg = null;

  /**
   * 商编
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 账户余额
   */
  @JsonProperty("accountAmt")
  private BigDecimal accountAmt = null;

  /**
   * 可用金额
   */
  @JsonProperty("useableAmt")
  private BigDecimal useableAmt = null;

  /**
   * 冻结金额
   */
  @JsonProperty("frozenAmt")
  private BigDecimal frozenAmt = null;

  /**
   * 可用余额（适用于多渠道业务）
   */
  @JsonProperty("availableAmt")
  private BigDecimal availableAmt = null;

  /**
   * 不可用余额（适用于多渠道业务）
   */
  @JsonProperty("unavailableAmt")
  private BigDecimal unavailableAmt = null;

  public BankAccountQueryQueryBankAccountRespDTOResult returnCode(String returnCode) {
    this.returnCode = returnCode;
    return this;
  }

   /**
   * 返回码
   * @return returnCode
  **/

  public String getReturnCode() {
    return returnCode;
  }

  public void setReturnCode(String returnCode) {
    this.returnCode = returnCode;
  }

  public BankAccountQueryQueryBankAccountRespDTOResult returnMsg(String returnMsg) {
    this.returnMsg = returnMsg;
    return this;
  }

   /**
   * 返回信息
   * @return returnMsg
  **/

  public String getReturnMsg() {
    return returnMsg;
  }

  public void setReturnMsg(String returnMsg) {
    this.returnMsg = returnMsg;
  }

  public BankAccountQueryQueryBankAccountRespDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商编
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public BankAccountQueryQueryBankAccountRespDTOResult accountAmt(BigDecimal accountAmt) {
    this.accountAmt = accountAmt;
    return this;
  }

   /**
   * 账户余额
   * @return accountAmt
  **/

  public BigDecimal getAccountAmt() {
    return accountAmt;
  }

  public void setAccountAmt(BigDecimal accountAmt) {
    this.accountAmt = accountAmt;
  }

  public BankAccountQueryQueryBankAccountRespDTOResult useableAmt(BigDecimal useableAmt) {
    this.useableAmt = useableAmt;
    return this;
  }

   /**
   * 可用金额
   * @return useableAmt
  **/

  public BigDecimal getUseableAmt() {
    return useableAmt;
  }

  public void setUseableAmt(BigDecimal useableAmt) {
    this.useableAmt = useableAmt;
  }

  public BankAccountQueryQueryBankAccountRespDTOResult frozenAmt(BigDecimal frozenAmt) {
    this.frozenAmt = frozenAmt;
    return this;
  }

   /**
   * 冻结金额
   * @return frozenAmt
  **/

  public BigDecimal getFrozenAmt() {
    return frozenAmt;
  }

  public void setFrozenAmt(BigDecimal frozenAmt) {
    this.frozenAmt = frozenAmt;
  }

  public BankAccountQueryQueryBankAccountRespDTOResult availableAmt(BigDecimal availableAmt) {
    this.availableAmt = availableAmt;
    return this;
  }

   /**
   * 可用余额（适用于多渠道业务）
   * @return availableAmt
  **/

  public BigDecimal getAvailableAmt() {
    return availableAmt;
  }

  public void setAvailableAmt(BigDecimal availableAmt) {
    this.availableAmt = availableAmt;
  }

  public BankAccountQueryQueryBankAccountRespDTOResult unavailableAmt(BigDecimal unavailableAmt) {
    this.unavailableAmt = unavailableAmt;
    return this;
  }

   /**
   * 不可用余额（适用于多渠道业务）
   * @return unavailableAmt
  **/

  public BigDecimal getUnavailableAmt() {
    return unavailableAmt;
  }

  public void setUnavailableAmt(BigDecimal unavailableAmt) {
    this.unavailableAmt = unavailableAmt;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BankAccountQueryQueryBankAccountRespDTOResult bankAccountQueryQueryBankAccountRespDTOResult = (BankAccountQueryQueryBankAccountRespDTOResult) o;
    return ObjectUtils.equals(this.returnCode, bankAccountQueryQueryBankAccountRespDTOResult.returnCode) &&
    ObjectUtils.equals(this.returnMsg, bankAccountQueryQueryBankAccountRespDTOResult.returnMsg) &&
    ObjectUtils.equals(this.merchantNo, bankAccountQueryQueryBankAccountRespDTOResult.merchantNo) &&
    ObjectUtils.equals(this.accountAmt, bankAccountQueryQueryBankAccountRespDTOResult.accountAmt) &&
    ObjectUtils.equals(this.useableAmt, bankAccountQueryQueryBankAccountRespDTOResult.useableAmt) &&
    ObjectUtils.equals(this.frozenAmt, bankAccountQueryQueryBankAccountRespDTOResult.frozenAmt) &&
    ObjectUtils.equals(this.availableAmt, bankAccountQueryQueryBankAccountRespDTOResult.availableAmt) &&
    ObjectUtils.equals(this.unavailableAmt, bankAccountQueryQueryBankAccountRespDTOResult.unavailableAmt);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(returnCode, returnMsg, merchantNo, accountAmt, useableAmt, frozenAmt, availableAmt, unavailableAmt);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BankAccountQueryQueryBankAccountRespDTOResult {\n");
    
    sb.append("    returnCode: ").append(toIndentedString(returnCode)).append("\n");
    sb.append("    returnMsg: ").append(toIndentedString(returnMsg)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    accountAmt: ").append(toIndentedString(accountAmt)).append("\n");
    sb.append("    useableAmt: ").append(toIndentedString(useableAmt)).append("\n");
    sb.append("    frozenAmt: ").append(toIndentedString(frozenAmt)).append("\n");
    sb.append("    availableAmt: ").append(toIndentedString(availableAmt)).append("\n");
    sb.append("    unavailableAmt: ").append(toIndentedString(unavailableAmt)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

