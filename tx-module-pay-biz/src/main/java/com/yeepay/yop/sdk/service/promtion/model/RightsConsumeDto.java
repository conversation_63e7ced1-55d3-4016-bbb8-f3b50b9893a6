/*
 * 营销
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.promtion.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * 请修改我
 */
public class RightsConsumeDto implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 
   */
  @JsonProperty("consumeTime")
  private String consumeTime = null;

  /**
   * 
   */
  @JsonProperty("brandName")
  private String brandName = null;

  /**
   * 
   */
  @JsonProperty("rightsCode")
  private String rightsCode = null;

  /**
   * 
   */
  @JsonProperty("rightsTitle")
  private String rightsTitle = null;

  /**
   * 
   */
  @JsonProperty("requestNo")
  private String requestNo = null;

  /**
   * 
   */
  @JsonProperty("consumeStatus")
  private String consumeStatus = null;

  public RightsConsumeDto consumeTime(String consumeTime) {
    this.consumeTime = consumeTime;
    return this;
  }

   /**
   * Get consumeTime
   * @return consumeTime
  **/

  public String getConsumeTime() {
    return consumeTime;
  }

  public void setConsumeTime(String consumeTime) {
    this.consumeTime = consumeTime;
  }

  public RightsConsumeDto brandName(String brandName) {
    this.brandName = brandName;
    return this;
  }

   /**
   * Get brandName
   * @return brandName
  **/

  public String getBrandName() {
    return brandName;
  }

  public void setBrandName(String brandName) {
    this.brandName = brandName;
  }

  public RightsConsumeDto rightsCode(String rightsCode) {
    this.rightsCode = rightsCode;
    return this;
  }

   /**
   * Get rightsCode
   * @return rightsCode
  **/

  public String getRightsCode() {
    return rightsCode;
  }

  public void setRightsCode(String rightsCode) {
    this.rightsCode = rightsCode;
  }

  public RightsConsumeDto rightsTitle(String rightsTitle) {
    this.rightsTitle = rightsTitle;
    return this;
  }

   /**
   * Get rightsTitle
   * @return rightsTitle
  **/

  public String getRightsTitle() {
    return rightsTitle;
  }

  public void setRightsTitle(String rightsTitle) {
    this.rightsTitle = rightsTitle;
  }

  public RightsConsumeDto requestNo(String requestNo) {
    this.requestNo = requestNo;
    return this;
  }

   /**
   * Get requestNo
   * @return requestNo
  **/

  public String getRequestNo() {
    return requestNo;
  }

  public void setRequestNo(String requestNo) {
    this.requestNo = requestNo;
  }

  public RightsConsumeDto consumeStatus(String consumeStatus) {
    this.consumeStatus = consumeStatus;
    return this;
  }

   /**
   * Get consumeStatus
   * @return consumeStatus
  **/

  public String getConsumeStatus() {
    return consumeStatus;
  }

  public void setConsumeStatus(String consumeStatus) {
    this.consumeStatus = consumeStatus;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    RightsConsumeDto rightsConsumeDto = (RightsConsumeDto) o;
    return ObjectUtils.equals(this.consumeTime, rightsConsumeDto.consumeTime) &&
    ObjectUtils.equals(this.brandName, rightsConsumeDto.brandName) &&
    ObjectUtils.equals(this.rightsCode, rightsConsumeDto.rightsCode) &&
    ObjectUtils.equals(this.rightsTitle, rightsConsumeDto.rightsTitle) &&
    ObjectUtils.equals(this.requestNo, rightsConsumeDto.requestNo) &&
    ObjectUtils.equals(this.consumeStatus, rightsConsumeDto.consumeStatus);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(consumeTime, brandName, rightsCode, rightsTitle, requestNo, consumeStatus);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RightsConsumeDto {\n");
    
    sb.append("    consumeTime: ").append(toIndentedString(consumeTime)).append("\n");
    sb.append("    brandName: ").append(toIndentedString(brandName)).append("\n");
    sb.append("    rightsCode: ").append(toIndentedString(rightsCode)).append("\n");
    sb.append("    rightsTitle: ").append(toIndentedString(rightsTitle)).append("\n");
    sb.append("    requestNo: ").append(toIndentedString(requestNo)).append("\n");
    sb.append("    consumeStatus: ").append(toIndentedString(consumeStatus)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

