/*
 * 旅游资源
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.travel_resources;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.travel_resources.request.*;
import com.yeepay.yop.sdk.service.travel_resources.response.*;

public interface TravelResourcesClient {

    /**
     * 创建电影票订单
     * 电影票品类下单
     * @return CreateOrderResponse
     * @throws YopClientException if fails to make API call
     */
    CreateOrderResponse createOrder(CreateOrderRequest request) throws YopClientException;

    /**
     * 申请采购订单退款
     * 
     * @return CreateRefundOrderResponse
     * @throws YopClientException if fails to make API call
     */
    CreateRefundOrderResponse createRefundOrder(CreateRefundOrderRequest request) throws YopClientException;

    /**
     * 创建KFC订单
     * 创建KFC订单
     * @return KfcOrderResponse
     * @throws YopClientException if fails to make API call
     */
    KfcOrderResponse kfcOrder(KfcOrderRequest request) throws YopClientException;

    /**
     * 创建KFC订单
     * 创建KFC订单
     * @return KfcOrder0Response
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    KfcOrder0Response kfcOrder_0(KfcOrder0Request request) throws YopClientException;

    /**
     * 查询电影票订单
     * 
     * @return QueryCinemaOrderResponse
     * @throws YopClientException if fails to make API call
     */
    QueryCinemaOrderResponse queryCinemaOrder(QueryCinemaOrderRequest request) throws YopClientException;

    /**
     * 查询KFC订单
     * 
     * @return QueryKfcOrderResponse
     * @throws YopClientException if fails to make API call
     */
    QueryKfcOrderResponse queryKfcOrder(QueryKfcOrderRequest request) throws YopClientException;

    /**
     * 查询电影票订单
     * 
     * @return QueryOrderResponse
     * @throws YopClientException if fails to make API call
     */
    @Deprecated
    QueryOrderResponse queryOrder(QueryOrderRequest request) throws YopClientException;

    /**
     * 查询支付订单信息
     * 查询支付订单信息
     * @return QueryPayOrderResponse
     * @throws YopClientException if fails to make API call
     */
    QueryPayOrderResponse queryPayOrder(QueryPayOrderRequest request) throws YopClientException;

    /**
     * 查询采购退款订单
     * 
     * @return QueryRefundOrderResponse
     * @throws YopClientException if fails to make API call
     */
    QueryRefundOrderResponse queryRefundOrder(QueryRefundOrderRequest request) throws YopClientException;


    /**
     * Shuts down this client object, releasing any resources that might be held open. This is an optional method, and
     * callers are not expected to call it, but can if they want to explicitly release any open resources. Once a client
     * has been shutdown, it should not be used to make any more requests.
     */
    void shutdown();
}
