/*
 * API收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.nccashierapi.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * ApiPayUnifiedAPICashierResponseDTOResult
 */
public class ApiPayUnifiedAPICashierResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 结果编码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 结果信息
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 支付工具
   */
  @JsonProperty("payTool")
  private String payTool = null;

  /**
   * 支付类型
   */
  @JsonProperty("payType")
  private String payType = null;

  /**
   * 商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 订单token
   */
  @JsonProperty("token")
  private String token = null;

  /**
   * 返回结果类型
   */
  @JsonProperty("resultType")
  private String resultType = null;

  /**
   * 返回数据
   */
  @JsonProperty("resultData")
  private String resultData = null;

  /**
   * 支付扩展参数
   */
  @JsonProperty("extParamMap")
  private String extParamMap = null;

  public ApiPayUnifiedAPICashierResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 结果编码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public ApiPayUnifiedAPICashierResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 结果信息
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public ApiPayUnifiedAPICashierResponseDTOResult payTool(String payTool) {
    this.payTool = payTool;
    return this;
  }

   /**
   * 支付工具
   * @return payTool
  **/

  public String getPayTool() {
    return payTool;
  }

  public void setPayTool(String payTool) {
    this.payTool = payTool;
  }

  public ApiPayUnifiedAPICashierResponseDTOResult payType(String payType) {
    this.payType = payType;
    return this;
  }

   /**
   * 支付类型
   * @return payType
  **/

  public String getPayType() {
    return payType;
  }

  public void setPayType(String payType) {
    this.payType = payType;
  }

  public ApiPayUnifiedAPICashierResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public ApiPayUnifiedAPICashierResponseDTOResult token(String token) {
    this.token = token;
    return this;
  }

   /**
   * 订单token
   * @return token
  **/

  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  public ApiPayUnifiedAPICashierResponseDTOResult resultType(String resultType) {
    this.resultType = resultType;
    return this;
  }

   /**
   * 返回结果类型
   * @return resultType
  **/

  public String getResultType() {
    return resultType;
  }

  public void setResultType(String resultType) {
    this.resultType = resultType;
  }

  public ApiPayUnifiedAPICashierResponseDTOResult resultData(String resultData) {
    this.resultData = resultData;
    return this;
  }

   /**
   * 返回数据
   * @return resultData
  **/

  public String getResultData() {
    return resultData;
  }

  public void setResultData(String resultData) {
    this.resultData = resultData;
  }

  public ApiPayUnifiedAPICashierResponseDTOResult extParamMap(String extParamMap) {
    this.extParamMap = extParamMap;
    return this;
  }

   /**
   * 支付扩展参数
   * @return extParamMap
  **/

  public String getExtParamMap() {
    return extParamMap;
  }

  public void setExtParamMap(String extParamMap) {
    this.extParamMap = extParamMap;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    ApiPayUnifiedAPICashierResponseDTOResult apiPayUnifiedAPICashierResponseDTOResult = (ApiPayUnifiedAPICashierResponseDTOResult) o;
    return ObjectUtils.equals(this.code, apiPayUnifiedAPICashierResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, apiPayUnifiedAPICashierResponseDTOResult.message) &&
    ObjectUtils.equals(this.payTool, apiPayUnifiedAPICashierResponseDTOResult.payTool) &&
    ObjectUtils.equals(this.payType, apiPayUnifiedAPICashierResponseDTOResult.payType) &&
    ObjectUtils.equals(this.merchantNo, apiPayUnifiedAPICashierResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.token, apiPayUnifiedAPICashierResponseDTOResult.token) &&
    ObjectUtils.equals(this.resultType, apiPayUnifiedAPICashierResponseDTOResult.resultType) &&
    ObjectUtils.equals(this.resultData, apiPayUnifiedAPICashierResponseDTOResult.resultData) &&
    ObjectUtils.equals(this.extParamMap, apiPayUnifiedAPICashierResponseDTOResult.extParamMap);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, payTool, payType, merchantNo, token, resultType, resultData, extParamMap);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ApiPayUnifiedAPICashierResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    payTool: ").append(toIndentedString(payTool)).append("\n");
    sb.append("    payType: ").append(toIndentedString(payType)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    token: ").append(toIndentedString(token)).append("\n");
    sb.append("    resultType: ").append(toIndentedString(resultType)).append("\n");
    sb.append("    resultData: ").append(toIndentedString(resultData)).append("\n");
    sb.append("    extParamMap: ").append(toIndentedString(extParamMap)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

