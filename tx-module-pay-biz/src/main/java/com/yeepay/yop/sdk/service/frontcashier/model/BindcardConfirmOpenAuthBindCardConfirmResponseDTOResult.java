/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult
 */
public class BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("message")
  private String message = null;

  /**
   * 主商户编号
   */
  @JsonProperty("parentMerchantNo")
  private String parentMerchantNo = null;

  /**
   * 商户编号
   */
  @JsonProperty("merchantNo")
  private String merchantNo = null;

  /**
   * 原商户绑卡请求号
   */
  @JsonProperty("merchantFlowId")
  private String merchantFlowId = null;

  /**
   * 易宝绑卡订单号
   */
  @JsonProperty("nopOrderId")
  private String nopOrderId = null;

  /**
   * 绑卡ID
   */
  @JsonProperty("bindId")
  private String bindId = null;

  /**
   * 签约状态
   */
  @JsonProperty("signStatus")
  private String signStatus = null;

  public BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult message(String message) {
    this.message = message;
    return this;
  }

   /**
   * 返回信息
   * @return message
  **/

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult parentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
    return this;
  }

   /**
   * 主商户编号
   * @return parentMerchantNo
  **/

  public String getParentMerchantNo() {
    return parentMerchantNo;
  }

  public void setParentMerchantNo(String parentMerchantNo) {
    this.parentMerchantNo = parentMerchantNo;
  }

  public BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult merchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
    return this;
  }

   /**
   * 商户编号
   * @return merchantNo
  **/

  public String getMerchantNo() {
    return merchantNo;
  }

  public void setMerchantNo(String merchantNo) {
    this.merchantNo = merchantNo;
  }

  public BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult merchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
    return this;
  }

   /**
   * 原商户绑卡请求号
   * @return merchantFlowId
  **/

  public String getMerchantFlowId() {
    return merchantFlowId;
  }

  public void setMerchantFlowId(String merchantFlowId) {
    this.merchantFlowId = merchantFlowId;
  }

  public BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult nopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
    return this;
  }

   /**
   * 易宝绑卡订单号
   * @return nopOrderId
  **/

  public String getNopOrderId() {
    return nopOrderId;
  }

  public void setNopOrderId(String nopOrderId) {
    this.nopOrderId = nopOrderId;
  }

  public BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult bindId(String bindId) {
    this.bindId = bindId;
    return this;
  }

   /**
   * 绑卡ID
   * @return bindId
  **/

  public String getBindId() {
    return bindId;
  }

  public void setBindId(String bindId) {
    this.bindId = bindId;
  }

  public BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult signStatus(String signStatus) {
    this.signStatus = signStatus;
    return this;
  }

   /**
   * 签约状态
   * @return signStatus
  **/

  public String getSignStatus() {
    return signStatus;
  }

  public void setSignStatus(String signStatus) {
    this.signStatus = signStatus;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult bindcardConfirmOpenAuthBindCardConfirmResponseDTOResult = (BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult) o;
    return ObjectUtils.equals(this.code, bindcardConfirmOpenAuthBindCardConfirmResponseDTOResult.code) &&
    ObjectUtils.equals(this.message, bindcardConfirmOpenAuthBindCardConfirmResponseDTOResult.message) &&
    ObjectUtils.equals(this.parentMerchantNo, bindcardConfirmOpenAuthBindCardConfirmResponseDTOResult.parentMerchantNo) &&
    ObjectUtils.equals(this.merchantNo, bindcardConfirmOpenAuthBindCardConfirmResponseDTOResult.merchantNo) &&
    ObjectUtils.equals(this.merchantFlowId, bindcardConfirmOpenAuthBindCardConfirmResponseDTOResult.merchantFlowId) &&
    ObjectUtils.equals(this.nopOrderId, bindcardConfirmOpenAuthBindCardConfirmResponseDTOResult.nopOrderId) &&
    ObjectUtils.equals(this.bindId, bindcardConfirmOpenAuthBindCardConfirmResponseDTOResult.bindId) &&
    ObjectUtils.equals(this.signStatus, bindcardConfirmOpenAuthBindCardConfirmResponseDTOResult.signStatus);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, message, parentMerchantNo, merchantNo, merchantFlowId, nopOrderId, bindId, signStatus);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindcardConfirmOpenAuthBindCardConfirmResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    parentMerchantNo: ").append(toIndentedString(parentMerchantNo)).append("\n");
    sb.append("    merchantNo: ").append(toIndentedString(merchantNo)).append("\n");
    sb.append("    merchantFlowId: ").append(toIndentedString(merchantFlowId)).append("\n");
    sb.append("    nopOrderId: ").append(toIndentedString(nopOrderId)).append("\n");
    sb.append("    bindId: ").append(toIndentedString(bindId)).append("\n");
    sb.append("    signStatus: ").append(toIndentedString(signStatus)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

