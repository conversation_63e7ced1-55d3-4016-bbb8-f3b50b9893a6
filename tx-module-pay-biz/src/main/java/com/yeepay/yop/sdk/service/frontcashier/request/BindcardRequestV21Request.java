/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class BindcardRequestV21Request extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String parentMerchantNo;

    private String merchantNo;

    private String merchantFlowId;

    private String userNo;

    private String userType;

    private String bankCardNo;

    private String userName;

    private String idCardType;

    private String idCardNo;

    private String phone;

    private String cvv2;

    private String validthru;

    private Integer orderValidate;

    private String authType;

    private String cardType;

    private String isSMS;


    /**
     * Get parentMerchantNo
     * @return parentMerchantNo
     **/
    
    public String getParentMerchantNo() {
        return parentMerchantNo;
    }

    public void setParentMerchantNo(String parentMerchantNo) {
        this.parentMerchantNo = parentMerchantNo;
    }

    /**
     * Get merchantNo
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    /**
     * Get merchantFlowId
     * @return merchantFlowId
     **/
    
    public String getMerchantFlowId() {
        return merchantFlowId;
    }

    public void setMerchantFlowId(String merchantFlowId) {
        this.merchantFlowId = merchantFlowId;
    }

    /**
     * Get userNo
     * @return userNo
     **/
    
    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    /**
     * Get userType
     * @return userType
     **/
    
    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    /**
     * Get bankCardNo
     * @return bankCardNo
     **/
    
    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    /**
     * Get userName
     * @return userName
     **/
    
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * Get idCardType
     * @return idCardType
     **/
    
    public String getIdCardType() {
        return idCardType;
    }

    public void setIdCardType(String idCardType) {
        this.idCardType = idCardType;
    }

    /**
     * Get idCardNo
     * @return idCardNo
     **/
    
    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    /**
     * Get phone
     * @return phone
     **/
    
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * Get cvv2
     * @return cvv2
     **/
    
    public String getCvv2() {
        return cvv2;
    }

    public void setCvv2(String cvv2) {
        this.cvv2 = cvv2;
    }

    /**
     * Get validthru
     * @return validthru
     **/
    
    public String getValidthru() {
        return validthru;
    }

    public void setValidthru(String validthru) {
        this.validthru = validthru;
    }

    /**
     * Get orderValidate
     * @return orderValidate
     **/
    
    public Integer getOrderValidate() {
        return orderValidate;
    }

    public void setOrderValidate(Integer orderValidate) {
        this.orderValidate = orderValidate;
    }

    /**
     * Get authType
     * @return authType
     **/
    
    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    /**
     * Get cardType
     * @return cardType
     **/
    
    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    /**
     * Get isSMS
     * @return isSMS
     **/
    
    public String getIsSMS() {
        return isSMS;
    }

    public void setIsSMS(String isSMS) {
        this.isSMS = isSMS;
    }

    @Override
    public String getOperationId() {
        return "bindcardRequestV2_1";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
