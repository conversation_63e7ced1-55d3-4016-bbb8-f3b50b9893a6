/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.frontcashier.request;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class UpopActivescanPayRequest extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String merchantNo;

    private String merchantFlowId;

    private String paySerialNo;

    private String couponSerialNo;

    private BigDecimal tradeAmount;

    private Long bindId;

    private String userNo;

    private String userType;

    private String callBackUrl;

    private String payComments;

    private String deviceID;

    private String deviceType;

    private String accountIDHash;

    private String sourceIP;

    private String usrRgstrDt;

    private String accountEmailLife;

    private String deviceLocation;

    private String fullDeviceNumber;

    private String captureMethod;

    private String deviceSimNumber;

    private String deviceName;


    /**
     * Get merchantNo
     * @return merchantNo
     **/
    
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    /**
     * Get merchantFlowId
     * @return merchantFlowId
     **/
    
    public String getMerchantFlowId() {
        return merchantFlowId;
    }

    public void setMerchantFlowId(String merchantFlowId) {
        this.merchantFlowId = merchantFlowId;
    }

    /**
     * Get paySerialNo
     * @return paySerialNo
     **/
    
    public String getPaySerialNo() {
        return paySerialNo;
    }

    public void setPaySerialNo(String paySerialNo) {
        this.paySerialNo = paySerialNo;
    }

    /**
     * Get couponSerialNo
     * @return couponSerialNo
     **/
    
    public String getCouponSerialNo() {
        return couponSerialNo;
    }

    public void setCouponSerialNo(String couponSerialNo) {
        this.couponSerialNo = couponSerialNo;
    }

    /**
     * Get tradeAmount
     * @return tradeAmount
     **/
    
    public BigDecimal getTradeAmount() {
        return tradeAmount;
    }

    public void setTradeAmount(BigDecimal tradeAmount) {
        this.tradeAmount = tradeAmount;
    }

    /**
     * Get bindId
     * @return bindId
     **/
    
    public Long getBindId() {
        return bindId;
    }

    public void setBindId(Long bindId) {
        this.bindId = bindId;
    }

    /**
     * Get userNo
     * @return userNo
     **/
    
    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    /**
     * Get userType
     * @return userType
     **/
    
    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    /**
     * Get callBackUrl
     * @return callBackUrl
     **/
    
    public String getCallBackUrl() {
        return callBackUrl;
    }

    public void setCallBackUrl(String callBackUrl) {
        this.callBackUrl = callBackUrl;
    }

    /**
     * Get payComments
     * @return payComments
     **/
    
    public String getPayComments() {
        return payComments;
    }

    public void setPayComments(String payComments) {
        this.payComments = payComments;
    }

    /**
     * Get deviceID
     * @return deviceID
     **/
    
    public String getDeviceID() {
        return deviceID;
    }

    public void setDeviceID(String deviceID) {
        this.deviceID = deviceID;
    }

    /**
     * Get deviceType
     * @return deviceType
     **/
    
    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    /**
     * Get accountIDHash
     * @return accountIDHash
     **/
    
    public String getAccountIDHash() {
        return accountIDHash;
    }

    public void setAccountIDHash(String accountIDHash) {
        this.accountIDHash = accountIDHash;
    }

    /**
     * Get sourceIP
     * @return sourceIP
     **/
    
    public String getSourceIP() {
        return sourceIP;
    }

    public void setSourceIP(String sourceIP) {
        this.sourceIP = sourceIP;
    }

    /**
     * Get usrRgstrDt
     * @return usrRgstrDt
     **/
    
    public String getUsrRgstrDt() {
        return usrRgstrDt;
    }

    public void setUsrRgstrDt(String usrRgstrDt) {
        this.usrRgstrDt = usrRgstrDt;
    }

    /**
     * Get accountEmailLife
     * @return accountEmailLife
     **/
    
    public String getAccountEmailLife() {
        return accountEmailLife;
    }

    public void setAccountEmailLife(String accountEmailLife) {
        this.accountEmailLife = accountEmailLife;
    }

    /**
     * Get deviceLocation
     * @return deviceLocation
     **/
    
    public String getDeviceLocation() {
        return deviceLocation;
    }

    public void setDeviceLocation(String deviceLocation) {
        this.deviceLocation = deviceLocation;
    }

    /**
     * Get fullDeviceNumber
     * @return fullDeviceNumber
     **/
    
    public String getFullDeviceNumber() {
        return fullDeviceNumber;
    }

    public void setFullDeviceNumber(String fullDeviceNumber) {
        this.fullDeviceNumber = fullDeviceNumber;
    }

    /**
     * Get captureMethod
     * @return captureMethod
     **/
    
    public String getCaptureMethod() {
        return captureMethod;
    }

    public void setCaptureMethod(String captureMethod) {
        this.captureMethod = captureMethod;
    }

    /**
     * Get deviceSimNumber
     * @return deviceSimNumber
     **/
    
    public String getDeviceSimNumber() {
        return deviceSimNumber;
    }

    public void setDeviceSimNumber(String deviceSimNumber) {
        this.deviceSimNumber = deviceSimNumber;
    }

    /**
     * Get deviceName
     * @return deviceName
     **/
    
    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    @Override
    public String getOperationId() {
        return "upopActivescanPay";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
