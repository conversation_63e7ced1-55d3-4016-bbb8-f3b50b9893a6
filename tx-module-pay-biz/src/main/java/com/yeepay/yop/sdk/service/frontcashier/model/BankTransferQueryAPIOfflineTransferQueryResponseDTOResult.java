/*
 * 前置收银台
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.yeepay.yop.sdk.service.frontcashier.model;

import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * BankTransferQueryAPIOfflineTransferQueryResponseDTOResult
 */
public class BankTransferQueryAPIOfflineTransferQueryResponseDTOResult implements Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 返回码
   */
  @JsonProperty("code")
  private String code = null;

  /**
   * 返回信息
   */
  @JsonProperty("msg")
  private String msg = null;

  /**
   * 收款方名称
   */
  @JsonProperty("receiveName")
  private String receiveName = null;

  /**
   * 收款方账号
   */
  @JsonProperty("receiveAccountNo")
  private String receiveAccountNo = null;

  /**
   * 收款方开户行
   */
  @JsonProperty("accountName")
  private String accountName = null;

  public BankTransferQueryAPIOfflineTransferQueryResponseDTOResult code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 返回码
   * @return code
  **/

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public BankTransferQueryAPIOfflineTransferQueryResponseDTOResult msg(String msg) {
    this.msg = msg;
    return this;
  }

   /**
   * 返回信息
   * @return msg
  **/

  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }

  public BankTransferQueryAPIOfflineTransferQueryResponseDTOResult receiveName(String receiveName) {
    this.receiveName = receiveName;
    return this;
  }

   /**
   * 收款方名称
   * @return receiveName
  **/

  public String getReceiveName() {
    return receiveName;
  }

  public void setReceiveName(String receiveName) {
    this.receiveName = receiveName;
  }

  public BankTransferQueryAPIOfflineTransferQueryResponseDTOResult receiveAccountNo(String receiveAccountNo) {
    this.receiveAccountNo = receiveAccountNo;
    return this;
  }

   /**
   * 收款方账号
   * @return receiveAccountNo
  **/

  public String getReceiveAccountNo() {
    return receiveAccountNo;
  }

  public void setReceiveAccountNo(String receiveAccountNo) {
    this.receiveAccountNo = receiveAccountNo;
  }

  public BankTransferQueryAPIOfflineTransferQueryResponseDTOResult accountName(String accountName) {
    this.accountName = accountName;
    return this;
  }

   /**
   * 收款方开户行
   * @return accountName
  **/

  public String getAccountName() {
    return accountName;
  }

  public void setAccountName(String accountName) {
    this.accountName = accountName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
  if (this == o) {
    return true;
  }
  if (o == null || getClass() != o.getClass()) {
    return false;
  }
    BankTransferQueryAPIOfflineTransferQueryResponseDTOResult bankTransferQueryAPIOfflineTransferQueryResponseDTOResult = (BankTransferQueryAPIOfflineTransferQueryResponseDTOResult) o;
    return ObjectUtils.equals(this.code, bankTransferQueryAPIOfflineTransferQueryResponseDTOResult.code) &&
    ObjectUtils.equals(this.msg, bankTransferQueryAPIOfflineTransferQueryResponseDTOResult.msg) &&
    ObjectUtils.equals(this.receiveName, bankTransferQueryAPIOfflineTransferQueryResponseDTOResult.receiveName) &&
    ObjectUtils.equals(this.receiveAccountNo, bankTransferQueryAPIOfflineTransferQueryResponseDTOResult.receiveAccountNo) &&
    ObjectUtils.equals(this.accountName, bankTransferQueryAPIOfflineTransferQueryResponseDTOResult.accountName);
  }

  @Override
  public int hashCode() {
    return ObjectUtils.hashCodeMulti(code, msg, receiveName, receiveAccountNo, accountName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BankTransferQueryAPIOfflineTransferQueryResponseDTOResult {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    msg: ").append(toIndentedString(msg)).append("\n");
    sb.append("    receiveName: ").append(toIndentedString(receiveName)).append("\n");
    sb.append("    receiveAccountNo: ").append(toIndentedString(receiveAccountNo)).append("\n");
    sb.append("    accountName: ").append(toIndentedString(accountName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private final Map<String, Object> _extParamMap = new HashMap<>();

  @JsonAnySetter
  public Map<String, Object> addParam(String name, Object value) {
    if (null != name && null != value) {
      _extParamMap.put(name, value);
    }
    return _extParamMap;
  }

  @JsonAnyGetter
  public Map<String, Object> get_extParamMap() {
    return _extParamMap;
  }

  @JsonIgnore
  public <T> T get_extParam(String name) {
    return (T) _extParamMap.get(name);
  }

}

