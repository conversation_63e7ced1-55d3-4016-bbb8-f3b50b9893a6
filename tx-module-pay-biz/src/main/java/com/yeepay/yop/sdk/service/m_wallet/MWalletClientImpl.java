/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet;

import com.yeepay.yop.sdk.client.*;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.HttpResponseAnalyzerSupport;
import com.yeepay.yop.sdk.http.HttpResponseHandler;
import com.yeepay.yop.sdk.http.handler.DefaultHttpResponseHandler;
import com.yeepay.yop.sdk.model.transform.RequestMarshaller;
import com.yeepay.yop.sdk.service.m_wallet.request.*;
import com.yeepay.yop.sdk.service.m_wallet.response.*;

public class MWalletClientImpl implements MWalletClient {

    private final ClientHandler clientHandler;

    MWalletClientImpl(ClientParams clientParams) {
        this.clientHandler = new ClientHandlerImpl(new ClientHandlerParams().withClientParams(clientParams));
    }

    @Override
    public AccountFaceCertifyOpenResponse accountFaceCertifyOpen(AccountFaceCertifyOpenRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountFaceCertifyOpenRequest> requestMarshaller = AccountFaceCertifyOpenRequestMarshaller.getInstance();
        HttpResponseHandler<AccountFaceCertifyOpenResponse> responseHandler =
                new DefaultHttpResponseHandler<AccountFaceCertifyOpenResponse>(AccountFaceCertifyOpenResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountFaceCertifyOpenRequest, AccountFaceCertifyOpenResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AccountOpenResponse accountOpen(AccountOpenRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountOpenRequest> requestMarshaller = AccountOpenRequestMarshaller.getInstance();
        HttpResponseHandler<AccountOpenResponse> responseHandler =
                new DefaultHttpResponseHandler<AccountOpenResponse>(AccountOpenResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountOpenRequest, AccountOpenResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AccountOpenNotifyResponse accountOpenNotify(AccountOpenNotifyRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountOpenNotifyRequest> requestMarshaller = AccountOpenNotifyRequestMarshaller.getInstance();
        HttpResponseHandler<AccountOpenNotifyResponse> responseHandler =
                new DefaultHttpResponseHandler<AccountOpenNotifyResponse>(AccountOpenNotifyResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountOpenNotifyRequest, AccountOpenNotifyResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AccountQueryResponse accountQuery(AccountQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountQueryRequest> requestMarshaller = AccountQueryRequestMarshaller.getInstance();
        HttpResponseHandler<AccountQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<AccountQueryResponse>(AccountQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountQueryRequest, AccountQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AccountQueryBalanceResponse accountQueryBalance(AccountQueryBalanceRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountQueryBalanceRequest> requestMarshaller = AccountQueryBalanceRequestMarshaller.getInstance();
        HttpResponseHandler<AccountQueryBalanceResponse> responseHandler =
                new DefaultHttpResponseHandler<AccountQueryBalanceResponse>(AccountQueryBalanceResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountQueryBalanceRequest, AccountQueryBalanceResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AccountQueryQuotaResponse accountQueryQuota(AccountQueryQuotaRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountQueryQuotaRequest> requestMarshaller = AccountQueryQuotaRequestMarshaller.getInstance();
        HttpResponseHandler<AccountQueryQuotaResponse> responseHandler =
                new DefaultHttpResponseHandler<AccountQueryQuotaResponse>(AccountQueryQuotaResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountQueryQuotaRequest, AccountQueryQuotaResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AccountFaceCertifyOpenResponse account_face_certify_open(AccountFaceCertifyOpenRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountFaceCertifyOpenRequest> requestMarshaller = AccountFaceCertifyOpenRequestMarshaller.getInstance();
        HttpResponseHandler<AccountFaceCertifyOpenResponse> responseHandler =
                new DefaultHttpResponseHandler<AccountFaceCertifyOpenResponse>(AccountFaceCertifyOpenResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountFaceCertifyOpenRequest, AccountFaceCertifyOpenResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AccountOpenNotifyResponse account_open_notify(AccountOpenNotifyRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountOpenNotifyRequest> requestMarshaller = AccountOpenNotifyRequestMarshaller.getInstance();
        HttpResponseHandler<AccountOpenNotifyResponse> responseHandler =
                new DefaultHttpResponseHandler<AccountOpenNotifyResponse>(AccountOpenNotifyResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountOpenNotifyRequest, AccountOpenNotifyResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AccountOpenV10Response account_open_v1_0(AccountOpenV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountOpenV10Request> requestMarshaller = AccountOpenV10RequestMarshaller.getInstance();
        HttpResponseHandler<AccountOpenV10Response> responseHandler =
                new DefaultHttpResponseHandler<AccountOpenV10Response>(AccountOpenV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountOpenV10Request, AccountOpenV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AccountQueryBalanceV10Response account_query_balance_v1_0(AccountQueryBalanceV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountQueryBalanceV10Request> requestMarshaller = AccountQueryBalanceV10RequestMarshaller.getInstance();
        HttpResponseHandler<AccountQueryBalanceV10Response> responseHandler =
                new DefaultHttpResponseHandler<AccountQueryBalanceV10Response>(AccountQueryBalanceV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountQueryBalanceV10Request, AccountQueryBalanceV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AccountQueryQuotaResponse account_query_quota(AccountQueryQuotaRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountQueryQuotaRequest> requestMarshaller = AccountQueryQuotaRequestMarshaller.getInstance();
        HttpResponseHandler<AccountQueryQuotaResponse> responseHandler =
                new DefaultHttpResponseHandler<AccountQueryQuotaResponse>(AccountQueryQuotaResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountQueryQuotaRequest, AccountQueryQuotaResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AccountQueryV10Response account_query_v1_0(AccountQueryV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AccountQueryV10Request> requestMarshaller = AccountQueryV10RequestMarshaller.getInstance();
        HttpResponseHandler<AccountQueryV10Response> responseHandler =
                new DefaultHttpResponseHandler<AccountQueryV10Response>(AccountQueryV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AccountQueryV10Request, AccountQueryV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AgreementPaymentCancelResponse agreementPaymentCancel(AgreementPaymentCancelRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AgreementPaymentCancelRequest> requestMarshaller = AgreementPaymentCancelRequestMarshaller.getInstance();
        HttpResponseHandler<AgreementPaymentCancelResponse> responseHandler =
                new DefaultHttpResponseHandler<AgreementPaymentCancelResponse>(AgreementPaymentCancelResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AgreementPaymentCancelRequest, AgreementPaymentCancelResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AgreementPaymentQueryResponse agreementPaymentQuery(AgreementPaymentQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AgreementPaymentQueryRequest> requestMarshaller = AgreementPaymentQueryRequestMarshaller.getInstance();
        HttpResponseHandler<AgreementPaymentQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<AgreementPaymentQueryResponse>(AgreementPaymentQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AgreementPaymentQueryRequest, AgreementPaymentQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AgreementPaymentRequestResponse agreementPaymentRequest(AgreementPaymentRequestRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AgreementPaymentRequestRequest> requestMarshaller = AgreementPaymentRequestRequestMarshaller.getInstance();
        HttpResponseHandler<AgreementPaymentRequestResponse> responseHandler =
                new DefaultHttpResponseHandler<AgreementPaymentRequestResponse>(AgreementPaymentRequestResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AgreementPaymentRequestRequest, AgreementPaymentRequestResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AgreementPaymentSignResponse agreementPaymentSign(AgreementPaymentSignRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AgreementPaymentSignRequest> requestMarshaller = AgreementPaymentSignRequestMarshaller.getInstance();
        HttpResponseHandler<AgreementPaymentSignResponse> responseHandler =
                new DefaultHttpResponseHandler<AgreementPaymentSignResponse>(AgreementPaymentSignResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AgreementPaymentSignRequest, AgreementPaymentSignResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AgreementPaymentCancelV10Response agreement_payment_cancel_v1_0(AgreementPaymentCancelV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AgreementPaymentCancelV10Request> requestMarshaller = AgreementPaymentCancelV10RequestMarshaller.getInstance();
        HttpResponseHandler<AgreementPaymentCancelV10Response> responseHandler =
                new DefaultHttpResponseHandler<AgreementPaymentCancelV10Response>(AgreementPaymentCancelV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AgreementPaymentCancelV10Request, AgreementPaymentCancelV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AgreementPaymentCancelWeb3V10Response agreement_payment_cancel_web3_v1_0(AgreementPaymentCancelWeb3V10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AgreementPaymentCancelWeb3V10Request> requestMarshaller = AgreementPaymentCancelWeb3V10RequestMarshaller.getInstance();
        HttpResponseHandler<AgreementPaymentCancelWeb3V10Response> responseHandler =
                new DefaultHttpResponseHandler<AgreementPaymentCancelWeb3V10Response>(AgreementPaymentCancelWeb3V10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AgreementPaymentCancelWeb3V10Request, AgreementPaymentCancelWeb3V10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AgreementPaymentNotifyWeb3Response agreement_payment_notify_web3(AgreementPaymentNotifyWeb3Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AgreementPaymentNotifyWeb3Request> requestMarshaller = AgreementPaymentNotifyWeb3RequestMarshaller.getInstance();
        HttpResponseHandler<AgreementPaymentNotifyWeb3Response> responseHandler =
                new DefaultHttpResponseHandler<AgreementPaymentNotifyWeb3Response>(AgreementPaymentNotifyWeb3Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AgreementPaymentNotifyWeb3Request, AgreementPaymentNotifyWeb3Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AgreementPaymentQueryResponse agreement_payment_query(AgreementPaymentQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AgreementPaymentQueryRequest> requestMarshaller = AgreementPaymentQueryRequestMarshaller.getInstance();
        HttpResponseHandler<AgreementPaymentQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<AgreementPaymentQueryResponse>(AgreementPaymentQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AgreementPaymentQueryRequest, AgreementPaymentQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AgreementPaymentQueryWeb3V10Response agreement_payment_query_web3_v1_0(AgreementPaymentQueryWeb3V10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AgreementPaymentQueryWeb3V10Request> requestMarshaller = AgreementPaymentQueryWeb3V10RequestMarshaller.getInstance();
        HttpResponseHandler<AgreementPaymentQueryWeb3V10Response> responseHandler =
                new DefaultHttpResponseHandler<AgreementPaymentQueryWeb3V10Response>(AgreementPaymentQueryWeb3V10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AgreementPaymentQueryWeb3V10Request, AgreementPaymentQueryWeb3V10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AgreementPaymentRequestV1Response agreement_payment_request_v1(AgreementPaymentRequestV1Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AgreementPaymentRequestV1Request> requestMarshaller = AgreementPaymentRequestV1RequestMarshaller.getInstance();
        HttpResponseHandler<AgreementPaymentRequestV1Response> responseHandler =
                new DefaultHttpResponseHandler<AgreementPaymentRequestV1Response>(AgreementPaymentRequestV1Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AgreementPaymentRequestV1Request, AgreementPaymentRequestV1Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AgreementPaymentSignV10Response agreement_payment_sign_v1_0(AgreementPaymentSignV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AgreementPaymentSignV10Request> requestMarshaller = AgreementPaymentSignV10RequestMarshaller.getInstance();
        HttpResponseHandler<AgreementPaymentSignV10Response> responseHandler =
                new DefaultHttpResponseHandler<AgreementPaymentSignV10Response>(AgreementPaymentSignV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AgreementPaymentSignV10Request, AgreementPaymentSignV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AgreementPaymentSignWeb3V10Response agreement_payment_sign_web3_v1_0(AgreementPaymentSignWeb3V10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AgreementPaymentSignWeb3V10Request> requestMarshaller = AgreementPaymentSignWeb3V10RequestMarshaller.getInstance();
        HttpResponseHandler<AgreementPaymentSignWeb3V10Response> responseHandler =
                new DefaultHttpResponseHandler<AgreementPaymentSignWeb3V10Response>(AgreementPaymentSignWeb3V10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AgreementPaymentSignWeb3V10Request, AgreementPaymentSignWeb3V10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AutoWithdrawResponse autoWithdraw(AutoWithdrawRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AutoWithdrawRequest> requestMarshaller = AutoWithdrawRequestMarshaller.getInstance();
        HttpResponseHandler<AutoWithdrawResponse> responseHandler =
                new DefaultHttpResponseHandler<AutoWithdrawResponse>(AutoWithdrawResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AutoWithdrawRequest, AutoWithdrawResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AutoWithdrawQueryResponse autoWithdrawQuery(AutoWithdrawQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AutoWithdrawQueryRequest> requestMarshaller = AutoWithdrawQueryRequestMarshaller.getInstance();
        HttpResponseHandler<AutoWithdrawQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<AutoWithdrawQueryResponse>(AutoWithdrawQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AutoWithdrawQueryRequest, AutoWithdrawQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AutoDeductionCreateResponse auto_deduction_create(AutoDeductionCreateRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AutoDeductionCreateRequest> requestMarshaller = AutoDeductionCreateRequestMarshaller.getInstance();
        HttpResponseHandler<AutoDeductionCreateResponse> responseHandler =
                new DefaultHttpResponseHandler<AutoDeductionCreateResponse>(AutoDeductionCreateResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AutoDeductionCreateRequest, AutoDeductionCreateResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AutoDeductionQueryResponse auto_deduction_query(AutoDeductionQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AutoDeductionQueryRequest> requestMarshaller = AutoDeductionQueryRequestMarshaller.getInstance();
        HttpResponseHandler<AutoDeductionQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<AutoDeductionQueryResponse>(AutoDeductionQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AutoDeductionQueryRequest, AutoDeductionQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AutoWithdrawResponse auto_withdraw(AutoWithdrawRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AutoWithdrawRequest> requestMarshaller = AutoWithdrawRequestMarshaller.getInstance();
        HttpResponseHandler<AutoWithdrawResponse> responseHandler =
                new DefaultHttpResponseHandler<AutoWithdrawResponse>(AutoWithdrawResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AutoWithdrawRequest, AutoWithdrawResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public AutoWithdrawQueryResponse auto_withdraw_query(AutoWithdrawQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<AutoWithdrawQueryRequest> requestMarshaller = AutoWithdrawQueryRequestMarshaller.getInstance();
        HttpResponseHandler<AutoWithdrawQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<AutoWithdrawQueryResponse>(AutoWithdrawQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<AutoWithdrawQueryRequest, AutoWithdrawQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountConfirmResponse bankAccountConfirm(BankAccountConfirmRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountConfirmRequest> requestMarshaller = BankAccountConfirmRequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountConfirmResponse> responseHandler =
                new DefaultHttpResponseHandler<BankAccountConfirmResponse>(BankAccountConfirmResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountConfirmRequest, BankAccountConfirmResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountOpenResponse bankAccountOpen(BankAccountOpenRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountOpenRequest> requestMarshaller = BankAccountOpenRequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountOpenResponse> responseHandler =
                new DefaultHttpResponseHandler<BankAccountOpenResponse>(BankAccountOpenResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountOpenRequest, BankAccountOpenResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountQueryActivationResponse bankAccountQueryActivation(BankAccountQueryActivationRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountQueryActivationRequest> requestMarshaller = BankAccountQueryActivationRequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountQueryActivationResponse> responseHandler =
                new DefaultHttpResponseHandler<BankAccountQueryActivationResponse>(BankAccountQueryActivationResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountQueryActivationRequest, BankAccountQueryActivationResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountQueryComplaintResponse bankAccountQueryComplaint(BankAccountQueryComplaintRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountQueryComplaintRequest> requestMarshaller = BankAccountQueryComplaintRequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountQueryComplaintResponse> responseHandler =
                new DefaultHttpResponseHandler<BankAccountQueryComplaintResponse>(BankAccountQueryComplaintResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountQueryComplaintRequest, BankAccountQueryComplaintResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountQueryOpenResultResponse bankAccountQueryOpenResult(BankAccountQueryOpenResultRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountQueryOpenResultRequest> requestMarshaller = BankAccountQueryOpenResultRequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountQueryOpenResultResponse> responseHandler =
                new DefaultHttpResponseHandler<BankAccountQueryOpenResultResponse>(BankAccountQueryOpenResultResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountQueryOpenResultRequest, BankAccountQueryOpenResultResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountQueryTradeResponse bankAccountQueryTrade(BankAccountQueryTradeRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountQueryTradeRequest> requestMarshaller = BankAccountQueryTradeRequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountQueryTradeResponse> responseHandler =
                new DefaultHttpResponseHandler<BankAccountQueryTradeResponse>(BankAccountQueryTradeResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountQueryTradeRequest, BankAccountQueryTradeResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountQueryWithdrawResponse bankAccountQueryWithdraw(BankAccountQueryWithdrawRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountQueryWithdrawRequest> requestMarshaller = BankAccountQueryWithdrawRequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountQueryWithdrawResponse> responseHandler =
                new DefaultHttpResponseHandler<BankAccountQueryWithdrawResponse>(BankAccountQueryWithdrawResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountQueryWithdrawRequest, BankAccountQueryWithdrawResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountSendMsgResponse bankAccountSendMsg(BankAccountSendMsgRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountSendMsgRequest> requestMarshaller = BankAccountSendMsgRequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountSendMsgResponse> responseHandler =
                new DefaultHttpResponseHandler<BankAccountSendMsgResponse>(BankAccountSendMsgResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountSendMsgRequest, BankAccountSendMsgResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountUpdateKeyWordsResponse bankAccountUpdateKeyWords(BankAccountUpdateKeyWordsRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountUpdateKeyWordsRequest> requestMarshaller = BankAccountUpdateKeyWordsRequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountUpdateKeyWordsResponse> responseHandler =
                new DefaultHttpResponseHandler<BankAccountUpdateKeyWordsResponse>(BankAccountUpdateKeyWordsResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountUpdateKeyWordsRequest, BankAccountUpdateKeyWordsResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountWithdrawResponse bankAccountWithdraw(BankAccountWithdrawRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountWithdrawRequest> requestMarshaller = BankAccountWithdrawRequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountWithdrawResponse> responseHandler =
                new DefaultHttpResponseHandler<BankAccountWithdrawResponse>(BankAccountWithdrawResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountWithdrawRequest, BankAccountWithdrawResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountConfirmV10Response bank_account_confirm_v1_0(BankAccountConfirmV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountConfirmV10Request> requestMarshaller = BankAccountConfirmV10RequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountConfirmV10Response> responseHandler =
                new DefaultHttpResponseHandler<BankAccountConfirmV10Response>(BankAccountConfirmV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountConfirmV10Request, BankAccountConfirmV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountOpenV10Response bank_account_open_v1_0(BankAccountOpenV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountOpenV10Request> requestMarshaller = BankAccountOpenV10RequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountOpenV10Response> responseHandler =
                new DefaultHttpResponseHandler<BankAccountOpenV10Response>(BankAccountOpenV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountOpenV10Request, BankAccountOpenV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountQueryActivationV10Response bank_account_query_activation_v1_0(BankAccountQueryActivationV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountQueryActivationV10Request> requestMarshaller = BankAccountQueryActivationV10RequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountQueryActivationV10Response> responseHandler =
                new DefaultHttpResponseHandler<BankAccountQueryActivationV10Response>(BankAccountQueryActivationV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountQueryActivationV10Request, BankAccountQueryActivationV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountQueryComplaintV10Response bank_account_query_complaint_v1_0(BankAccountQueryComplaintV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountQueryComplaintV10Request> requestMarshaller = BankAccountQueryComplaintV10RequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountQueryComplaintV10Response> responseHandler =
                new DefaultHttpResponseHandler<BankAccountQueryComplaintV10Response>(BankAccountQueryComplaintV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountQueryComplaintV10Request, BankAccountQueryComplaintV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountQueryOpenResultV10Response bank_account_query_open_result_v1_0(BankAccountQueryOpenResultV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountQueryOpenResultV10Request> requestMarshaller = BankAccountQueryOpenResultV10RequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountQueryOpenResultV10Response> responseHandler =
                new DefaultHttpResponseHandler<BankAccountQueryOpenResultV10Response>(BankAccountQueryOpenResultV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountQueryOpenResultV10Request, BankAccountQueryOpenResultV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountQueryTradeV10Response bank_account_query_trade_v1_0(BankAccountQueryTradeV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountQueryTradeV10Request> requestMarshaller = BankAccountQueryTradeV10RequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountQueryTradeV10Response> responseHandler =
                new DefaultHttpResponseHandler<BankAccountQueryTradeV10Response>(BankAccountQueryTradeV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountQueryTradeV10Request, BankAccountQueryTradeV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountQueryWithdrawV10Response bank_account_query_withdraw_v1_0(BankAccountQueryWithdrawV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountQueryWithdrawV10Request> requestMarshaller = BankAccountQueryWithdrawV10RequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountQueryWithdrawV10Response> responseHandler =
                new DefaultHttpResponseHandler<BankAccountQueryWithdrawV10Response>(BankAccountQueryWithdrawV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountQueryWithdrawV10Request, BankAccountQueryWithdrawV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountSendMsgV10Response bank_account_send_msg_v1_0(BankAccountSendMsgV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountSendMsgV10Request> requestMarshaller = BankAccountSendMsgV10RequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountSendMsgV10Response> responseHandler =
                new DefaultHttpResponseHandler<BankAccountSendMsgV10Response>(BankAccountSendMsgV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountSendMsgV10Request, BankAccountSendMsgV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountUpdateKeyWordsV10Response bank_account_update_key_words_v1_0(BankAccountUpdateKeyWordsV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountUpdateKeyWordsV10Request> requestMarshaller = BankAccountUpdateKeyWordsV10RequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountUpdateKeyWordsV10Response> responseHandler =
                new DefaultHttpResponseHandler<BankAccountUpdateKeyWordsV10Response>(BankAccountUpdateKeyWordsV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountUpdateKeyWordsV10Request, BankAccountUpdateKeyWordsV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BankAccountWithdrawV10Response bank_account_withdraw_v1_0(BankAccountWithdrawV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BankAccountWithdrawV10Request> requestMarshaller = BankAccountWithdrawV10RequestMarshaller.getInstance();
        HttpResponseHandler<BankAccountWithdrawV10Response> responseHandler =
                new DefaultHttpResponseHandler<BankAccountWithdrawV10Response>(BankAccountWithdrawV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BankAccountWithdrawV10Request, BankAccountWithdrawV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BillQueryDetailResponse billQueryDetail(BillQueryDetailRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BillQueryDetailRequest> requestMarshaller = BillQueryDetailRequestMarshaller.getInstance();
        HttpResponseHandler<BillQueryDetailResponse> responseHandler =
                new DefaultHttpResponseHandler<BillQueryDetailResponse>(BillQueryDetailResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BillQueryDetailRequest, BillQueryDetailResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BillQueryListResponse billQueryList(BillQueryListRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BillQueryListRequest> requestMarshaller = BillQueryListRequestMarshaller.getInstance();
        HttpResponseHandler<BillQueryListResponse> responseHandler =
                new DefaultHttpResponseHandler<BillQueryListResponse>(BillQueryListResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BillQueryListRequest, BillQueryListResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BillQueryListV2Response billQueryListV2(BillQueryListV2Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BillQueryListV2Request> requestMarshaller = BillQueryListV2RequestMarshaller.getInstance();
        HttpResponseHandler<BillQueryListV2Response> responseHandler =
                new DefaultHttpResponseHandler<BillQueryListV2Response>(BillQueryListV2Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BillQueryListV2Request, BillQueryListV2Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BillQueryOverviewResponse billQueryOverview(BillQueryOverviewRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BillQueryOverviewRequest> requestMarshaller = BillQueryOverviewRequestMarshaller.getInstance();
        HttpResponseHandler<BillQueryOverviewResponse> responseHandler =
                new DefaultHttpResponseHandler<BillQueryOverviewResponse>(BillQueryOverviewResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BillQueryOverviewRequest, BillQueryOverviewResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BillQueryDetailResponse bill_query_detail(BillQueryDetailRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BillQueryDetailRequest> requestMarshaller = BillQueryDetailRequestMarshaller.getInstance();
        HttpResponseHandler<BillQueryDetailResponse> responseHandler =
                new DefaultHttpResponseHandler<BillQueryDetailResponse>(BillQueryDetailResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BillQueryDetailRequest, BillQueryDetailResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BillQueryListResponse bill_query_list(BillQueryListRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BillQueryListRequest> requestMarshaller = BillQueryListRequestMarshaller.getInstance();
        HttpResponseHandler<BillQueryListResponse> responseHandler =
                new DefaultHttpResponseHandler<BillQueryListResponse>(BillQueryListResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BillQueryListRequest, BillQueryListResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public BillQueryOverviewResponse bill_query_overview(BillQueryOverviewRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<BillQueryOverviewRequest> requestMarshaller = BillQueryOverviewRequestMarshaller.getInstance();
        HttpResponseHandler<BillQueryOverviewResponse> responseHandler =
                new DefaultHttpResponseHandler<BillQueryOverviewResponse>(BillQueryOverviewResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<BillQueryOverviewRequest, BillQueryOverviewResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public CardQueryResponse cardQuery(CardQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<CardQueryRequest> requestMarshaller = CardQueryRequestMarshaller.getInstance();
        HttpResponseHandler<CardQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<CardQueryResponse>(CardQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<CardQueryRequest, CardQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public CardQueryV10Response card_query_v1_0(CardQueryV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<CardQueryV10Request> requestMarshaller = CardQueryV10RequestMarshaller.getInstance();
        HttpResponseHandler<CardQueryV10Response> responseHandler =
                new DefaultHttpResponseHandler<CardQueryV10Response>(CardQueryV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<CardQueryV10Request, CardQueryV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public CouponListQueryWeb3Response coupon_list_query_web3(CouponListQueryWeb3Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<CouponListQueryWeb3Request> requestMarshaller = CouponListQueryWeb3RequestMarshaller.getInstance();
        HttpResponseHandler<CouponListQueryWeb3Response> responseHandler =
                new DefaultHttpResponseHandler<CouponListQueryWeb3Response>(CouponListQueryWeb3Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<CouponListQueryWeb3Request, CouponListQueryWeb3Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public ManageFeeQueryDeductResponse manageFeeQueryDeduct(ManageFeeQueryDeductRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<ManageFeeQueryDeductRequest> requestMarshaller = ManageFeeQueryDeductRequestMarshaller.getInstance();
        HttpResponseHandler<ManageFeeQueryDeductResponse> responseHandler =
                new DefaultHttpResponseHandler<ManageFeeQueryDeductResponse>(ManageFeeQueryDeductResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<ManageFeeQueryDeductRequest, ManageFeeQueryDeductResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public ManageFeeQueryDeductResponse manage_fee_query_deduct(ManageFeeQueryDeductRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<ManageFeeQueryDeductRequest> requestMarshaller = ManageFeeQueryDeductRequestMarshaller.getInstance();
        HttpResponseHandler<ManageFeeQueryDeductResponse> responseHandler =
                new DefaultHttpResponseHandler<ManageFeeQueryDeductResponse>(ManageFeeQueryDeductResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<ManageFeeQueryDeductRequest, ManageFeeQueryDeductResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public MemberCardListResponse memberCardList(MemberCardListRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<MemberCardListRequest> requestMarshaller = MemberCardListRequestMarshaller.getInstance();
        HttpResponseHandler<MemberCardListResponse> responseHandler =
                new DefaultHttpResponseHandler<MemberCardListResponse>(MemberCardListResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<MemberCardListRequest, MemberCardListResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public MemberQueryResponse memberQuery(MemberQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<MemberQueryRequest> requestMarshaller = MemberQueryRequestMarshaller.getInstance();
        HttpResponseHandler<MemberQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<MemberQueryResponse>(MemberQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<MemberQueryRequest, MemberQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public MemberCardListResponse member_card_list(MemberCardListRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<MemberCardListRequest> requestMarshaller = MemberCardListRequestMarshaller.getInstance();
        HttpResponseHandler<MemberCardListResponse> responseHandler =
                new DefaultHttpResponseHandler<MemberCardListResponse>(MemberCardListResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<MemberCardListRequest, MemberCardListResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public MemberQueryV10Response member_query_v1_0(MemberQueryV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<MemberQueryV10Request> requestMarshaller = MemberQueryV10RequestMarshaller.getInstance();
        HttpResponseHandler<MemberQueryV10Response> responseHandler =
                new DefaultHttpResponseHandler<MemberQueryV10Response>(MemberQueryV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<MemberQueryV10Request, MemberQueryV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public PasswordManageResponse passwordManage(PasswordManageRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<PasswordManageRequest> requestMarshaller = PasswordManageRequestMarshaller.getInstance();
        HttpResponseHandler<PasswordManageResponse> responseHandler =
                new DefaultHttpResponseHandler<PasswordManageResponse>(PasswordManageResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<PasswordManageRequest, PasswordManageResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public PasswordManageV10Response password_manage_v1_0(PasswordManageV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<PasswordManageV10Request> requestMarshaller = PasswordManageV10RequestMarshaller.getInstance();
        HttpResponseHandler<PasswordManageV10Response> responseHandler =
                new DefaultHttpResponseHandler<PasswordManageV10Response>(PasswordManageV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<PasswordManageV10Request, PasswordManageV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public PaymentManageWeb3V10Response payment_manage_web3_v1_0(PaymentManageWeb3V10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<PaymentManageWeb3V10Request> requestMarshaller = PaymentManageWeb3V10RequestMarshaller.getInstance();
        HttpResponseHandler<PaymentManageWeb3V10Response> responseHandler =
                new DefaultHttpResponseHandler<PaymentManageWeb3V10Response>(PaymentManageWeb3V10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<PaymentManageWeb3V10Request, PaymentManageWeb3V10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public RechargeInitiateResponse rechargeInitiate(RechargeInitiateRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<RechargeInitiateRequest> requestMarshaller = RechargeInitiateRequestMarshaller.getInstance();
        HttpResponseHandler<RechargeInitiateResponse> responseHandler =
                new DefaultHttpResponseHandler<RechargeInitiateResponse>(RechargeInitiateResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<RechargeInitiateRequest, RechargeInitiateResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public RechargeQueryResponse rechargeQuery(RechargeQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<RechargeQueryRequest> requestMarshaller = RechargeQueryRequestMarshaller.getInstance();
        HttpResponseHandler<RechargeQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<RechargeQueryResponse>(RechargeQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<RechargeQueryRequest, RechargeQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public RechargeInitiateV10Response recharge_initiate_v1_0(RechargeInitiateV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<RechargeInitiateV10Request> requestMarshaller = RechargeInitiateV10RequestMarshaller.getInstance();
        HttpResponseHandler<RechargeInitiateV10Response> responseHandler =
                new DefaultHttpResponseHandler<RechargeInitiateV10Response>(RechargeInitiateV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<RechargeInitiateV10Request, RechargeInitiateV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public RechargeQueryV10Response recharge_query_v1_0(RechargeQueryV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<RechargeQueryV10Request> requestMarshaller = RechargeQueryV10RequestMarshaller.getInstance();
        HttpResponseHandler<RechargeQueryV10Response> responseHandler =
                new DefaultHttpResponseHandler<RechargeQueryV10Response>(RechargeQueryV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<RechargeQueryV10Request, RechargeQueryV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public SubscribeExpireNotifyResponse subscribeExpireNotify(SubscribeExpireNotifyRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<SubscribeExpireNotifyRequest> requestMarshaller = SubscribeExpireNotifyRequestMarshaller.getInstance();
        HttpResponseHandler<SubscribeExpireNotifyResponse> responseHandler =
                new DefaultHttpResponseHandler<SubscribeExpireNotifyResponse>(SubscribeExpireNotifyResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<SubscribeExpireNotifyRequest, SubscribeExpireNotifyResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public SubscribeExpireNotifyResponse subscribe_expire_notify(SubscribeExpireNotifyRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<SubscribeExpireNotifyRequest> requestMarshaller = SubscribeExpireNotifyRequestMarshaller.getInstance();
        HttpResponseHandler<SubscribeExpireNotifyResponse> responseHandler =
                new DefaultHttpResponseHandler<SubscribeExpireNotifyResponse>(SubscribeExpireNotifyResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<SubscribeExpireNotifyRequest, SubscribeExpireNotifyResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public TradeAutoDeductionCreateResponse tradeAutoDeductionCreate(TradeAutoDeductionCreateRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<TradeAutoDeductionCreateRequest> requestMarshaller = TradeAutoDeductionCreateRequestMarshaller.getInstance();
        HttpResponseHandler<TradeAutoDeductionCreateResponse> responseHandler =
                new DefaultHttpResponseHandler<TradeAutoDeductionCreateResponse>(TradeAutoDeductionCreateResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<TradeAutoDeductionCreateRequest, TradeAutoDeductionCreateResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public TradeAutoDeductionQueryResponse tradeAutoDeductionQuery(TradeAutoDeductionQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<TradeAutoDeductionQueryRequest> requestMarshaller = TradeAutoDeductionQueryRequestMarshaller.getInstance();
        HttpResponseHandler<TradeAutoDeductionQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<TradeAutoDeductionQueryResponse>(TradeAutoDeductionQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<TradeAutoDeductionQueryRequest, TradeAutoDeductionQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public TradeOrderResponse tradeOrder(TradeOrderRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<TradeOrderRequest> requestMarshaller = TradeOrderRequestMarshaller.getInstance();
        HttpResponseHandler<TradeOrderResponse> responseHandler =
                new DefaultHttpResponseHandler<TradeOrderResponse>(TradeOrderResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<TradeOrderRequest, TradeOrderResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public TradeOrderV2Response tradeOrderV2(TradeOrderV2Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<TradeOrderV2Request> requestMarshaller = TradeOrderV2RequestMarshaller.getInstance();
        HttpResponseHandler<TradeOrderV2Response> responseHandler =
                new DefaultHttpResponseHandler<TradeOrderV2Response>(TradeOrderV2Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<TradeOrderV2Request, TradeOrderV2Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public TradeOrderV10Response trade_order_v1_0(TradeOrderV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<TradeOrderV10Request> requestMarshaller = TradeOrderV10RequestMarshaller.getInstance();
        HttpResponseHandler<TradeOrderV10Response> responseHandler =
                new DefaultHttpResponseHandler<TradeOrderV10Response>(TradeOrderV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<TradeOrderV10Request, TradeOrderV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public TradeOrderV20Response trade_order_v2_0(TradeOrderV20Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<TradeOrderV20Request> requestMarshaller = TradeOrderV20RequestMarshaller.getInstance();
        HttpResponseHandler<TradeOrderV20Response> responseHandler =
                new DefaultHttpResponseHandler<TradeOrderV20Response>(TradeOrderV20Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<TradeOrderV20Request, TradeOrderV20Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public TransferB2cInitiateResponse transferB2cInitiate(TransferB2cInitiateRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<TransferB2cInitiateRequest> requestMarshaller = TransferB2cInitiateRequestMarshaller.getInstance();
        HttpResponseHandler<TransferB2cInitiateResponse> responseHandler =
                new DefaultHttpResponseHandler<TransferB2cInitiateResponse>(TransferB2cInitiateResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<TransferB2cInitiateRequest, TransferB2cInitiateResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public TransferB2cMarketResponse transferB2cMarket(TransferB2cMarketRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<TransferB2cMarketRequest> requestMarshaller = TransferB2cMarketRequestMarshaller.getInstance();
        HttpResponseHandler<TransferB2cMarketResponse> responseHandler =
                new DefaultHttpResponseHandler<TransferB2cMarketResponse>(TransferB2cMarketResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<TransferB2cMarketRequest, TransferB2cMarketResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public TransferB2cQueryResponse transferB2cQuery(TransferB2cQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<TransferB2cQueryRequest> requestMarshaller = TransferB2cQueryRequestMarshaller.getInstance();
        HttpResponseHandler<TransferB2cQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<TransferB2cQueryResponse>(TransferB2cQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<TransferB2cQueryRequest, TransferB2cQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public TransferB2cInitiateV10Response transfer_b2c_initiate_v1_0(TransferB2cInitiateV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<TransferB2cInitiateV10Request> requestMarshaller = TransferB2cInitiateV10RequestMarshaller.getInstance();
        HttpResponseHandler<TransferB2cInitiateV10Response> responseHandler =
                new DefaultHttpResponseHandler<TransferB2cInitiateV10Response>(TransferB2cInitiateV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<TransferB2cInitiateV10Request, TransferB2cInitiateV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public TransferB2cMarketV10Response transfer_b2c_market_v1_0(TransferB2cMarketV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<TransferB2cMarketV10Request> requestMarshaller = TransferB2cMarketV10RequestMarshaller.getInstance();
        HttpResponseHandler<TransferB2cMarketV10Response> responseHandler =
                new DefaultHttpResponseHandler<TransferB2cMarketV10Response>(TransferB2cMarketV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<TransferB2cMarketV10Request, TransferB2cMarketV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public TransferB2cQueryV10Response transfer_b2c_query_v1_0(TransferB2cQueryV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<TransferB2cQueryV10Request> requestMarshaller = TransferB2cQueryV10RequestMarshaller.getInstance();
        HttpResponseHandler<TransferB2cQueryV10Response> responseHandler =
                new DefaultHttpResponseHandler<TransferB2cQueryV10Response>(TransferB2cQueryV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<TransferB2cQueryV10Request, TransferB2cQueryV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WalletCancelResponse walletCancel(WalletCancelRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WalletCancelRequest> requestMarshaller = WalletCancelRequestMarshaller.getInstance();
        HttpResponseHandler<WalletCancelResponse> responseHandler =
                new DefaultHttpResponseHandler<WalletCancelResponse>(WalletCancelResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WalletCancelRequest, WalletCancelResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WalletIndexV2Response walletIndexV2(WalletIndexV2Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WalletIndexV2Request> requestMarshaller = WalletIndexV2RequestMarshaller.getInstance();
        HttpResponseHandler<WalletIndexV2Response> responseHandler =
                new DefaultHttpResponseHandler<WalletIndexV2Response>(WalletIndexV2Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WalletIndexV2Request, WalletIndexV2Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WalletCancelV10Response wallet_cancel_v1_0(WalletCancelV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WalletCancelV10Request> requestMarshaller = WalletCancelV10RequestMarshaller.getInstance();
        HttpResponseHandler<WalletCancelV10Response> responseHandler =
                new DefaultHttpResponseHandler<WalletCancelV10Response>(WalletCancelV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WalletCancelV10Request, WalletCancelV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WalletIndexV20Response wallet_index_v2_0(WalletIndexV20Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WalletIndexV20Request> requestMarshaller = WalletIndexV20RequestMarshaller.getInstance();
        HttpResponseHandler<WalletIndexV20Response> responseHandler =
                new DefaultHttpResponseHandler<WalletIndexV20Response>(WalletIndexV20Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WalletIndexV20Request, WalletIndexV20Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public Web3AgreementNotifyResponse web3AgreementNotify(Web3AgreementNotifyRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<Web3AgreementNotifyRequest> requestMarshaller = Web3AgreementNotifyRequestMarshaller.getInstance();
        HttpResponseHandler<Web3AgreementNotifyResponse> responseHandler =
                new DefaultHttpResponseHandler<Web3AgreementNotifyResponse>(Web3AgreementNotifyResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<Web3AgreementNotifyRequest, Web3AgreementNotifyResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public Web3AgreementPaymentCancelResponse web3AgreementPaymentCancel(Web3AgreementPaymentCancelRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<Web3AgreementPaymentCancelRequest> requestMarshaller = Web3AgreementPaymentCancelRequestMarshaller.getInstance();
        HttpResponseHandler<Web3AgreementPaymentCancelResponse> responseHandler =
                new DefaultHttpResponseHandler<Web3AgreementPaymentCancelResponse>(Web3AgreementPaymentCancelResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<Web3AgreementPaymentCancelRequest, Web3AgreementPaymentCancelResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public Web3AgreementPaymentQueryResponse web3AgreementPaymentQuery(Web3AgreementPaymentQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<Web3AgreementPaymentQueryRequest> requestMarshaller = Web3AgreementPaymentQueryRequestMarshaller.getInstance();
        HttpResponseHandler<Web3AgreementPaymentQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<Web3AgreementPaymentQueryResponse>(Web3AgreementPaymentQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<Web3AgreementPaymentQueryRequest, Web3AgreementPaymentQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public Web3AgreementPaymentSignResponse web3AgreementPaymentSign(Web3AgreementPaymentSignRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<Web3AgreementPaymentSignRequest> requestMarshaller = Web3AgreementPaymentSignRequestMarshaller.getInstance();
        HttpResponseHandler<Web3AgreementPaymentSignResponse> responseHandler =
                new DefaultHttpResponseHandler<Web3AgreementPaymentSignResponse>(Web3AgreementPaymentSignResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<Web3AgreementPaymentSignRequest, Web3AgreementPaymentSignResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public Web3CouponListQueryResponse web3CouponListQuery(Web3CouponListQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<Web3CouponListQueryRequest> requestMarshaller = Web3CouponListQueryRequestMarshaller.getInstance();
        HttpResponseHandler<Web3CouponListQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<Web3CouponListQueryResponse>(Web3CouponListQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<Web3CouponListQueryRequest, Web3CouponListQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public Web3PaymentManageResponse web3PaymentManage(Web3PaymentManageRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<Web3PaymentManageRequest> requestMarshaller = Web3PaymentManageRequestMarshaller.getInstance();
        HttpResponseHandler<Web3PaymentManageResponse> responseHandler =
                new DefaultHttpResponseHandler<Web3PaymentManageResponse>(Web3PaymentManageResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<Web3PaymentManageRequest, Web3PaymentManageResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WithdrawInitiateResponse withdrawInitiate(WithdrawInitiateRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WithdrawInitiateRequest> requestMarshaller = WithdrawInitiateRequestMarshaller.getInstance();
        HttpResponseHandler<WithdrawInitiateResponse> responseHandler =
                new DefaultHttpResponseHandler<WithdrawInitiateResponse>(WithdrawInitiateResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WithdrawInitiateRequest, WithdrawInitiateResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WithdrawQueryResponse withdrawQuery(WithdrawQueryRequest request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WithdrawQueryRequest> requestMarshaller = WithdrawQueryRequestMarshaller.getInstance();
        HttpResponseHandler<WithdrawQueryResponse> responseHandler =
                new DefaultHttpResponseHandler<WithdrawQueryResponse>(WithdrawQueryResponse.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WithdrawQueryRequest, WithdrawQueryResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WithdrawInitiateV10Response withdraw_initiate_v1_0(WithdrawInitiateV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WithdrawInitiateV10Request> requestMarshaller = WithdrawInitiateV10RequestMarshaller.getInstance();
        HttpResponseHandler<WithdrawInitiateV10Response> responseHandler =
                new DefaultHttpResponseHandler<WithdrawInitiateV10Response>(WithdrawInitiateV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WithdrawInitiateV10Request, WithdrawInitiateV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
    @Override
    public WithdrawQueryV10Response withdraw_query_v1_0(WithdrawQueryV10Request request) throws YopClientException{
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RequestMarshaller<WithdrawQueryV10Request> requestMarshaller = WithdrawQueryV10RequestMarshaller.getInstance();
        HttpResponseHandler<WithdrawQueryV10Response> responseHandler =
                new DefaultHttpResponseHandler<WithdrawQueryV10Response>(WithdrawQueryV10Response.class,
                        HttpResponseAnalyzerSupport.getAnalyzerChain());

        return clientHandler.execute(new ClientExecutionParams<WithdrawQueryV10Request, WithdrawQueryV10Response>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }

    @Override
    public void shutdown() {
        clientHandler.shutdown();
    }

}
