<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.tianxing.cloud</groupId>
        <artifactId>tx-module-pay</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>tx-module-pay-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        pay 模块，我们放支付业务，提供业务的支付能力。
        例如说：商户、应用、支付、退款等等
    </description>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-spring-boot-starter-env</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-module-pay-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <!--        &lt;!&ndash; 基础类包 &ndash;&gt;-->
        <!-- yop-basic-sdk -->
        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk</artifactId>
            <version>4.4.15</version>
        </dependency>

        <!--  下方两个软算法包可以根据需要引入 -->
        <!--  1.国际软算法(RSA) -->
        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk-crypto-inter</artifactId>
            <version>4.4.15</version>
        </dependency>
        <!--  2.商密软算法(SM2) -->
        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk-crypto-gm</artifactId>
            <version>4.4.15</version>
        </dependency>

        <!-- test lib -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <version>1.7.21</version>
            <scope>test</scope>
        </dependency>
        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-spring-boot-starter-biz-pay</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-spring-boot-starter-monitor</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-spring-boot-starter-excel</artifactId>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
