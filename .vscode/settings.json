{"maven.view": "flat", "files.encoding": "utf8", "files.autoGuessEncoding": true, "terminal.integrated.defaultProfile.windows": "Command Prompt", "terminal.integrated.env.windows": {"LANG": "zh_CN.UTF-8"}, "terminal.integrated.shellArgs.windows": ["/K", "chcp 65001"], "terminal.integrated.profiles.windows": {"Command Prompt": {"path": "C:\\Windows\\System32\\cmd.exe", "args": ["/K", "chcp 65001"]}}, "files.associations": {"*.json": "jsonc"}, "workbench.editor.enablePreview": false, "java.jdt.ls.java.home": "", "java.configuration.updateBuildConfiguration": "automatic", "terminal.integrated.fontFamily": "Consolas, 'Microsoft YaHei', monospace", "terminal.integrated.fontSize": 14, "editorjumper.selectedIDE": "IDEA"}