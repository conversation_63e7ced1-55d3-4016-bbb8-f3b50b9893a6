package cn.tianxing.cloud.module.crm.service.caseentrust;

import cn.tianxing.cloud.module.crm.dal.dataobject.caseentrust.CaseEntruDebtorRelationsDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 债务人关联方信息表（含联系人、担保人） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
public interface ICaseEntruDebtorRelationsService extends IService<CaseEntruDebtorRelationsDO> {

    /**
     * 批量新增/修改关联人信息
     *
     * @param debtorRelationsDOList
     */
    void insertOrUpdate(List<CaseEntruDebtorRelationsDO> debtorRelationsDOList);

    /**
     * 获取-联系人/担保人/企业担保信息
     *
     * @param caseEntrustId 委案ID
     * @return
     */
    List<CaseEntruDebtorRelationsDO> getDebtorRelationsList(Long caseEntrustId);

    /**
     * 删除关联人信息
     *
     * @param debtorRelationsIdList 不删除的ID
     * @param caseEntrustId         委案ID
     * @return
     */
    int deleteDebtorRelationsNotIds(List<Long> debtorRelationsIdList, Long caseEntrustId);
}
