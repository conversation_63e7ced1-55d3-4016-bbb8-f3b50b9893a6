package cn.tianxing.cloud.module.crm.service.caseentrust.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.tianxing.cloud.framework.common.exception.ServiceException;
import cn.tianxing.cloud.framework.common.util.object.BeanUtils;
import cn.tianxing.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tianxing.cloud.module.crm.controller.admin.caseentrust.dto.CaseEntruDebtorRelationsSaveDTO;
import cn.tianxing.cloud.module.crm.controller.admin.caseentrust.dto.CaseEntruManualBasicSaveDTO;
import cn.tianxing.cloud.module.crm.controller.admin.caseentrust.dto.CaseEntruManualSaveDTO;
import cn.tianxing.cloud.module.crm.controller.admin.caseentrust.dto.CaseEntrustAttachmentDetailSaveDTO;
import cn.tianxing.cloud.module.crm.controller.admin.caseentrust.vo.CaseEntruManualBasicVo;
import cn.tianxing.cloud.module.crm.controller.admin.caseentrust.vo.CaseEntruManualVo;
import cn.tianxing.cloud.module.crm.dal.dataobject.caseentrust.CaseEntruDebtorRelationsDO;
import cn.tianxing.cloud.module.crm.dal.dataobject.caseentrust.CaseEntrustDO;
import cn.tianxing.cloud.module.crm.dal.dataobject.customer.CustomerCollectDO;
import cn.tianxing.cloud.module.crm.dal.mysql.caseentrust.CaseEntrustMapper;
import cn.tianxing.cloud.module.crm.dal.redis.no.CrmNoRedisDAO;
import cn.tianxing.cloud.module.crm.enums.DictTypeConstants;
import cn.tianxing.cloud.module.crm.enums.ErrorCodeConstants;
import cn.tianxing.cloud.module.crm.service.caseentrust.CaseEntruManualService;
import cn.tianxing.cloud.module.crm.service.caseentrust.ICaseEntruDebtorRelationsService;
import cn.tianxing.cloud.module.crm.service.caseentrust.ICaseEntrustAttachmentDetailService;
import cn.tianxing.cloud.module.crm.service.customer.ICustomerCollectService;
import cn.tianxing.cloud.module.crm.service.phome.IVirtualRealPhoneMappingService;
import cn.tianxing.cloud.module.crm.service.policydistribution.IBusinessStaffScopeDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static cn.tianxing.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.tianxing.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 手动进件 service实现
 */
@Service
public class CaseEntrustManualServiceImpl extends ServiceImpl<CaseEntrustMapper, CaseEntrustDO> implements CaseEntruManualService {

    @Resource
    private ICaseEntruDebtorRelationsService caseEntruDebtorRelationsService;

    @Resource
    private IVirtualRealPhoneMappingService virtualRealPhoneMappingService;

    @Resource
    private ICaseEntrustAttachmentDetailService caseEntrustAttachmentDetailService;

    @Resource
    private ICustomerCollectService customerCollectService;

    @Resource
    private CrmNoRedisDAO crmNoRedisDAO;

    @Resource
    private IBusinessStaffScopeDetailService businessStaffScopeDetailService;

    /**
     * 创建/编辑（委案信息）
     * <p>
     * 功能说明：
     * 1. 校验委案编号是否已存在
     * 2. 保存虚拟号与真实手机号的映射关系
     * 3. 保存委案基本信息
     * 4. 保存委案附件明细信息
     *
     * @param caseEntruManualSaveDTO 委案创建信息，包含：
     *                               - 委案基本信息（entrustBasicDTO）
     *                               - 债务人关系列表（debtorRelationsList）
     * @return 委案ID
     * @throws ServiceException 当委案编号已存在时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAndEditCaseEntrust(CaseEntruManualSaveDTO caseEntruManualSaveDTO) {
        // 1. 获取委案基本信息
        CaseEntruManualBasicSaveDTO entrustBasicDTO = caseEntruManualSaveDTO.getEntrustBasicDTO();
        // 判断债权人和债务人是否为同一个
        if (Objects.equals(entrustBasicDTO.getCreditorId(), entrustBasicDTO.getDebtorId())) {
            throw exception(ErrorCodeConstants.CASEENTRUST_CREDITOR_DEBTOR_SAME, "债权人和债务人不能为同一个");
        }
        String caseNumber = entrustBasicDTO.getCaseNumber();
        if (StringUtils.isEmpty(caseNumber)) {
            caseNumber = crmNoRedisDAO.generateCaseCode();
            entrustBasicDTO.setCaseNumber(caseNumber);
        }

        // 2. 校验委案编号是否已存在
        Long id = entrustBasicDTO.getId();
        if (Objects.isNull(id)) {
            CaseEntrustDO caseEntrustDO = baseMapper.selectOne(new LambdaQueryWrapperX<CaseEntrustDO>().eqIfPresent(CaseEntrustDO::getCaseNumber, caseNumber));
            if (Objects.nonNull(caseEntrustDO)) {
                throw exception(ErrorCodeConstants.CASEENTRUST_NUMBER_EXISTS, caseNumber);
            }
        } else {
            CaseEntrustDO caseEntrustDO = baseMapper.selectById(id);
            if (Objects.isNull(caseEntrustDO)) {
                throw exception(ErrorCodeConstants.CASEENTRUST_NUMBER_NOt_EXISTS, caseNumber);
            }
        }

        // 3. 保存委案基本信息（包含虚拟号码替换）
        Long caseEntrusId = saveCaseEntrustBasicInfo(entrustBasicDTO, caseEntruManualSaveDTO.getEntrustDebtorRelationsList());

        // 4. 保存委案附件明细信息
        caseEntrustAttachmentDetailService.saveAttachmentDetail(caseEntrusId, caseEntruManualSaveDTO.getEntrustAttachmentDetailList());


        return caseEntrusId;
    }

    /**
     * 获取详情（委案信息）
     *
     * @param id 委案ID
     * @return CaseEntruManualVo 包含基础信息及分组后的联系人、担保人、企业担、附件明细保信息
     */
    @Override
    public CaseEntruManualVo getBasicsAffiliateCaseEntrust(Long id) {

        // 1. 创建返回VO对象
        CaseEntruManualVo caseEntruManualVo = new CaseEntruManualVo();

        // 2. 校验委案信息是否存在
        CaseEntrustDO caseEntrustDO = baseMapper.selectById(id);
        if (Objects.isNull(caseEntrustDO)) {
            // 如果委案信息不存在，抛出异常
            throw exception(ErrorCodeConstants.CASEENTRUST_NUMBER_NOt_EXISTS);
        }

        // 3. 组装委案/债权/债务基础信息
        CaseEntruManualBasicVo entruManualBasicVo = BeanUtils.toBean(caseEntrustDO, CaseEntruManualBasicVo.class);
        processCreditorAndDebtorDataForQuery(entruManualBasicVo);
        Long customerCollectId = entruManualBasicVo.getCustomerCollectId();
        if (Objects.nonNull(customerCollectId)) {
            // 如果有客户采集ID，查询客户编号并设置
            CustomerCollectDO customerCollectDO = customerCollectService.getById(customerCollectId);
            if (Objects.nonNull(customerCollectDO)) {
                entruManualBasicVo.setCustomerCollectNum(customerCollectDO.getCustomerNum());
            }
        }
        // 设置基础信息到返回VO
        caseEntruManualVo.setEntruManualBasicVo(entruManualBasicVo);

        // 4. 查询联系人/担保人/企业担保信息
        getAffiliateInfo(entruManualBasicVo.getId(), caseEntruManualVo);

        // 5.查询委案附件信息
        List<CaseEntrustAttachmentDetailSaveDTO> attachmentDetailList = caseEntrustAttachmentDetailService.getAttachmentDetailList(entruManualBasicVo.getId());
        caseEntruManualVo.setEntruAttachmentDetailList(attachmentDetailList);

        // 6. 返回组装好的VO对象
        return caseEntruManualVo;
    }

    /**
     * 查询联系人/担保人/企业担保信息
     *
     * @param id                委案ID
     * @param caseEntruManualVo 委案详情VO
     */
    private void getAffiliateInfo(Long id, CaseEntruManualVo caseEntruManualVo) {
        List<CaseEntruDebtorRelationsDO> debtorRelationsList = caseEntruDebtorRelationsService.getDebtorRelationsList(id);
        if (debtorRelationsList != null && !debtorRelationsList.isEmpty()) {
            // 4.1 按relationType分组，分为联系人、自然担保人、企业担保人三类
            Map<String, List<CaseEntruDebtorRelationsDO>> groupedRelations = debtorRelationsList.stream().collect(Collectors.groupingBy(CaseEntruDebtorRelationsDO::getRelationType));

            // 4.2 遍历分组结果，根据类型组装到VO
            for (Map.Entry<String, List<CaseEntruDebtorRelationsDO>> entry : groupedRelations.entrySet()) {
                String relationType = entry.getKey();
                List<CaseEntruDebtorRelationsDO> relations = entry.getValue();

                // 4.3 将DO对象转换为DTO对象
                List<CaseEntruDebtorRelationsSaveDTO> saveDTOList = relations.stream().map(relation -> {
                    CaseEntruDebtorRelationsSaveDTO saveDTO = new CaseEntruDebtorRelationsSaveDTO();
                    BeanUtils.copyProperties(relation, saveDTO);
                    return saveDTO;
                }).collect(Collectors.toList());

                // 4.4 根据relationType类型，设置到VO的不同属性
                switch (relationType) {
                    case DictTypeConstants.CRM_CONTACT:
                        // 联系人
                        caseEntruManualVo.setEntruDebtorLinkmanList(saveDTOList);
                        break;
                    case DictTypeConstants.CRM_NATURAL_GUARANTOR:
                        // 自然担保人
                        caseEntruManualVo.setEntruDebtorSuretyList(saveDTOList);
                        break;
                    case DictTypeConstants.CRM_CORPORATE_GUARANTOR:
                        // 企业担保人
                        caseEntruManualVo.setEntruDebtorBusinessList(saveDTOList);
                        break;
                }
            }
        }
    }

    /**
     * 保存虚拟号与真实手机号的映射关系
     * <p>
     * 功能说明：
     * 1. 从CaseEntruManualBasicDTO和债务人关系列表中收集所有不为空的真实手机号
     * 2. 为每个真实手机号生成对应的虚拟号
     * 3. 将虚拟号与真实号的映射关系保存到数据库
     * 4. 同时保存债务人关系数据
     * 5. 将原始DTO中的联系电话替换为对应的虚拟号码
     *
     * @param caseEntruManualBasicSaveDTO 案件基本信息DTO，包含案件相关手机号
     * @param debtorRelationsList         债务人关系列表，包含债务人相关手机号
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveCaseEntrustBasicInfo(CaseEntruManualBasicSaveDTO caseEntruManualBasicSaveDTO, List<CaseEntruDebtorRelationsSaveDTO> debtorRelationsList) {
        // 获取当前登录用户ID和时间
        Long currentUserId = getLoginUserId();
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));

        // 批量构建债务人关系对象
        List<CaseEntruDebtorRelationsDO> debtorRelationsDOList = new ArrayList<>();


        // 2.保存委案基本信息
        CaseEntrustDO caseEntrustDO = BeanUtils.toBean(caseEntruManualBasicSaveDTO, CaseEntrustDO.class);
        Long id = caseEntrustDO.getId();
        if (Objects.isNull(id)) {
            caseEntrustDO.setCreator(currentUserId.toString());
        }
        // 逾期阶段计算
        LocalDate overdueTime = caseEntrustDO.getOverdueTime();
        if (Objects.nonNull(overdueTime)) {
            // 结束时间
            String endStr = now.format(DateTimeFormatter.ISO_LOCAL_DATE);
            // 开始时间
            String beginStr = overdueTime.format(DateTimeFormatter.ISO_LOCAL_DATE);
            // 计算赋值
            caseEntrustDO.setCaseStage(caseStageCalculate(beginStr, endStr));
        }

        // 备份存债权和债务数据
        // 设置关联状态为已关联、关联时间
        caseEntrustDO.setCreditorAssociationStatus("2");
        caseEntrustDO.setDebtorAssociationStatus("2");
        caseEntrustDO.setCreditorAssociationTime(now);
        caseEntrustDO.setDebtorAssociationTime(now);
        processCreditorAndDebtorData(caseEntruManualBasicSaveDTO, caseEntrustDO);
        
        // 根据债务人ID查询机构地址，并获取对应的业务员ID
        setBusinessStaffId(caseEntruManualBasicSaveDTO.getDebtorId(), caseEntrustDO);

        caseEntrustDO.setUpdater(currentUserId.toString());

        baseMapper.insertOrUpdate(caseEntrustDO);

        // 2. 处理债务人关系列表中的手机号
        if (!CollectionUtil.isEmpty(debtorRelationsList)) {
            for (CaseEntruDebtorRelationsSaveDTO dto : debtorRelationsList) {
                // 转换并补充债务人关系数据
                CaseEntruDebtorRelationsDO relationsDO = new CaseEntruDebtorRelationsDO();
                BeanUtils.copyProperties(dto, relationsDO);
                relationsDO.setCaseEntrustId(caseEntrustDO.getId());

                // 处理联系电话
                if (StringUtils.hasText(dto.getPhone())) {
                    relationsDO.setPhone(virtualRealPhoneMappingService.getOrCreateVirtualPhone(dto.getPhone()));
                }

                // 设置审计字段
                if (Objects.isNull(dto.getId())) {
                    relationsDO.setCreateTime(now);
                    relationsDO.setCreator(currentUserId.toString());
                }
                relationsDO.setUpdateTime(now);
                relationsDO.setUpdater(currentUserId.toString());
                debtorRelationsDOList.add(relationsDO);
            }
        }

        if (!CollectionUtil.isEmpty(debtorRelationsDOList)) {
            // 新增/修改（关联人）
            caseEntruDebtorRelationsService.insertOrUpdate(debtorRelationsDOList);
            // 删除（关联人）
            List<Long> debtorRelationsIdList = debtorRelationsDOList.stream().map(CaseEntruDebtorRelationsDO::getId).collect(Collectors.toList());
            caseEntruDebtorRelationsService.deleteDebtorRelationsNotIds(debtorRelationsIdList, caseEntrustDO.getId());
        }

        return caseEntrustDO.getId();
    }

    /**
     * 逾期阶段计算
     *
     * @param beginStr 开始时间
     * @param endStr   结束时间
     */
    public String caseStageCalculate(String beginStr, String endStr) {
        LocalDate startDate = LocalDate.parse(beginStr);
        LocalDate endDate = LocalDate.parse(endStr);
        long between = ChronoUnit.DAYS.between(startDate, endDate);
        String caseStage = "";
        if (between <= 2) {
            caseStage = "D1";
        } else if (between >= 3 && between <= 7) {
            caseStage = "S1";
        } else if (between >= 8 && between <= 14) {
            caseStage = "S2";
        } else if (between >= 15 && between <= 30) {
            caseStage = "S3";
        } else if (between >= 31 && between <= 60) {
            caseStage = "M1";
        } else if (between >= 61 && between <= 90) {
            caseStage = "M2";
        } else if (between >= 91 && between <= 120) {
            caseStage = "M3";
        } else if (between >= 121 && between <= 150) {
            caseStage = "M4";
        } else if (between >= 151 && between <= 180) {
            caseStage = "M5";
        } else if (between > 180) {
            caseStage = "M5+";
        }
        return caseStage;
    }

    private void processCreditorAndDebtorData(CaseEntruManualBasicSaveDTO caseEntruManualBasicSaveDTO, CaseEntrustDO caseEntrustDO) {
        // 备份处理债权人数据
        Long creditorId = caseEntruManualBasicSaveDTO.getCreditorId();
        if (Objects.nonNull(creditorId)) {
            // 根据creditorId查询机构数据
            Map<String, Object> creditorMap = baseMapper.selectMechanismById(creditorId);
            if (Objects.nonNull(creditorMap) && !creditorMap.isEmpty()) {
                // 设置债权人相关字段
                caseEntrustDO.setCreditorType((String) creditorMap.get("mechanism_type"));
                caseEntrustDO.setCreditorMemberId((Long) creditorMap.get("member_id"));
                caseEntrustDO.setCreditorIdType((String) creditorMap.get("id_type"));
                caseEntrustDO.setCreditorName((String) creditorMap.get("mechanism_name"));
                caseEntrustDO.setCreditorCreditCode((String) creditorMap.get("credit_code"));
                caseEntrustDO.setCreditorContactPhone((String) creditorMap.get("contact_phone"));
                caseEntrustDO.setCreditorContactTelephone((String) creditorMap.get("contact_telephone"));
                caseEntrustDO.setCreditorEmail((String) creditorMap.get("email"));
                caseEntrustDO.setCreditorProfession((String) creditorMap.get("profession"));
                caseEntrustDO.setCreditorJobTitle((String) creditorMap.get("job_title"));
                caseEntrustDO.setCreditorLegalPerson((String) creditorMap.get("legal_person"));
                caseEntrustDO.setCreditorCompanyName((String) creditorMap.get("company_name"));
                caseEntrustDO.setCreditorLegalPersonPhone((String) creditorMap.get("legal_person_phone"));
                caseEntrustDO.setCreditorLegalPersonIdCard((String) creditorMap.get("legal_person_id_card"));
                caseEntrustDO.setCreditorZhongzhengCode((String) creditorMap.get("zhongzheng_code"));
                caseEntrustDO.setCreditorBankUnionCode((String) creditorMap.get("bank_union_code"));
                caseEntrustDO.setCreditorBusinessLicense((String) creditorMap.get("business_license"));
                caseEntrustDO.setCreditorStatus((Integer) creditorMap.get("status"));
                caseEntrustDO.setCreditorRegisterAddress((String) creditorMap.get("register_address"));
                caseEntrustDO.setCreditorRegisterDetailAddress((String) creditorMap.get("register_detail_address"));
                caseEntrustDO.setCreditorOfficeAddress((String) creditorMap.get("office_address"));
                caseEntrustDO.setCreditorOfficeDetailAddress((String) creditorMap.get("office_detail_address"));
            }
        }

        // 处理债务人数据
        Long debtorId = caseEntruManualBasicSaveDTO.getDebtorId();
        if (Objects.nonNull(debtorId)) {
            // 根据debtorId查询机构数据
            Map<String, Object> debtorMap = baseMapper.selectMechanismById(debtorId);
            if (Objects.nonNull(debtorMap) && !debtorMap.isEmpty()) {
                // 设置债务人相关字段
                caseEntrustDO.setDebtorType((String) debtorMap.get("mechanism_type"));
                caseEntrustDO.setDebtorMemberId((Long) debtorMap.get("member_id"));
                caseEntrustDO.setDebtorIdType((String) debtorMap.get("id_type"));
                caseEntrustDO.setDebtorName((String) debtorMap.get("mechanism_name"));
                caseEntrustDO.setDebtorCreditCode((String) debtorMap.get("credit_code"));
                caseEntrustDO.setDebtorContactPhone((String) debtorMap.get("contact_phone"));
                caseEntrustDO.setDebtorContactTelephone((String) debtorMap.get("contact_telephone"));
                caseEntrustDO.setDebtorEmail((String) debtorMap.get("email"));
                caseEntrustDO.setDebtorProfession((String) debtorMap.get("profession"));
                caseEntrustDO.setDebtorJobTitle((String) debtorMap.get("job_title"));
                caseEntrustDO.setDebtorLegalPerson((String) debtorMap.get("legal_person"));
                caseEntrustDO.setDebtorCompanyName((String) debtorMap.get("company_name"));
                caseEntrustDO.setDebtorLegalPersonPhone((String) debtorMap.get("legal_person_phone"));
                caseEntrustDO.setDebtorLegalPersonIdCard((String) debtorMap.get("legal_person_id_card"));
                caseEntrustDO.setDebtorZhongzhengCode((String) debtorMap.get("zhongzheng_code"));
                caseEntrustDO.setDebtorBankUnionCode((String) debtorMap.get("bank_union_code"));
                caseEntrustDO.setDebtorBusinessLicense((String) debtorMap.get("business_license"));
                caseEntrustDO.setDebtorStatus((Integer) debtorMap.get("status"));
                caseEntrustDO.setDebtorRegisterAddress((String) debtorMap.get("register_address"));
                caseEntrustDO.setDebtorRegisterDetailAddress((String) debtorMap.get("register_detail_address"));
                caseEntrustDO.setDebtorOfficeAddress((String) debtorMap.get("office_address"));
                caseEntrustDO.setDebtorOfficeDetailAddress((String) debtorMap.get("office_detail_address"));
            }
        }

        // 不需要单独更新数据库，因为调用此方法后会执行insertOrUpdate
    }

    private void processCreditorAndDebtorDataForQuery(CaseEntruManualBasicVo entruManualBasicVo) {
        // 判断是否是提交状态    提交状态直接展示case_entrust表的数据，暂存状态要展示mechanism表的数据
        String editingStatus = entruManualBasicVo.getEditingStatus();
        if ("submit".equals(editingStatus)) {
            // 如果是提交状态，直接返回，不进行债权和债务数据处理
            return;
        }

        // 处理债权人数据
        Long creditorId = entruManualBasicVo.getCreditorId();
        if (Objects.nonNull(creditorId)) {
            // 根据creditorId查询机构数据
            Map<String, Object> creditorMap = baseMapper.selectMechanismById(creditorId);
            if (Objects.nonNull(creditorMap) && !creditorMap.isEmpty()) {
                // 设置债权人相关字段
                entruManualBasicVo.setCreditorType((String) creditorMap.get("mechanism_type"));
                entruManualBasicVo.setCreditorMemberId((Long) creditorMap.get("member_id"));
                entruManualBasicVo.setCreditorIdType((String) creditorMap.get("id_type"));
                entruManualBasicVo.setCreditorName((String) creditorMap.get("mechanism_name"));
                entruManualBasicVo.setCreditorCreditCode((String) creditorMap.get("credit_code"));
                entruManualBasicVo.setCreditorContactPhone((String) creditorMap.get("contact_phone"));
                entruManualBasicVo.setCreditorContactTelephone((String) creditorMap.get("contact_telephone"));
                entruManualBasicVo.setCreditorEmail((String) creditorMap.get("email"));
                entruManualBasicVo.setCreditorProfession((String) creditorMap.get("profession"));
                entruManualBasicVo.setCreditorJobTitle((String) creditorMap.get("job_title"));
                entruManualBasicVo.setCreditorLegalPerson((String) creditorMap.get("legal_person"));
                entruManualBasicVo.setCreditorCompanyName((String) creditorMap.get("company_name"));
                entruManualBasicVo.setCreditorLegalPersonPhone((String) creditorMap.get("legal_person_phone"));
                entruManualBasicVo.setCreditorLegalPersonIdCard((String) creditorMap.get("legal_person_id_card"));
                entruManualBasicVo.setCreditorZhongzhengCode((String) creditorMap.get("zhongzheng_code"));
                entruManualBasicVo.setCreditorBankUnionCode((String) creditorMap.get("bank_union_code"));
                entruManualBasicVo.setCreditorBusinessLicense((String) creditorMap.get("business_license"));
                entruManualBasicVo.setCreditorStatus((Integer) creditorMap.get("status"));
                entruManualBasicVo.setCreditorRegisterAddress((String) creditorMap.get("register_address"));
                entruManualBasicVo.setCreditorRegisterDetailAddress((String) creditorMap.get("register_detail_address"));
                entruManualBasicVo.setCreditorOfficeAddress((String) creditorMap.get("office_address"));
                entruManualBasicVo.setCreditorOfficeDetailAddress((String) creditorMap.get("office_detail_address"));
            }
        }

        // 处理债务人数据
        Long debtorId = entruManualBasicVo.getDebtorId();
        if (Objects.nonNull(debtorId)) {
            // 根据debtorId查询机构数据
            Map<String, Object> debtorMap = baseMapper.selectMechanismById(debtorId);
            if (Objects.nonNull(debtorMap) && !debtorMap.isEmpty()) {
                // 设置债务人相关字段
                entruManualBasicVo.setDebtorType((String) debtorMap.get("mechanism_type"));
                entruManualBasicVo.setDebtorMemberId((Long) debtorMap.get("member_id"));
                entruManualBasicVo.setDebtorIdType((String) debtorMap.get("id_type"));
                entruManualBasicVo.setDebtorName((String) debtorMap.get("mechanism_name"));
                entruManualBasicVo.setDebtorCreditCode((String) debtorMap.get("credit_code"));
                entruManualBasicVo.setDebtorContactPhone((String) debtorMap.get("contact_phone"));
                entruManualBasicVo.setDebtorContactTelephone((String) debtorMap.get("contact_telephone"));
                entruManualBasicVo.setDebtorEmail((String) debtorMap.get("email"));
                entruManualBasicVo.setDebtorProfession((String) debtorMap.get("profession"));
                entruManualBasicVo.setDebtorJobTitle((String) debtorMap.get("job_title"));
                entruManualBasicVo.setDebtorLegalPerson((String) debtorMap.get("legal_person"));
                entruManualBasicVo.setDebtorCompanyName((String) debtorMap.get("company_name"));
                entruManualBasicVo.setDebtorLegalPersonPhone((String) debtorMap.get("legal_person_phone"));
                entruManualBasicVo.setDebtorLegalPersonIdCard((String) debtorMap.get("legal_person_id_card"));
                entruManualBasicVo.setDebtorZhongzhengCode((String) debtorMap.get("zhongzheng_code"));
                entruManualBasicVo.setDebtorBankUnionCode((String) debtorMap.get("bank_union_code"));
                entruManualBasicVo.setDebtorBusinessLicense((String) debtorMap.get("business_license"));
                entruManualBasicVo.setDebtorStatus((Integer) debtorMap.get("status"));
                entruManualBasicVo.setDebtorRegisterAddress((String) debtorMap.get("register_address"));
                entruManualBasicVo.setDebtorRegisterDetailAddress((String) debtorMap.get("register_detail_address"));
                entruManualBasicVo.setDebtorOfficeAddress((String) debtorMap.get("office_address"));
                entruManualBasicVo.setDebtorOfficeDetailAddress((String) debtorMap.get("office_detail_address"));
            }
        }
    }

    private void setBusinessStaffId(Long debtorId, CaseEntrustDO caseEntrustDO) {
        // 根据debtorId查询机构地址，并获取对应的业务员ID
        Map<String, Object> debtorMap = baseMapper.selectMechanismById(debtorId);
        if (Objects.nonNull(debtorMap) && !debtorMap.isEmpty()) {
            // 获取办公地址
            String officeAddress = (String) debtorMap.get("office_address");

            // 如果办公地址不为空，则获取业务员ID
            if (StringUtils.hasText(officeAddress)) {
                // 根据办公地址获取业务员ID
                Long businessStaffId = getBusinessStaffId(officeAddress);
                if (businessStaffId != null) {
                    caseEntrustDO.setSalesman(businessStaffId);
                }
            }
        }
    }

    private Long getBusinessStaffId(String officeAddress) {
        // 如果地址为空，则返回null
        if (!StringUtils.hasText(officeAddress)) {
            return null;
        }
        
        try {
            // 将地址字符串转换为地址编码列表
            ArrayList<String> addressCodes = new ArrayList<>();
            
            // 处理JSON格式的地址字符串，如 ["110000","110100","110101"]
            if (officeAddress.startsWith("[") && officeAddress.endsWith("]")) {
                // 去掉首尾的 [ ]
                String content = officeAddress.substring(1, officeAddress.length() - 1);
                // 按照逗号分隔，并去掉每个编码周围的引号
                String[] parts = content.split(",");
                for (String part : parts) {
                    // 去除引号和空白
                    String code = part.trim().replace("\"", "");
                    if (StringUtils.hasText(code)) {
                        addressCodes.add(code);
                    }
                }
            } else {
                // 兼容格式，如 "110000,110100,110101"
                String[] codes = officeAddress.split(",");
                Collections.addAll(addressCodes, codes);
            }
            
            // 调用业务员查询服务
            return businessStaffScopeDetailService.getBusinessStaffId(addressCodes);
        } catch (Exception e) {
            // 记录异常，但不影响主流程
            log.error("获取业务员ID失败", e);
            return null;
        }
    }
}




