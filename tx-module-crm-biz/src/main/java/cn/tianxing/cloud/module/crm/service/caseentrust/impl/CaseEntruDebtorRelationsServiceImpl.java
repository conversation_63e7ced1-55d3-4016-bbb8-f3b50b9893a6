package cn.tianxing.cloud.module.crm.service.caseentrust.impl;

import cn.tianxing.cloud.module.crm.dal.dataobject.caseentrust.CaseEntruDebtorRelationsDO;
import cn.tianxing.cloud.module.crm.dal.mysql.caseentrust.CaseEntruDebtorRelationsMapper;
import cn.tianxing.cloud.module.crm.service.caseentrust.ICaseEntruDebtorRelationsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <p>
 * 债务人关联方信息表（含联系人、担保人） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Service
public class CaseEntruDebtorRelationsServiceImpl extends ServiceImpl<CaseEntruDebtorRelationsMapper, CaseEntruDebtorRelationsDO> implements ICaseEntruDebtorRelationsService {

    /**
     * 批量新增/修改关联人信息
     *
     * @param debtorRelationsDOList
     */
    @Override
    public void insertOrUpdate(List<CaseEntruDebtorRelationsDO> debtorRelationsDOList) {
        baseMapper.insertOrUpdate(debtorRelationsDOList);
    }

    /**
     * 获取-联系人/担保人/企业担保信息
     *
     * @param caseEntrustId 委案ID
     * @return
     */
    @Override
    public List<CaseEntruDebtorRelationsDO> getDebtorRelationsList(Long caseEntrustId) {
        return baseMapper.selectList(caseEntrustId);
    }

    /**
     * 删除关联人信息
     *
     * @param debtorRelationsIdList 不删除的ID
     * @param caseEntrustId
     */
    @Override
    public int deleteDebtorRelationsNotIds(List<Long> debtorRelationsIdList,Long caseEntrustId) {
        return baseMapper.delete(debtorRelationsIdList,caseEntrustId);
    }

}
